plugins {
    id "org.sonarqube"
    id "jacoco"
}

sonar {
    properties {
        property "sonar.projectKey", "premier-store-os_${project.name}"
        property "sonar.organization", "mercaso"
        property "sonar.host.url", "https://sonarcloud.io"
        property "sonar.coverage.jacoco.xmlReportPaths", "build/reports/jacoco/test/jacocoTestReport.xml, build/reports/jacoco/integrationTest/jacocoTestReport.xml"
        def branchName = 'git rev-parse --abbrev-ref HEAD'.execute().text.trim()
        property "sonar.branch.name", branchName
        property "sonar.coverage.exclusions", "**/command/**,**/dto/**,**/mapper/**,**/application/query/**,**/enums/**,**/adaptor/**,**/config/**,**/excel/**,**/exception/**,**/external/**,**/statemachine/**,**/infrastructure/client/**,**/*Config.java,**/*Data.java,**/*Dto.java,**/*Do.java,**/*Command.java,**/*Event.java,**/*Query.java, **/*Utils.java,**/ItemPriceApplicationServiceImpl.java,**/CalculateItemPriceApi.java,**/Application.java,**/payload/**"
    }
}

jacocoTestReport {
    dependsOn test, integrationTest
    reports {
        xml.required = true
        html.required = true
    }
    executionData = files(
            "$buildDir/jacoco/test.exec",
            "$buildDir/jacoco/integrationTest.exec"
    )
}


test {
    maxParallelForks = Runtime.runtime.availableProcessors().intdiv(2) ?: 1
    finalizedBy jacocoTestReport
}

integrationTest {
    // Use integration profile for IT tests
    systemProperty 'spring.profiles.active', 'integration'
    // IT tests run sequentially to avoid resource conflicts
    maxParallelForks = 1
    // Increase timeout for IT tests
    timeout = Duration.ofMinutes(10)
    finalizedBy jacocoTestReport
}

dependencies {
    implementation 'com.mercaso.data:data_client:1.0.12200'
    implementation 'javax.xml.bind:jaxb-api:2.3.1'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.google.guava:guava:33.2.1-jre'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'io.github.resilience4j:resilience4j-spring-boot2:2.1.0'
    implementation 'io.github.resilience4j:resilience4j-annotations:2.1.0'
    implementation 'io.github.resilience4j:resilience4j-ratelimiter:2.1.0'
    implementation 'com.google.api-client:google-api-client:2.0.0'
    implementation 'com.google.apis:google-api-services-drive:v3-rev20220815-2.0.0'
    implementation 'com.google.auth:google-auth-library-oauth2-http:1.14.0'
    implementation 'org.apache.skywalking:apm-toolkit-meter:9.2.0'
    implementation 'org.apache.tika:tika-core:2.6.0'
    implementation 'org.jsoup:jsoup:1.15.4'
    implementation 'software.amazon.awssdk:textract:2.26.24'
    implementation 'com.google.apis:google-api-services-sheets:v4-rev20220927-2.0.0'
    implementation 'com.sun.mail:javax.mail:1.6.2'
    implementation 'org.springframework.ai:spring-ai-starter-model-openai:1.0.0'
    implementation 'org.springframework.ai:spring-ai-retry:1.0.0'
}

springBoot {
    mainClass = 'com.mercaso.ims.Application'
}
