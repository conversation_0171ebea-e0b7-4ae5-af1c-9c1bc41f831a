# Enable parallel execution for unit tests only
junit.jupiter.execution.parallel.enabled=true
junit.jupiter.execution.parallel.mode.default=same_thread
junit.jupiter.execution.parallel.mode.classes.default=concurrent

# Configure parallel execution strategy
junit.jupiter.execution.parallel.config.strategy=dynamic
junit.jupiter.execution.parallel.config.dynamic.factor=1.0

# Integration tests should run sequentially to avoid resource conflicts
junit.jupiter.execution.parallel.config.custom.class.0=.*IT$
junit.jupiter.execution.parallel.config.custom.class.0.mode=same_thread