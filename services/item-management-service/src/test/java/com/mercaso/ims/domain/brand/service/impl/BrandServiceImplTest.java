package com.mercaso.ims.domain.brand.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.BrandRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {BrandServiceImpl.class})
class BrandServiceImplTest extends AbstractTest {

    @MockBean
    private BrandRepository brandRepository;

    @Autowired
    private BrandServiceImpl brandServiceImpl;

    @Test
    void testFindByName() {

        // Arrange
        when(brandRepository.findByName(Mockito.<String>any())).thenReturn(null);

        // Act
        Brand actualFindByNameResult = brandServiceImpl.findByName("Name");

        // Assert
        verify(brandRepository).findByName("Name");
        assertNull(actualFindByNameResult);
    }

    @Test
    void testSave() {
        // Arrange
        when(brandRepository.save(Mockito.<Brand>any())).thenReturn(null);

        // Act
        Brand actualSaveResult = brandServiceImpl.save(null);

        // Assert
        verify(brandRepository).save(isNull());
        assertNull(actualSaveResult);
    }

    @Test
    void testUpdate() {
        // Arrange
        when(brandRepository.update(Mockito.<Brand>any())).thenReturn(null);

        // Act
        Brand actualUpdateResult = brandServiceImpl.update(null);

        // Assert
        verify(brandRepository).update(isNull());
        assertNull(actualUpdateResult);
    }

    @Test
    void testDelete() {
        // Arrange
        when(brandRepository.deleteById(Mockito.<UUID>any())).thenReturn(null);

        // Act
        brandServiceImpl.delete(UUID.randomUUID());

        // Assert that nothing has changed
        verify(brandRepository).deleteById(isA(UUID.class));
    }

    @Test
    void testFindByIds() {
        // Arrange
        ArrayList<Brand> brandList = new ArrayList<>();
        when(brandRepository.findByIds(Mockito.<List<UUID>>any())).thenReturn(brandList);
        ArrayList<UUID> ids = new ArrayList<>();

        // Act
        List<Brand> actualFindByIdsResult = brandServiceImpl.findByIds(ids);

        // Assert
        verify(brandRepository).findByIds(isA(List.class));
        assertTrue(actualFindByIdsResult.isEmpty());
        assertEquals(actualFindByIdsResult, ids);
        assertEquals(actualFindByIdsResult, brandServiceImpl.findAll());
        assertSame(brandList, actualFindByIdsResult);
    }

    @Test
    void testFindByIdsArrange() {
        // Arrange
        ArrayList<Brand> brandList = new ArrayList<>();
        when(brandRepository.findByIds(Mockito.<List<UUID>>any())).thenReturn(brandList);

        ArrayList<UUID> ids = new ArrayList<>();
        ids.add(UUID.randomUUID());

        // Act
        List<Brand> actualFindByIdsResult = brandServiceImpl.findByIds(ids);

        // Assert
        verify(brandRepository).findByIds(isA(List.class));
        assertTrue(actualFindByIdsResult.isEmpty());
        assertEquals(actualFindByIdsResult, brandServiceImpl.findAll());
        assertSame(brandList, actualFindByIdsResult);
    }

    @Test
    void testFindByIdsAssert() {
        // Arrange
        ArrayList<Brand> brandList = new ArrayList<>();
        when(brandRepository.findByIds(Mockito.<List<UUID>>any())).thenReturn(brandList);

        ArrayList<UUID> ids = new ArrayList<>();
        ids.add(UUID.randomUUID());
        ids.add(UUID.randomUUID());

        // Act
        List<Brand> actualFindByIdsResult = brandServiceImpl.findByIds(ids);

        // Assert
        verify(brandRepository).findByIds(isA(List.class));
        assertTrue(actualFindByIdsResult.isEmpty());
        assertEquals(actualFindByIdsResult, brandServiceImpl.findAll());
        assertSame(brandList, actualFindByIdsResult);
    }

    @Test
    void testFindAll() {
        // Arrange
        ArrayList<Brand> brandList = new ArrayList<>();
        when(brandRepository.findAll()).thenReturn(brandList);

        // Act
        List<Brand> actualFindAllResult = brandServiceImpl.findAll();

        // Assert
        verify(brandRepository).findAll();
        assertTrue(actualFindAllResult.isEmpty());
        assertSame(brandList, actualFindAllResult);
    }

    @Test
    void testFindByFuzzyName() {
        // Arrange
        ArrayList<Brand> brandList = new ArrayList<>();
        when(brandRepository.findByFuzzyName(Mockito.<String>any())).thenReturn(brandList);

        // Act
        List<Brand> actualFindByFuzzyNameResult = brandServiceImpl.findByFuzzyName("Name");

        // Assert
        verify(brandRepository).findByFuzzyName("Name");
        assertTrue(actualFindByFuzzyNameResult.isEmpty());
        assertEquals(actualFindByFuzzyNameResult, brandServiceImpl.findAll());
        assertSame(brandList, actualFindByFuzzyNameResult);
    }
}
