package com.mercaso.ims.utils.businessevent;

import com.mercaso.ims.domain.businessevent.BusinessEvent;
import com.mercaso.ims.domain.businessevent.enums.EventTypeEnums;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import java.util.UUID;

public class BusinessEventUtil {

    public static BusinessEvent buildBusinessEvent(UUID id,
        BusinessEventPayloadDto payloadDto) {
        return BusinessEvent.builder()
            .id(id)
            .type(EventTypeEnums.forPayload(payloadDto.getClass()))
            .payload(SerializationUtils.serialize(payloadDto))
            .build();

    }
}
