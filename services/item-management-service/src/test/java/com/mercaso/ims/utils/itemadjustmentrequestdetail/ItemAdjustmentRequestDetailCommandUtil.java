package com.mercaso.ims.utils.itemadjustmentrequestdetail;

import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;

import java.util.UUID;

public class ItemAdjustmentRequestDetailCommandUtil {

    public static CreateItemAdjustmentRequestDetailCommand buildCreateItemAdjustmentRequestDetailCommand(UUID itemAdjustmentRequestId) {
        return CreateItemAdjustmentRequestDetailCommand.builder()
                .requestId(itemAdjustmentRequestId)
                .type(ItemAdjustmentType.CREATE)
                .sku("sku")
                .build();
    }
}
