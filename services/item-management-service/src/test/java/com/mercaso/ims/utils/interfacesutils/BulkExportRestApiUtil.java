package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

@Component
public class BulkExportRestApiUtil extends IntegrationTestRestUtil {

    private static final String BULK_EXPORT_URL = "/v1/bulk-export";

    public BulkExportRestApiUtil(Environment environment) {
        super(environment);
    }

    public void downloadFilteredBulkReport(String customFilter, ExportType exportType) throws Exception {
        UriComponentsBuilder builder = UriComponentsBuilder.fromPath(BULK_EXPORT_URL + "/download");

        if (customFilter != null && !customFilter.isEmpty()) {
            builder.queryParam("customFilter", customFilter);
        }
        if (exportType != null) {
            builder.queryParam("exportType", exportType);
        }

        String url = builder.toUriString();
        getEntity(url, Void.class);
    }

    public void downloadFilteredBulkReportWithoutParams() throws Exception {
        getEntity(BULK_EXPORT_URL + "/download", Void.class);
    }
}