package com.mercaso.ims.interfaces;

import static com.mercaso.ims.domain.item.enums.ItemUpcType.CASE_UPC;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.api.client.util.Lists;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.BatchUpdateItemPhotoCommand;
import com.mercaso.ims.application.command.BatchUpdateItemStatusCommand;
import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.command.UpdateItemPromoPriceCommand;
import com.mercaso.ims.application.command.UpdateItemUpcCommand;
import com.mercaso.ims.application.command.ValidateBarcodeCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemPhotoResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemPromoPriceResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemStatusResultDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemUPCDto;
import com.mercaso.ims.application.dto.ValidateBarcodeResultDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.enums.ArchivedReason;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import com.mercaso.ims.utils.item.ItemCommandUtil;
import com.mercaso.ims.utils.itemupc.ItemUPCUtil;
import java.io.File;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.mock.mockito.MockBean;

class ItemRestApiIT extends AbstractIT {

    private static final Logger log = LoggerFactory.getLogger(ItemRestApiIT.class);
    @MockBean
    protected DocumentApplicationService documentApplicationService;

    @BeforeEach
    void setup() {
        Mockito.reset(documentApplicationService);
    }

    @Test
    void shouldSuccessWhenGetItemDetailRequest() throws Exception {

        ItemDo itemDo = buildItemDataWithVendor("adasdada22324", "item_detail_JC66451_test", "66451_test");

        ItemDto itemDto = itemRestApiUtil.itemDetailRequest(itemDo.getId());
        log.info("itemDto: {}", itemDto);
        assertNotNull(itemDto);
    }


    @Test
    void shouldSuccessWhenDeleteItem() {

        String sku = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);

        Item item = itemRepository.findById(itemDo.getId());

        assertNotNull(item);

        itemRestApiUtil.deleteItemRequest(itemDo.getId());

        item = itemRepository.findById(itemDo.getId());

        assertNull(item);
    }

    @Test
    void shouldSuccessWhenDraftItem() throws JsonProcessingException {

        String sku = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);

        assertEquals(AvailabilityStatus.ACTIVE, itemDo.getAvailabilityStatus());

        itemRestApiUtil.draftItemRequest(itemDo.getId());

        Item item = itemRepository.findById(itemDo.getId());

        assertEquals(AvailabilityStatus.DRAFT, item.getAvailabilityStatus());
    }

    @Test
    void shouldSuccessWhenArchiveItem() throws JsonProcessingException {

        String sku = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);

        assertEquals(AvailabilityStatus.ACTIVE, itemDo.getAvailabilityStatus());

        UpdateItemCommand command = UpdateItemCommand.builder().archivedReason(ArchivedReason.SC_DISCONTINUED).build();

        itemRestApiUtil.archiveItemRequest(itemDo.getId(), command);

        Item item = itemRepository.findById(itemDo.getId());

        assertEquals(AvailabilityStatus.ARCHIVED, item.getAvailabilityStatus());
    }

    @Test
    void shouldSuccessWhenActiveItem() throws JsonProcessingException {

        String sku = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);
        itemRestApiUtil.draftItemRequest(itemDo.getId());
        Item item = itemRepository.findById(itemDo.getId());

        assertEquals(AvailabilityStatus.DRAFT, item.getAvailabilityStatus());

        itemRestApiUtil.activeItemRequest(itemDo.getId());

        item = itemRepository.findById(itemDo.getId());

        assertEquals(AvailabilityStatus.ACTIVE, item.getAvailabilityStatus());
    }


    @Test
    void shouldSuccessWhenCreateItem() throws Exception {
        String brandName = "brand" + System.currentTimeMillis();
        String vendorName = "vendor" + System.currentTimeMillis();

        Brand brand = buildBrandData(brandName);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        CreateItemCommand command = ItemCommandUtil.buildCreateItemCommand(null);
        command.setBrandId(brand.getId());
        command.setPrimaryVendorId(vendorDo.getId());

        ItemDto result = itemRestApiUtil.createItemRequest(command);

        Item item = itemRepository.findById(result.getId());

        assertNotNull(item);
    }

    @Test
    void shouldSuccessWhenUpdateItem() throws Exception {

        String sku = "sku" + System.currentTimeMillis();

        ItemDo itemDo = buildItemData(sku);
        UpdateItemCommand command = ItemCommandUtil.buildUpdateItemCommand(itemDo.getId(), itemDo.getSkuNumber(), null);
        ItemDto result = itemRestApiUtil.updateItemRequest(itemDo.getId(), command);

        Item item = itemRepository.findById(result.getId());

        assertNotNull(item);
        assertEquals(command.getTitle(), item.getTitle());
    }

    @Test
    void shouldSuccessWhenSetPrimaryVendor() throws Exception {

        String sku = RandomStringUtils.randomNumeric(5);
        String vendorName = RandomStringUtils.randomNumeric(5);
        String vendorItemNumber = RandomStringUtils.randomNumeric(5);

        ItemDo itemDo = buildItemData(sku);

        VendorDo vendorDo = buildVendorDoData(vendorName);
        buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        ItemDto result = itemRestApiUtil.setPrimaryVendor(itemDo.getId(), vendorDo.getId());

        Item item = itemRepository.findById(result.getId());

        assertNotNull(item);
        assertEquals(vendorDo.getId(), item.getPrimaryVendorId());
    }

    @Test
    void shouldSuccessWhenSetBackupVendor() throws Exception {

        String sku = "sku" + System.currentTimeMillis();

        ItemDo itemDo = buildItemData(sku);

        String vendorName = RandomStringUtils.randomNumeric(5);

        VendorDo vendorDo = buildVendorDoData(vendorName);
        UUID vendorId = vendorDo.getId();
        String vendorItemNumber = RandomStringUtils.randomNumeric(5);

        buildVendorItemData(vendorItemNumber, vendorDo.getId(), itemDo.getId());

        ItemDto result = itemRestApiUtil.setBackupVendor(itemDo.getId(), vendorId);

        Item item = itemRepository.findById(result.getId());

        assertNotNull(item);
        assertEquals(vendorId, item.getBackupVendorId());
    }


    @Test
    void shouldSuccessWhenBatchUpdateItemStatus() throws Exception {

        String sku1 = "sku1" + System.currentTimeMillis();
        String sku2 = "sku2" + System.currentTimeMillis();

        ItemDo itemDo1 = buildItemData(sku1);
        ItemDo itemDo2 = buildItemData(sku2);
        UUID randomId = UUID.randomUUID();
        List<UUID> ids = List.of(itemDo1.getId(), itemDo2.getId(), randomId);
        BatchUpdateItemStatusCommand command = ItemCommandUtil.buildBatchUpdateItemStatusCommand(ids, AvailabilityStatus.ACTIVE);
        BatchUpdateItemStatusResultDto result = itemRestApiUtil.batchUpdateItemStatus(command);

        assertNotNull(result);
        assertEquals(2, result.getUpdatedCount());
        assertEquals(randomId.toString(), result.getFailedSkuNumbers().get(0));
    }


    @Test
    void shouldSuccessWhenValidateBarcodeAsInValidUpc() throws Exception {

        String upcNumber = RandomStringUtils.randomNumeric(5);

        ValidateBarcodeCommand command = ValidateBarcodeCommand.builder().barcode(upcNumber).type("CASE_UPC").build();
        ValidateBarcodeResultDto result = itemRestApiUtil.validateBarcode(command);

        assertNotNull(result);
        assertFalse(result.isValid());
        assertNull(result.getAssociatedItems());
    }


    @Test
    void shouldSuccessWhenValidateBarcodeAsValidUpc() throws Exception {

        String sku = RandomStringUtils.randomNumeric(5);
        String upcNumber = "9555755800036";
        ItemUPCDo itemUPC = ItemUPCUtil.buildItemUPCDo(upcNumber, CASE_UPC);

        List<ItemUPCDo> upcDos = Arrays.asList(itemUPC);

        ItemDo itemDo = buildItemDataWithUpc(sku, upcDos);

        ValidateBarcodeCommand command = ValidateBarcodeCommand.builder().barcode(upcNumber).type("CASE_UPC").build();
        ValidateBarcodeResultDto result = itemRestApiUtil.validateBarcode(command);

        assertNotNull(result);
        assertTrue(result.isValid());
        assertEquals(itemDo.getId(), result.getAssociatedItems().getFirst().getId());
    }


    @Test
    void shouldSuccessWhenBatchUpdatePhoto() throws Exception {

        String sku1 = "BatchUpdatePhoto_1_" + RandomStringUtils.randomNumeric(5);
        ItemDo itemDo1 = buildItemData(sku1);

        String sku2 = "BatchUpdatePhoto_2_" + RandomStringUtils.randomNumeric(5);
        ItemDo itemDo2 = buildItemData(sku2);
        List<BatchUpdateItemPhotoCommand> commands = Lists.newArrayList();
        commands.add(BatchUpdateItemPhotoCommand.builder().itemId(itemDo1.getId()).photoName("90001.jpg").build());
        commands.add(BatchUpdateItemPhotoCommand.builder().itemId(itemDo2.getId()).photoName("sku11734427248722.jpg").build());

        BatchUpdateItemPhotoResultDto batchUpdateItemPhotoResultDto = itemRestApiUtil.batchUpdatePhoto(commands);

        assertNotNull(batchUpdateItemPhotoResultDto);
        assertEquals(2, batchUpdateItemPhotoResultDto.getUpdatedCount());
    }

    @Test
    void shouldSuccessWhenUpdatePromoPrice() throws Exception {

        String sku = RandomStringUtils.randomNumeric(5);
        ItemDo itemDo = buildItemData(sku);

        UpdateItemPromoPriceCommand command = UpdateItemPromoPriceCommand.builder()
            .itemId(itemDo.getId())
            .promoPrice(BigDecimal.TEN)
            .promoFlag(true)
            .promoBeginTime(Instant.now().plusSeconds(100L))
            .build();

        ItemDto result = itemRestApiUtil.updatePromoPrice(command);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(0, result.getPromoPrice().compareTo(BigDecimal.TEN));
    }

    @Test
    void shouldSuccessWhenUpdateUpc() throws Exception {

        String sku = RandomStringUtils.randomNumeric(5);
        String upcNumber = RandomStringUtils.randomNumeric(5);
        ItemDo itemDo = buildItemData(sku);
        ArrayList<ItemUPCDto> itemUPCs = new ArrayList<>();
        itemUPCs.add(ItemUPCUtil.buildItemUPCDto(upcNumber, CASE_UPC));
        itemUPCs.add(ItemUPCUtil.buildItemUPCDto(upcNumber, CASE_UPC));
        UpdateItemUpcCommand command = UpdateItemUpcCommand.builder()
            .itemId(itemDo.getId())
            .itemUPCs(itemUPCs)
            .build();

        ItemDto result = itemRestApiUtil.updateItemUpc(command);

        Assertions.assertNotNull(result);
        Assertions.assertEquals(2, result.getItemUPCs().size());
    }

    @Test
    void shouldSuccessWhenBatchUpdatePromoPrice() throws Exception {

        String sku = RandomStringUtils.randomNumeric(5);
        ItemDo itemDo = buildItemData(sku);

        UpdateItemPromoPriceCommand command = UpdateItemPromoPriceCommand.builder()
            .itemId(itemDo.getId())
            .promoPrice(BigDecimal.TEN)
            .promoFlag(true)
            .promoBeginTime(Instant.now().plusSeconds(100L))
            .build();

        BatchUpdateItemPromoPriceResultDto result = itemRestApiUtil.batchUpdatePromoPrice(List.of(command));

        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.getUpdatedCount());
    }

    @Test
    void shouldSuccessWhenBindingPhoto() throws Exception {
        String sku = "DW1226751";
        buildItemData(sku);
        String photoName = "DW1226751.png";

        String fileName = getFilePath("file/DW1226751.png");
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName(photoName);
        documentResponse.setSignedUrl("https://example.org/example");
        when(documentApplicationService.uploadImage(any(), anyString())).thenReturn(documentResponse);
        ItemDto itemDto = itemRestApiUtil.bindingSkuPhoto(new File(fileName));

        assertNotNull(itemDto);
        assertEquals(photoName, itemDto.getPhotoName());
    }

    private String getFilePath(String fileName) {
        return getClass().getClassLoader().getResource(fileName).getPath();

    }


}
