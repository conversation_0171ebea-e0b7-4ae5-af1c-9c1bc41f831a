package com.mercaso.ims.utils.attributegroup;

import com.mercaso.ims.domain.attributegroup.enums.AttributeGroupStatus;
import com.mercaso.ims.infrastructure.repository.attributegroup.jpa.dataobject.AttributeGroupDo;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.UUID;

public class AttributeGroupUtil {

    public static AttributeGroupDo buildAttributeGroupDo() {
        AttributeGroupDo attributeGroupDo = new AttributeGroupDo();
        attributeGroupDo.setAttributeGroupDetails(new ArrayList<>());
        attributeGroupDo.setCategoryId(UUID.randomUUID());
        attributeGroupDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeGroupDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeGroupDo.setCreatedUserName("janedoe");
        attributeGroupDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeGroupDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeGroupDo.setDeletedUserName("janedoe");
        attributeGroupDo.setDescription("The characteristics of someone or something");
        attributeGroupDo.setId(UUID.randomUUID());
        attributeGroupDo.setName("Name");
        attributeGroupDo.setStatus(AttributeGroupStatus.DRAFT);
        attributeGroupDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeGroupDo.setUpdatedBy("2020-03-01");
        attributeGroupDo.setUpdatedUserName("janedoe");
        return attributeGroupDo;
    }
}
