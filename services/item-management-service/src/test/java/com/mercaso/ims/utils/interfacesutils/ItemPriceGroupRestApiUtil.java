package com.mercaso.ims.utils.interfacesutils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.ims.application.command.BatchBindingItemToPriceGroupCommand;
import com.mercaso.ims.application.command.BatchUnbindingItemFromPriceGroupCommand;
import com.mercaso.ims.application.command.CreateItemPriceGroupCommand;
import com.mercaso.ims.application.command.UpdateItemPriceGroupCommand;
import com.mercaso.ims.application.dto.BatchBindingItemPriceGroupResultDto;
import com.mercaso.ims.application.dto.BatchUnbindingItemPriceGroupResultDto;
import com.mercaso.ims.application.dto.ItemPriceGroupDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class ItemPriceGroupRestApi<PERSON>til extends IntegrationTestRestUtil {

    private static final String ITEM_PRICE_GROUP_REST_API_URL = "/v1/item-price-group";

    public ItemPriceGroupRestApiUtil(Environment environment) {
        super(environment);
    }

    public ItemPriceGroupDto createItemPriceGroupRequest(CreateItemPriceGroupCommand command) throws Exception {
        return createEntity(ITEM_PRICE_GROUP_REST_API_URL, command, ItemPriceGroupDto.class);
    }

    public ItemPriceGroupDto updateItemPriceGroup(UpdateItemPriceGroupCommand command) throws Exception {
        return updateEntity(ITEM_PRICE_GROUP_REST_API_URL, command, ItemPriceGroupDto.class);
    }

    public ItemPriceGroupDto getItemPriceGroupDetail(UUID id) throws Exception {
        return getEntity(ITEM_PRICE_GROUP_REST_API_URL + "/" + id, ItemPriceGroupDto.class);
    }

    public BatchBindingItemPriceGroupResultDto batchBindingItemPriceGroup(BatchBindingItemToPriceGroupCommand commands)
        throws JsonProcessingException {
        return updateEntity(ITEM_PRICE_GROUP_REST_API_URL + "/batch-binding-items",
            commands,
            BatchBindingItemPriceGroupResultDto.class);
    }

    public BatchUnbindingItemPriceGroupResultDto batchUnbindingItemPriceGroup(BatchUnbindingItemFromPriceGroupCommand commands)
        throws JsonProcessingException {
        return updateEntity(ITEM_PRICE_GROUP_REST_API_URL + "/batch-unbinding-items",
            commands,
            BatchUnbindingItemPriceGroupResultDto.class);
    }

    public void deleteItemPriceGroup(UUID id) {
        deleteEntity(ITEM_PRICE_GROUP_REST_API_URL + "/" + id);
    }

}
