package com.mercaso.ims.application.service.impl;

import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.service.ShopifyApplicationService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.itemsyncinfo.ItemAdjustmentSyncStatus;
import com.mercaso.ims.domain.itemsyncinfo.ItemAdjustmentSyncStatusRepository;
import com.mercaso.ims.domain.itemsyncinfo.enums.SyncShopifyStatus;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.event.factory.ApplicationEventFactory;
import com.mercaso.ims.infrastructure.schedule.ItemAdjustmentSyncScheduler;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.ratelimiter.internal.AtomicRateLimiter;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemAdjustmentSyncScheduler.class})
class ItemAdjustmentSyncSchedulerTest extends AbstractTest {

    private static final Integer LOCK_KEY = "[ItemAdjustmentSyncService.startSyncShopifyTask]".hashCode();
    @MockBean
    private ApplicationEventFactory applicationEventFactory;
    @MockBean
    private BusinessEventService businessEventService;
    @MockBean
    private EntityManagerFactory entityManagerFactory;
    @MockBean
    private EntityManager entityManager;
    @MockBean
    private ItemAdjustmentSyncStatusRepository itemAdjustmentSyncStatusRepository;
    @MockBean
    private PgAdvisoryLock pgAdvisoryLock;
    @MockBean
    private RateLimiterRegistry rateLimiterRegistry;
    @MockBean
    private ShopifyApplicationService shopifyApplicationService;
    @Autowired
    private ItemAdjustmentSyncScheduler itemAdjustmentSyncServiceImpl;


    @Test
    void testStartSyncShopifyTask() {
        // Arrange
        when(itemAdjustmentSyncStatusRepository.findBySyncShopifyStatus(Mockito.<SyncShopifyStatus>any()))
            .thenReturn(new ArrayList<>());
        RateLimiterConfig rateLimiterConfig = mock(RateLimiterConfig.class);
        when(rateLimiterConfig.getLimitForPeriod()).thenReturn(1);
        AtomicRateLimiter atomicRateLimiter = new AtomicRateLimiter("Name", rateLimiterConfig);

        when(rateLimiterRegistry.rateLimiter(Mockito.<String>any())).thenReturn(atomicRateLimiter);

        when(entityManagerFactory.createEntityManager()).thenReturn(entityManager);

        when(pgAdvisoryLock.tryLockWithSessionLevel(entityManager, LOCK_KEY, "Sync Shopify Task")).thenReturn(true);

        // Act
        itemAdjustmentSyncServiceImpl.startSyncShopifyTask();

        // Assert that nothing has changed
        verify(itemAdjustmentSyncStatusRepository).findBySyncShopifyStatus(SyncShopifyStatus.FAILURE);
        verify(rateLimiterConfig).getLimitForPeriod();
        verify(rateLimiterRegistry).rateLimiter("syncShopify");
    }

    @Test
    void testStartSyncShopifyTask2() {

        when(entityManagerFactory.createEntityManager()).thenReturn(entityManager);

        when(pgAdvisoryLock.tryLockWithSessionLevel(entityManager, LOCK_KEY, "Sync Shopify Task")).thenReturn(true);

        // Arrange
        ItemAdjustmentSyncStatus itemAdjustmentSyncStatus = mock(ItemAdjustmentSyncStatus.class);
        when(itemAdjustmentSyncStatus.getBusinessEventId()).thenReturn(UUID.randomUUID());

        ArrayList<ItemAdjustmentSyncStatus> itemAdjustmentSyncStatusList = new ArrayList<>();
        itemAdjustmentSyncStatusList.add(itemAdjustmentSyncStatus);
        when(itemAdjustmentSyncStatusRepository.findBySyncShopifyStatus(Mockito.<SyncShopifyStatus>any()))
            .thenReturn(itemAdjustmentSyncStatusList);
        when(businessEventService.findByIdInAndOrderByCreatedAt(Mockito.<List<UUID>>any())).thenReturn(new ArrayList<>());
        RateLimiterConfig rateLimiterConfig = mock(RateLimiterConfig.class);
        when(rateLimiterConfig.getLimitForPeriod()).thenReturn(1);
        AtomicRateLimiter atomicRateLimiter = new AtomicRateLimiter("Name", rateLimiterConfig);

        when(rateLimiterRegistry.rateLimiter(Mockito.<String>any())).thenReturn(atomicRateLimiter);

        // Act
        itemAdjustmentSyncServiceImpl.startSyncShopifyTask();

        // Assert that nothing has changed
        verify(businessEventService).findByIdInAndOrderByCreatedAt(isA(List.class));
        verify(itemAdjustmentSyncStatus).getBusinessEventId();
        verify(itemAdjustmentSyncStatusRepository).findBySyncShopifyStatus(SyncShopifyStatus.FAILURE);
        verify(rateLimiterConfig).getLimitForPeriod();
        verify(rateLimiterRegistry).rateLimiter("syncShopify");
    }

}
