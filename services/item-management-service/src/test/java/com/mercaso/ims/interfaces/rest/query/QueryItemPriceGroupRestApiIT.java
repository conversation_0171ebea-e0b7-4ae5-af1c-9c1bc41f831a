package com.mercaso.ims.interfaces.rest.query;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.CreateItemPriceGroupCommand;
import com.mercaso.ims.application.dto.ItemPriceGroupDto;
import java.math.BigDecimal;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class QueryItemPriceGroupRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenSearchItemPriceGroup() throws Exception {
        String groupName = RandomStringUtils.randomAlphabetic(5);

        CreateItemPriceGroupCommand command = CreateItemPriceGroupCommand.builder()
            .groupName(groupName)
            .price(BigDecimal.TEN)
            .build();

        ItemPriceGroupDto result = itemPriceGroupRestApiUtil.createItemPriceGroupRequest(command);

        List<ItemPriceGroupDto> vendorDtos = queryItemPriceGroupRestApiUtil.searchItemPriceGroup("");
        Assertions.assertNotNull(result);
        Assertions.assertNotNull(vendorDtos);

    }
}
