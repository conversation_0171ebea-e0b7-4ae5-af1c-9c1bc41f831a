package com.mercaso.ims.interfaces.rest.query;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.VendorDto;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class QueryVendorRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenSearchVendors() throws Exception {

        List<VendorDto> vendorDtos = queryVendorRestApiUtil.searchVendors("");
        Assertions.assertNotNull(vendorDtos);

    }
}
