package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.command.CreateVendorCommand;
import com.mercaso.ims.application.command.UpdateVendorCommand;
import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class VendorRestApiUtil extends IntegrationTestRestUtil {

    private static final String VENDOR_REST_API_URL = "/v1/vendor";

    public VendorRestApiUtil(Environment environment) {
        super(environment);
    }


    public void deleteVendorRequest(UUID id) {
        deleteEntity(VENDOR_REST_API_URL + "/" + id);
    }

    public VendorDto updateVendorRequest(UUID id, UpdateVendorCommand command) throws Exception {
        return updateEntity(VENDOR_REST_API_URL + "/" + id, command, VendorDto.class);
    }

    public VendorDto createVendorRequest(CreateVendorCommand command) throws Exception {
        return createEntity(VENDOR_REST_API_URL, command, VendorDto.class);
    }

}
