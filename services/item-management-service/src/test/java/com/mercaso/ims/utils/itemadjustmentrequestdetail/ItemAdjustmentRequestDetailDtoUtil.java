package com.mercaso.ims.utils.itemadjustmentrequestdetail;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDetailDto;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import java.math.BigDecimal;
import java.util.UUID;

public class ItemAdjustmentRequestDetailDtoUtil {

    public static ItemAdjustmentRequestDetailDto buildItemAdjustmentRequestDetailDto(UUID itemAdjustmentRequestId) {
        return ItemAdjustmentRequestDetailDto.builder()
            .requestId(itemAdjustmentRequestId)
            .type(ItemAdjustmentType.CREATE)
            .primaryPoVendor("vendor")
            .primaryPoVendorItemCost(BigDecimal.TEN)
            .vendorItemNumber("111111")
            .sku("sku")
            .newDescription("detail")
            .itemStatus(AvailabilityStatus.ACTIVE)
            .title("description")
            .status(ItemAdjustmentStatus.PENDING)
            .companyId("1000001")
            .locationId("1000001")
            .brand("brand")
            .packageSize(10)
            .itemSize("2.2")
            .itemUnitMeasure("oz")
            .regPricePackNoCrv(BigDecimal.TEN)
            .promoPricePackNoCrv(BigDecimal.TEN)
            .promoFlag(Boolean.TRUE)
            .build();
    }

    public static ItemAdjustmentRequestDetailDto buildUpdateItemAdjustmentRequestDetailDto(UUID itemAdjustmentRequestId) {
        return ItemAdjustmentRequestDetailDto.builder()
            .requestId(itemAdjustmentRequestId)
            .type(ItemAdjustmentType.UPDATE)
            .status(ItemAdjustmentStatus.PENDING)
            .sku("sku")
            .brand("brand")
            .vendor("vendor")
            .vendorItemNumber("1111111")
            .regPricePackNoCrv(BigDecimal.TEN)
            .attributeName("companyId")
            .attributeValue("1111111")
            .caseUpc("2323")
            .upc("123456789012")
            .promoFlag(Boolean.TRUE)
            .promoPricePackNoCrv(BigDecimal.TEN)
            .itemUnitMeasure("oz")
            .itemSize("2.2")
            .eachUpc("1234")
            .tags("tag1,tag2")
            .department("department")
            .category("category")
            .subCategory("subCategory")
            .caseWeight(1.1)
            .caseWeightUnit("lb")
            .eachWeight(0.5)
            .eachWeightUnit("lb")
            .primaryPoVendor("vendor")
            .primaryVendorItemAisle("3232")
            .build();
    }

    public static ItemAdjustmentRequestDetailDto buildDeleteItemAdjustmentRequestDetailDto(UUID itemAdjustmentRequestId) {
        return ItemAdjustmentRequestDetailDto.builder()
            .requestId(itemAdjustmentRequestId)
            .type(ItemAdjustmentType.DELETE)
            .status(ItemAdjustmentStatus.PENDING)
            .sku("sku")
            .build();
    }

    public static ItemAdjustmentRequestDetailDto buildRemoveUpcAdjustmentRequestDetailDto(UUID itemAdjustmentRequestId) {
        return ItemAdjustmentRequestDetailDto.builder()
            .requestId(itemAdjustmentRequestId)
            .type(ItemAdjustmentType.CLEAN_UPC)
            .status(ItemAdjustmentStatus.PENDING)
            .sku("sku")
            .build();
    }


}
