package com.mercaso.ims.utils.itemattribute;

import com.mercaso.ims.domain.itemattribute.ItemAttribute;
import java.util.UUID;

public class ItemAttributeUtil {

    public static ItemAttribute buildItemAttribute() {
        return buildItemAttribute(UUID.randomUUID(), UUID.randomUUID());
    }

    public static ItemAttribute buildItemAttribute(UUID itemId, UUID attributeId) {
        return ItemAttribute.builder()
            .itemId(itemId)
            .attributeId(attributeId)
            .value("2")
            .unit("unit")
            .sort(1)
            .build();
    }

}
