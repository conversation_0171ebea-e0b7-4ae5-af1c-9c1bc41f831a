package com.mercaso.ims.domain.itemadjustmentrequestdetail;

import static org.mockito.ArgumentMatchers.any;

import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentTransitionEvents;
import com.mercaso.ims.infrastructure.statemachine.processor.StateMachineProcessor;
import com.mercaso.ims.infrastructure.util.SpringContextUtil;
import com.mercaso.ims.utils.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailUtil;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class ItemAdjustmentRequestDetailTest {

    @Mock
    StateMachineProcessor<ItemAdjustmentRequestDetail, ItemAdjustmentStatus, ItemAdjustmentTransitionEvents> stateMachineProcessor;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testUpdateIms() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {

            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            UUID itemAdjustmentRequestId = UUID.randomUUID();
            ItemAdjustmentRequestDetail itemAdjustmentRequestDetail = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(
                itemAdjustmentRequestId);
            ItemAdjustmentRequestDetail result = itemAdjustmentRequestDetail.updateIms();
            Assertions.assertNotNull(result);
        }
    }

    @Test
    void testUpdateImsFailure() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {
            String failureReason = "testUpdateImsFailure";

            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            UUID itemAdjustmentRequestId = UUID.randomUUID();
            ItemAdjustmentRequestDetail itemAdjustmentRequestDetail = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(
                itemAdjustmentRequestId);
            ItemAdjustmentRequestDetail result = itemAdjustmentRequestDetail.updateImsFailure(failureReason);
            Assertions.assertEquals(failureReason, result.getFailureReason());
        }
    }

    @Test
    void testSyncToShopify() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {

            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);

            UUID itemAdjustmentRequestId = UUID.randomUUID();
            ItemAdjustmentRequestDetail itemAdjustmentRequestDetail = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(
                itemAdjustmentRequestId);
            itemAdjustmentRequestDetail.updateIms();
            ItemAdjustmentRequestDetail result = itemAdjustmentRequestDetail.syncToShopify();
            Assertions.assertNotNull(result);
        }
    }

    @Test
    void testSyncToShopifyFailure() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {
            String failureReason = "testSyncToShopifyFailure";

            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            UUID itemAdjustmentRequestId = UUID.randomUUID();
            ItemAdjustmentRequestDetail itemAdjustmentRequestDetail = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(
                itemAdjustmentRequestId);
            itemAdjustmentRequestDetail.updateIms();
            ItemAdjustmentRequestDetail result = itemAdjustmentRequestDetail.syncToShopifyFailure(failureReason);
            Assertions.assertEquals(failureReason, result.getFailureReason());
        }
    }

    @Test
    void testIsCompleted() {

        UUID itemAdjustmentRequestId = UUID.randomUUID();
        ItemAdjustmentRequestDetail itemAdjustmentRequestDetail = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(
            itemAdjustmentRequestId);
        boolean result = itemAdjustmentRequestDetail.isCompleted();
        Assertions.assertFalse(result);
    }

    @Test
    void testIsFailure() {
        UUID itemAdjustmentRequestId = UUID.randomUUID();
        ItemAdjustmentRequestDetail itemAdjustmentRequestDetail = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(
            itemAdjustmentRequestId);
        boolean result = itemAdjustmentRequestDetail.isFailure();
        Assertions.assertFalse(result);
    }


}