package com.mercaso.ims.utils.itemcostcollection;

import static com.mercaso.ims.domain.vendor.VendorConstant.VERNON_SALES;

import com.mercaso.ims.application.dto.ItemCostCollectionDto;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import java.util.UUID;

public class ItemCostCollectionDtoUtil {

    public static ItemCostCollectionDto buildItemCostCollectionDto() {
        return buildItemCostCollectionDto(UUID.randomUUID());
    }

    public static ItemCostCollectionDto buildItemCostCollectionDto(UUID vendorId) {

        return new ItemCostCollectionDto(
            UUID.randomUUID(),
            ItemCostCollectionSources.MANUAL_UPLOADED,
            vendorId,
            VERNON_SALES,
            "collectionNumber",
            "collectionNumber",
            ItemCostCollectionTypes.CSV_FILE,
            "fileName",
            Integer.valueOf(0),
            Integer.valueOf(0),
            Integer.valueOf(0));
    }

}
