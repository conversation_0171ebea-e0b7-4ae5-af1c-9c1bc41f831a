package com.mercaso.ims.domain.item;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.ItemRegPriceRepository;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.utils.item.ItemUtil;
import com.mercaso.ims.utils.vendoritem.VendorItemUtil;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemSpecification.class})
class ItemSpecificationTest extends AbstractTest {

    @MockBean
    private VendorItemRepository vendorItemRepository;

    @MockBean
    private ItemRepository itemRepository;

    @MockBean
    private ItemRegPriceRepository regPriceRepository;

    @Autowired
    private ItemSpecification itemSpecification;

    private Item testItem;
    private UUID itemId;
    private UUID primaryVendorId;
    private UUID backupVendorId;

    @BeforeEach
    void setUp() {
        itemSpecification = new ItemSpecification(vendorItemRepository, itemRepository, regPriceRepository);

        primaryVendorId = UUID.randomUUID();
        backupVendorId = UUID.randomUUID();

        testItem = ItemUtil.buildItem("TEST-SKU", primaryVendorId, UUID.randomUUID(), UUID.randomUUID());
        itemId = testItem.getId();
        testItem.setBackupVendorId(backupVendorId);
        testItem.setAvailabilityStatus(AvailabilityStatus.ACTIVE);
    }

    @Test
    void isSatisfiedNewMargin_WhenItemNotFound_ShouldReturnFalse() {
        // Arrange
        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        when(itemRepository.findById(itemId)).thenReturn(null);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertFalse(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenAvailabilityStatusNotActive_ShouldReturnTrue() {
        // Arrange
        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setAvailabilityStatus(AvailabilityStatus.DRAFT);
        when(itemRepository.findById(itemId)).thenReturn(testItem);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenBackupVendorIdIsNull_ShouldReturnTrue() {
        // Arrange
        testItem.setBackupVendorId(null);
        testItem.setPrimaryVendorId(null);

        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setPrimaryVendorId(null);
        when(itemRepository.findById(itemId)).thenReturn(testItem);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenBackupVendorItemNotFound_ShouldReturnTrue() {
        // Arrange
        testItem.setPrimaryVendorId(null);

        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setPrimaryVendorId(null);
        when(itemRepository.findById(itemId)).thenReturn(testItem);
        when(vendorItemRepository.findByVendorIDAndItemId(backupVendorId, itemId)).thenReturn(null);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenPrimaryVendorIdIsNull_ShouldReturnTrue() {
        // Arrange
        testItem.setPrimaryVendorId(null);

        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setPrimaryVendorId(UUID.randomUUID());
        when(itemRepository.findById(itemId)).thenReturn(testItem);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenPrimaryVendorItemNotFound_ShouldReturnTrue() {
        // Arrange
        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setPrimaryVendorId(primaryVendorId);
        when(itemRepository.findById(itemId)).thenReturn(testItem);
        when(vendorItemRepository.findByVendorIDAndItemId(primaryVendorId, itemId)).thenReturn(null);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenPrimaryCostIsNull_ShouldReturnTrue() {
        // Arrange
        VendorItem vendorItem = VendorItemUtil.buildVendorItem(UUID.randomUUID());
        vendorItem.setPackPlusCrvCost(null);

        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setPrimaryVendorId(primaryVendorId);
        when(itemRepository.findById(itemId)).thenReturn(testItem);
        when(vendorItemRepository.findByVendorIDAndItemId(primaryVendorId, itemId)).thenReturn(vendorItem);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenItemRegPriceNotFound_ShouldReturnTrue() {
        // Arrange
        VendorItem vendorItem = VendorItemUtil.buildVendorItem(UUID.randomUUID());
        vendorItem.setPackPlusCrvCost(new BigDecimal("10.00"));

        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setPrimaryVendorId(primaryVendorId);
        when(itemRepository.findById(itemId)).thenReturn(testItem);
        when(vendorItemRepository.findByVendorIDAndItemId(primaryVendorId, itemId)).thenReturn(vendorItem);
        when(regPriceRepository.findByItemId(itemId)).thenReturn(null);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenNewPriceIsNull_ShouldReturnTrue() {
        // Arrange
        VendorItem vendorItem = VendorItemUtil.buildVendorItem(UUID.randomUUID());
        vendorItem.setPackPlusCrvCost(new BigDecimal("10.00"));

        ItemRegPrice itemRegPrice = ItemRegPrice.builder().build();
        itemRegPrice.setRegPrice(null);

        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setPrimaryVendorId(primaryVendorId);
        when(itemRepository.findById(itemId)).thenReturn(testItem);
        when(vendorItemRepository.findByVendorIDAndItemId(primaryVendorId, itemId)).thenReturn(vendorItem);
        when(regPriceRepository.findByItemId(itemId)).thenReturn(itemRegPrice);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenPriceIsTooLow_ShouldReturnFalse() {
        // Arrange
        VendorItem vendorItem = VendorItemUtil.buildVendorItem(UUID.randomUUID());
        vendorItem.setPackPlusCrvCost(new BigDecimal("10.00"));

        ItemRegPrice itemRegPrice = ItemRegPrice.builder().build();

        itemRegPrice.setRegPrice(new BigDecimal("8.00"));

        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setPrimaryVendorId(primaryVendorId);
        when(itemRepository.findById(itemId)).thenReturn(testItem);
        when(vendorItemRepository.findByVendorIDAndItemId(primaryVendorId, itemId)).thenReturn(vendorItem);
        when(regPriceRepository.findByItemId(itemId)).thenReturn(itemRegPrice);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertFalse(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenPriceIsSufficient_ShouldReturnTrue() {
        // Arrange
        VendorItem vendorItem = VendorItemUtil.buildVendorItem(UUID.randomUUID());
        vendorItem.setPackPlusCrvCost(new BigDecimal("10.00"));

        ItemRegPrice itemRegPrice = ItemRegPrice.builder().build();

        itemRegPrice.setRegPrice(new BigDecimal("15.00"));

        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setPrimaryVendorId(primaryVendorId);
        when(itemRepository.findById(itemId)).thenReturn(testItem);
        when(vendorItemRepository.findByVendorIDAndItemId(primaryVendorId, itemId)).thenReturn(vendorItem);
        when(regPriceRepository.findByItemId(itemId)).thenReturn(itemRegPrice);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenUsingBackupVendorAndPriceIsSufficient_ShouldReturnTrue() {
        // Arrange
        testItem.setPrimaryVendorId(null);

        VendorItem backupVendorItem = VendorItemUtil.buildVendorItem(UUID.randomUUID());
        backupVendorItem.setBackupPackPlusCrvCost(new BigDecimal("10.00"));

        ItemRegPrice itemRegPrice = ItemRegPrice.builder().build();

        itemRegPrice.setRegPrice(new BigDecimal("15.00"));

        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setPrimaryVendorId(null);
        when(itemRepository.findById(itemId)).thenReturn(testItem);
        when(vendorItemRepository.findByVendorIDAndItemId(backupVendorId, itemId)).thenReturn(backupVendorItem);
        when(regPriceRepository.findByItemId(itemId)).thenReturn(itemRegPrice);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertTrue(result);
    }

    @Test
    void isSatisfiedNewMargin_WhenUsingCommandRegPrice_ShouldUseCommandPrice() {
        // Arrange
        VendorItem vendorItem = VendorItemUtil.buildVendorItem(UUID.randomUUID());
        vendorItem.setPackPlusCrvCost(new BigDecimal("10.00"));

        ItemRegPrice itemRegPrice = ItemRegPrice.builder().build();

        itemRegPrice.setRegPrice(new BigDecimal("8.00")); // Lower than cost

        UpdateItemCommand command = UpdateItemCommand.builder()
            .id(itemId)
            .build();
        command.setPrimaryVendorId(primaryVendorId);
        command.setRegPrice(new BigDecimal("15.00")); // Higher than cost
        when(itemRepository.findById(itemId)).thenReturn(testItem);
        when(vendorItemRepository.findByVendorIDAndItemId(primaryVendorId, itemId)).thenReturn(vendorItem);
        when(regPriceRepository.findByItemId(itemId)).thenReturn(itemRegPrice);

        // Act
        boolean result = itemSpecification.isSatisfiedNewMargin(command);

        // Assert
        assertTrue(result);
    }
} 