package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class SearchCategoryTreeRequestRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_CATEGORIES_REQUEST_URL = "/v1/search/categories";

    public SearchCategoryTreeRequestRestApiUtil(Environment environment) {
        super(environment);
    }


    public List<CategoryDto> searchCategoryTreeNoParamsRequest() throws Exception {
        return getEntityList(SEARCH_CATEGORIES_REQUEST_URL, CategoryDto.class);
    }

    public List<CategoryDto> searchCategoryTreeByCategoryIdParamsRequest(UUID categoryId) throws Exception {
        return getEntityList(SEARCH_CATEGORIES_REQUEST_URL+"?categoryId="+categoryId, CategoryDto.class);
    }


}
