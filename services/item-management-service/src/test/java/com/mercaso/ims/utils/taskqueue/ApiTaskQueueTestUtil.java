package com.mercaso.ims.utils.taskqueue;

import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;

import java.time.Instant;
import java.util.UUID;

/**
 * Utility class for creating test data for ApiTaskQueue related tests
 */
public class ApiTaskQueueTestUtil {

    public static ApiTaskQueue buildApiTaskQueue() {
        return buildApiTaskQueue(TaskStatus.PENDING);
    }

    public static ApiTaskQueue buildApiTaskQueue(TaskStatus status) {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("FINALE_GET_PRODUCT")
                .apiEndpoint("/api/products")
                .httpMethod("GET")
                .requestPayload("{\"productId\": \"123\"}")
                .status(status)
                .priority(5)
                .maxRetryCount(3)
                .currentRetryCount(0)
                .scheduledAt(Instant.now())
                .createdAt(Instant.now())
                .createdBy("TEST_USER")
                .createdUserName("Test User")
                .updatedAt(Instant.now())
                .updatedBy("TEST_USER")
                .updatedUserName("Test User")
                .build();
    }

    public static ApiTaskQueue buildApiTaskQueueWithType(String taskType) {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType(taskType)
                .apiEndpoint("/api/products")
                .httpMethod("GET")
                .requestPayload("{\"productId\": \"123\"}")
                .status(TaskStatus.PENDING)
                .priority(5)
                .maxRetryCount(3)
                .currentRetryCount(0)
                .scheduledAt(Instant.now())
                .createdAt(Instant.now())
                .createdBy("TEST_USER")
                .createdUserName("Test User")
                .updatedAt(Instant.now())
                .updatedBy("TEST_USER")
                .updatedUserName("Test User")
                .build();
    }

    public static ApiTaskQueue buildApiTaskQueueWithPriority(Integer priority) {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("FINALE_GET_PRODUCT")
                .apiEndpoint("/api/products")
                .httpMethod("GET")
                .requestPayload("{\"productId\": \"123\"}")
                .status(TaskStatus.PENDING)
                .priority(priority)
                .maxRetryCount(3)
                .currentRetryCount(0)
                .scheduledAt(Instant.now())
                .createdAt(Instant.now())
                .createdBy("TEST_USER")
                .createdUserName("Test User")
                .updatedAt(Instant.now())
                .updatedBy("TEST_USER")
                .updatedUserName("Test User")
                .build();
    }

    public static ApiTaskQueue buildCompletedApiTaskQueue() {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("FINALE_GET_PRODUCT")
                .apiEndpoint("/api/products")
                .httpMethod("GET")
                .requestPayload("{\"productId\": \"123\"}")
                .responsePayload("{\"result\": \"success\"}")
                .status(TaskStatus.COMPLETED)
                .priority(5)
                .maxRetryCount(3)
                .currentRetryCount(0)
                .scheduledAt(Instant.now().minusSeconds(3600))
                .completedAt(Instant.now().minusSeconds(3600)) // Completed 1 hour ago
                .createdAt(Instant.now().minusSeconds(3600))
                .createdBy("TEST_USER")
                .createdUserName("Test User")
                .updatedAt(Instant.now().minusSeconds(3600))
                .updatedBy("TEST_USER")
                .updatedUserName("Test User")
                .build();
    }

    public static ApiTaskQueue buildProcessingApiTaskQueue(Instant startedAt) {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("FINALE_GET_PRODUCT")
                .apiEndpoint("/api/products")
                .httpMethod("GET")
                .requestPayload("{\"productId\": \"123\"}")
                .status(TaskStatus.PROCESSING)
                .priority(5)
                .maxRetryCount(3)
                .currentRetryCount(0)
                .scheduledAt(startedAt.minusSeconds(300))
                .startedAt(startedAt)
                .createdAt(startedAt.minusSeconds(300))
                .createdBy("TEST_USER")
                .createdUserName("Test User")
                .updatedAt(startedAt)
                .updatedBy("TEST_USER")
                .updatedUserName("Test User")
                .build();
    }

    public static ApiTaskQueue buildFailedApiTaskQueue() {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("FINALE_GET_PRODUCT")
                .apiEndpoint("/api/products")
                .httpMethod("GET")
                .requestPayload("{\"productId\": \"123\"}")
                .status(TaskStatus.FAILED)
                .priority(5)
                .maxRetryCount(3)
                .currentRetryCount(3)
                .scheduledAt(Instant.now().minusSeconds(3600))
                .completedAt(Instant.now())
                .errorMessage("Connection timeout")
                .createdAt(Instant.now().minusSeconds(3600))
                .createdBy("TEST_USER")
                .createdUserName("Test User")
                .updatedAt(Instant.now())
                .updatedBy("TEST_USER")
                .updatedUserName("Test User")
                .build();
    }

    public static ApiTaskQueue buildRetryApiTaskQueue() {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType("FINALE_GET_PRODUCT")
                .apiEndpoint("/api/products")
                .httpMethod("GET")
                .requestPayload("{\"productId\": \"123\"}")
                .status(TaskStatus.RETRY)
                .priority(5)
                .maxRetryCount(3)
                .currentRetryCount(1)
                .scheduledAt(Instant.now().plusSeconds(300)) // Retry in 5 minutes
                .errorMessage("Temporary failure")
                .createdAt(Instant.now().minusSeconds(1800))
                .createdBy("TEST_USER")
                .createdUserName("Test User")
                .updatedAt(Instant.now())
                .updatedBy("TEST_USER")
                .updatedUserName("Test User")
                .build();
    }
} 