package com.mercaso.ims.domain.attribute.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.attribute.Attribute;
import com.mercaso.ims.domain.attribute.AttributeRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {AttributeServiceImpl.class})
class AttributeServiceImplTest extends AbstractTest {

    @MockBean
    private AttributeRepository attributeRepository;

    @Autowired
    private AttributeServiceImpl attributeServiceImpl;

    @Test
    void testFindById() {
        // Arrange
        when(attributeRepository.findById(Mockito.<UUID>any())).thenReturn(null);

        // Act
        Attribute actualFindByIdResult = attributeServiceImpl.findById(UUID.randomUUID());

        // Assert
        verify(attributeRepository).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }

    @Test
    void testFindByIds() {
        // Arrange
        ArrayList<Attribute> attributeList = new ArrayList<>();
        when(attributeRepository.findAllByIdIn(Mockito.<List<UUID>>any())).thenReturn(attributeList);
        ArrayList<UUID> ids = new ArrayList<>();

        // Act
        List<Attribute> actualFindByIdsResult = attributeServiceImpl.findByIds(ids);

        // Assert
        verify(attributeRepository).findAllByIdIn(isA(List.class));
        assertTrue(actualFindByIdsResult.isEmpty());
        assertEquals(actualFindByIdsResult, ids);
        assertSame(attributeList, actualFindByIdsResult);
    }

    @Test
    void testFindByIdsSuccess() {
        // Arrange
        ArrayList<Attribute> attributeList = new ArrayList<>();
        when(attributeRepository.findAllByIdIn(Mockito.<List<UUID>>any())).thenReturn(attributeList);

        ArrayList<UUID> ids = new ArrayList<>();
        ids.add(UUID.randomUUID());

        // Act
        List<Attribute> actualFindByIdsResult = attributeServiceImpl.findByIds(ids);

        // Assert
        verify(attributeRepository).findAllByIdIn(isA(List.class));
        assertTrue(actualFindByIdsResult.isEmpty());
        assertSame(attributeList, actualFindByIdsResult);
    }

    @Test
    void testVerifyFindByIds() {
        // Arrange
        ArrayList<Attribute> attributeList = new ArrayList<>();
        when(attributeRepository.findAllByIdIn(Mockito.<List<UUID>>any())).thenReturn(attributeList);

        ArrayList<UUID> ids = new ArrayList<>();
        ids.add(UUID.randomUUID());
        ids.add(UUID.randomUUID());

        // Act
        List<Attribute> actualFindByIdsResult = attributeServiceImpl.findByIds(ids);

        // Assert
        verify(attributeRepository).findAllByIdIn(isA(List.class));
        assertTrue(actualFindByIdsResult.isEmpty());
        assertSame(attributeList, actualFindByIdsResult);
    }


    @Test
    void testFindByName() {
        // Arrange
        ArrayList<Attribute> attributeList = new ArrayList<>();
        when(attributeRepository.findByName(Mockito.<String>any())).thenReturn(attributeList);

        // Act
        List<Attribute> actualFindByNameResult = attributeServiceImpl.findByName("Name");

        // Assert
        verify(attributeRepository).findByName("Name");
        assertTrue(actualFindByNameResult.isEmpty());
        assertSame(attributeList, actualFindByNameResult);
    }


    @Test
    void testFindByCategoryIdAndName() {
        // Arrange
        when(attributeRepository.findByCategoryIdAndName(Mockito.<UUID>any(), Mockito.<String>any())).thenReturn(null);

        // Act
        Attribute actualFindByCategoryIdAndNameResult = attributeServiceImpl.findByCategoryIdAndName(UUID.randomUUID(),
            "Name");

        // Assert
        verify(attributeRepository).findByCategoryIdAndName(isA(UUID.class), eq("Name"));
        assertNull(actualFindByCategoryIdAndNameResult);
    }


    @Test
    void testFindByCategoryId() {
        // Arrange
        ArrayList<Attribute> attributeList = new ArrayList<>();
        when(attributeRepository.findByCategoryId(Mockito.<UUID>any())).thenReturn(attributeList);

        // Act
        List<Attribute> actualFindByCategoryIdResult = attributeServiceImpl.findByCategoryId(UUID.randomUUID());

        // Assert
        verify(attributeRepository).findByCategoryId(isA(UUID.class));
        assertTrue(actualFindByCategoryIdResult.isEmpty());
        assertSame(attributeList, actualFindByCategoryIdResult);
    }


    @Test
    void testSave() {
        // Arrange
        when(attributeRepository.save(Mockito.<Attribute>any())).thenReturn(null);

        // Act
        Attribute actualSaveResult = attributeServiceImpl.save(null);

        // Assert
        verify(attributeRepository).save(isNull());
        assertNull(actualSaveResult);
    }


    @Test
    void testUpdate() {
        // Arrange
        when(attributeRepository.update(Mockito.<Attribute>any())).thenReturn(null);

        // Act
        Attribute actualUpdateResult = attributeServiceImpl.update(null);

        // Assert
        verify(attributeRepository).update(isNull());
        assertNull(actualUpdateResult);
    }


    @Test
    void testDelete() {
        // Arrange
        when(attributeRepository.deleteById(Mockito.<UUID>any())).thenReturn(null);

        // Act
        attributeServiceImpl.delete(UUID.randomUUID());

        // Assert that nothing has changed
        verify(attributeRepository).deleteById(isA(UUID.class));
    }


    @Test
    void testFindByFuzzyName() {
        // Arrange
        ArrayList<Attribute> attributeList = new ArrayList<>();
        when(attributeRepository.findByFuzzyName(Mockito.<String>any())).thenReturn(attributeList);

        // Act
        List<Attribute> actualFindByFuzzyNameResult = attributeServiceImpl.findByFuzzyName("Name");

        // Assert
        verify(attributeRepository).findByFuzzyName("Name");
        assertTrue(actualFindByFuzzyNameResult.isEmpty());
        assertSame(attributeList, actualFindByFuzzyNameResult);
    }
}
