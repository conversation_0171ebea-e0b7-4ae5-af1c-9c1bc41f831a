package com.mercaso.ims.utils.vendor;

import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemType;
import java.math.BigDecimal;
import java.util.UUID;

public class VendorItemDtoUtil {

    public static VendorItemDto buildVendorItemDto(String vendorSkuNumber, UUID vendorId, UUID itemId) {
        return VendorItemDto.builder()
            .vendorSkuNumber(vendorSkuNumber)
            .vendorItemId(UUID.randomUUID())
            .vendorId(vendorId)
            .vendorItemName("Vendor Item Name")
            .itemId(itemId)
            .note("Note")
            .aisle("Aisle")
            .cost(BigDecimal.TEN)
            .backupCost(BigDecimal.TEN)
            .vendorItemType(VendorItemType.DIRECT.getTypeName())
            .build();
    }

    public static VendorItemDto buildVendorItemDto() {
        return buildVendorItemDto("vendorSkuNumber", UUID.randomUUID(), UUID.randomUUID());
    }

}
