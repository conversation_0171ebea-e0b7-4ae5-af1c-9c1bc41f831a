package com.mercaso.ims.utils.itemcostchangerequest;

import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequest;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import java.math.BigDecimal;
import java.util.UUID;

public class ItemCostChangeRequestUtil {

    public static ItemCostChangeRequest buildItemCostChangeRequest() {
        return buildItemCostChangeRequest(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID());
    }

    public static ItemCostChangeRequest buildItemCostChangeRequest(UUID itemCostCollectionId, UUID vendorId, UUID matchedItemId) {
        return ItemCostChangeRequest.builder()
            .itemCostCollectionId(itemCostCollectionId)
            .vendorSkuNumber("vendorSkuNumber")
            .itemId(matchedItemId)
            .vendorId(vendorId)
            .vendorItemName("Vendor Item Name")
            .previousCost(BigDecimal.valueOf(9))
            .targetCost(BigDecimal.valueOf(10))
            .matchType(MatchedType.MATCHED)
            .status(ItemCostChangeRequestStatus.PENDING)
            .build();
    }


}
