package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class QueryVendorRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_VENDORS_URL = "/v1/query/vendor";

    public QueryVendorRestApiUtil(Environment environment) {
        super(environment);
    }

    public List<VendorDto> searchVendors (String vendorName) throws Exception {
        String url = String.format(SEARCH_VENDORS_URL + "?vendorName=%s", vendorName);
        return getEntityList(url, VendorDto.class);
    }
}
