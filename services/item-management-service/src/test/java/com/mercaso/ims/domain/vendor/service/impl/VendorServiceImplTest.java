package com.mercaso.ims.domain.vendor.service.impl;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {VendorServiceImpl.class})
class VendorServiceImplTest extends AbstractTest {

    @MockBean
    private VendorRepository vendorRepository;

    @Autowired
    private VendorServiceImpl vendorServiceImpl;


    @Test
    void testFindById() {
        // Arrange
        when(vendorRepository.findById(Mockito.<UUID>any())).thenReturn(null);

        // Act
        Vendor actualFindByIdResult = vendorServiceImpl.findById(UUID.randomUUID());

        // Assert
        verify(vendorRepository).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }


    @Test
    void testFindByIds() {
        // Arrange
        ArrayList<Vendor> vendorList = new ArrayList<>();
        when(vendorRepository.findAllByIdIn(Mockito.<List<UUID>>any())).thenReturn(vendorList);

        // Act
        List<Vendor> actualFindByIdsResult = vendorServiceImpl.findByIds(new ArrayList<>());

        // Assert
        verify(vendorRepository).findAllByIdIn(isA(List.class));
        assertTrue(actualFindByIdsResult.isEmpty());
        assertSame(vendorList, actualFindByIdsResult);
    }


    @Test
    void testFindByIds2() {
        // Arrange
        ArrayList<Vendor> vendorList = new ArrayList<>();
        when(vendorRepository.findAllByIdIn(Mockito.<List<UUID>>any())).thenReturn(vendorList);

        ArrayList<UUID> ids = new ArrayList<>();
        ids.add(UUID.randomUUID());

        // Act
        List<Vendor> actualFindByIdsResult = vendorServiceImpl.findByIds(ids);

        // Assert
        verify(vendorRepository).findAllByIdIn(isA(List.class));
        assertTrue(actualFindByIdsResult.isEmpty());
        assertSame(vendorList, actualFindByIdsResult);
    }


    @Test
    void testFindByIds3() {
        // Arrange
        ArrayList<Vendor> vendorList = new ArrayList<>();
        when(vendorRepository.findAllByIdIn(Mockito.<List<UUID>>any())).thenReturn(vendorList);

        ArrayList<UUID> ids = new ArrayList<>();
        ids.add(UUID.randomUUID());
        ids.add(UUID.randomUUID());

        // Act
        List<Vendor> actualFindByIdsResult = vendorServiceImpl.findByIds(ids);

        // Assert
        verify(vendorRepository).findAllByIdIn(isA(List.class));
        assertTrue(actualFindByIdsResult.isEmpty());
        assertSame(vendorList, actualFindByIdsResult);
    }


    @Test
    void testFindAll() {
        // Arrange
        ArrayList<Vendor> vendorList = new ArrayList<>();
        when(vendorRepository.findAll()).thenReturn(vendorList);

        // Act
        List<Vendor> actualFindAllResult = vendorServiceImpl.findAll();

        // Assert
        verify(vendorRepository).findAll();
        assertTrue(actualFindAllResult.isEmpty());
        assertSame(vendorList, actualFindAllResult);
    }


    @Test
    void testFindByVendorName() {
        // Arrange
        when(vendorRepository.findByVendorName(Mockito.<String>any())).thenReturn(null);

        // Act
        Vendor actualFindByVendorNameResult = vendorServiceImpl.findByVendorName("Vendor Name");

        // Assert
        verify(vendorRepository).findByVendorName("Vendor Name");
        assertNull(actualFindByVendorNameResult);
    }


    @Test
    void testSave() {
        // Arrange
        when(vendorRepository.save(Mockito.<Vendor>any())).thenReturn(null);

        // Act
        Vendor actualSaveResult = vendorServiceImpl.save(null);

        // Assert
        verify(vendorRepository).save(isNull());
        assertNull(actualSaveResult);
    }


    @Test
    void testUpdate() {
        // Arrange
        when(vendorRepository.update(Mockito.<Vendor>any())).thenReturn(null);

        // Act
        Vendor actualUpdateResult = vendorServiceImpl.update(null);

        // Assert
        verify(vendorRepository).update(isNull());
        assertNull(actualUpdateResult);
    }


    @Test
    void testDelete() {
        // Arrange
        when(vendorRepository.deleteById(Mockito.<UUID>any())).thenReturn(null);

        // Act
        Vendor actualDeleteResult = vendorServiceImpl.delete(UUID.randomUUID());

        // Assert
        verify(vendorRepository).deleteById(isA(UUID.class));
        assertNull(actualDeleteResult);
    }


    @Test
    void testFindByFuzzyName() {
        // Arrange
        ArrayList<Vendor> vendorList = new ArrayList<>();
        when(vendorRepository.findByFuzzyName(Mockito.<String>any())).thenReturn(vendorList);

        // Act
        List<Vendor> actualFindByFuzzyNameResult = vendorServiceImpl.findByFuzzyName("Vendor Name");

        // Assert
        verify(vendorRepository).findByFuzzyName("Vendor Name");
        assertTrue(actualFindByFuzzyNameResult.isEmpty());
        assertSame(vendorList, actualFindByFuzzyNameResult);
    }
}
