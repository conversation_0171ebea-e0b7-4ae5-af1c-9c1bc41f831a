package com.mercaso.ims.utils.email;

import com.mercaso.ims.domain.email.Email;
import com.mercaso.ims.domain.email.enums.EmailType;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.UUID;

public class EmailTestUtil {

    /**
     * Build an Email for testing purposes
     */
    public static Email buildEmail() {
        return buildEmail("Vendor", UUID.randomUUID(), EmailType.WORK);
    }

    /**
     * Build an Email with specific entity type and entity ID
     */
    public static Email buildEmail(String entityType, UUID entityId) {
        return buildEmail(entityType, entityId, EmailType.WORK);
    }

    /**
     * Build an Email with specific entity type, entity ID and email type
     */
    public static Email buildEmail(String entityType, UUID entityId, EmailType emailType) {
        return Email.builder()
            .entityType(entityType)
            .entityId(entityId)
            .emailType(emailType)
            .email("test" + RandomStringUtils.randomNumeric(3) + "@example.com")
            .extension("ext" + RandomStringUtils.randomNumeric(3))
            .build();
    }

    /**
     * Build an Email with specific email address
     */
    public static Email buildEmail(String email) {
        return Email.builder()
            .entityType("Vendor")
            .entityId(UUID.randomUUID())
            .emailType(EmailType.WORK)
            .email(email)
            .extension("ext101")
            .build();
    }

    /**
     * Build an Email with specific entity type and email type
     */
    public static Email buildEmail(String entityType, EmailType emailType) {
        return buildEmail(entityType, UUID.randomUUID(), emailType);
    }
}
