package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.ItemCostChangeRequestListDto;
import com.mercaso.ims.application.dto.ItemCostCollectionListDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Component
public class SearchItemCostCollectionRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_ITEM_COST_COLLECTION_URL = "/v1/search/item-cost-collection";
    private static final String SEARCH_ITEM_COST_COLLECTION_REQUEST_URL = "/v1/search/item-cost-collection/item-cost-request";

    public SearchItemCostCollectionRestApiUtil(Environment environment) {
        super(environment);
    }

    public ItemCostCollectionListDto searchItemCostCollection(Instant createdStartDate, Instant createdEndDate, UUID vendorId)
        throws Exception {

        List<String> queryParams = new ArrayList<>();

        if (!ObjectUtils.isEmpty(createdStartDate)) {
            queryParams.add("createdAtBegin=" + createdStartDate);
        }
        if (!ObjectUtils.isEmpty(createdEndDate)) {
            queryParams.add("createdAtEnd=" + createdEndDate);
        }
        if (!ObjectUtils.isEmpty(vendorId)) {
            queryParams.add("vendorId=" + vendorId);
        }

        String queryString = queryParams.isEmpty() ? "" : "?" + String.join("&", queryParams);

        return getEntity(SEARCH_ITEM_COST_COLLECTION_URL + queryString, ItemCostCollectionListDto.class);

    }

    public ItemCostChangeRequestListDto searchItemCostRequestList(UUID itemCostCollectionId)
        throws Exception {

        List<String> queryParams = new ArrayList<>();

        if (!ObjectUtils.isEmpty(itemCostCollectionId)) {
            queryParams.add("itemCostCollectionId=" + itemCostCollectionId);
        }

        String queryString = queryParams.isEmpty() ? "" : "?" + String.join("&", queryParams);

        return getEntity(SEARCH_ITEM_COST_COLLECTION_REQUEST_URL + queryString, ItemCostChangeRequestListDto.class);

    }
}
