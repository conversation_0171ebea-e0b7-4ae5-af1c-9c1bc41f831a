package com.mercaso.ims.utils.difyworkflowrecord;

import com.mercaso.ims.domain.difyworkflowsrecord.DifyWorkflowRecord;
import java.time.Instant;
import java.util.UUID;

public class DifyWorkflowRecordTestUtil {

    public static DifyWorkflowRecord buildDifyWorkflowRecord(UUID itemId, String skuNumber) {
        return DifyWorkflowRecord.builder()
            .id(UUID.randomUUID())
            .input("input")
            .totalTokens(1000L)
            .totalSteps(5)
            .elapsedTime(1.5)
            .workflowId(UUID.randomUUID())
            .result("Test results")
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .createdBy("SYSTEM")
            .build();
    }

    public static DifyWorkflowRecord buildDifyWorkflowRecord(String skuNumber) {
        return buildDifyWorkflowRecord(UUID.randomUUID(), skuNumber);
    }
} 