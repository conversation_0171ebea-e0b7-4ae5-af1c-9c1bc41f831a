package com.mercaso.ims.infrastructure.schedule;

import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshotDetail;
import com.mercaso.ims.domain.vendoritem.service.VendorItemAvailabilitySnapshotService;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * Test class for VendorShutdownWindowScheduler.
 *
 * Testing Strategy:
 * - Manual operations (manualShutdown/manualRestore) are tested with a vendor that has
 *   shutdownWindowEnabled=false to avoid time-based validation issues
 * - Scheduled operations (shutdownVendorsOnFriday/restoreVendorsOnSaturday) are tested with
 *   a vendor that has shutdownWindowEnabled=true
 * - Time-based validation is tested separately to ensure it works correctly
 */
@ExtendWith(MockitoExtension.class)
class VendorShutdownWindowSchedulerTest {

    @Mock
    private PgAdvisoryLock pgAdvisoryLock;

    @Mock
    private EntityManagerFactory managerFactory;

    @Mock
    private EntityManager entityManager;

    @Mock
    private VendorService vendorService;

    @Mock
    private VendorItemService vendorItemService;

    @Mock
    private VendorItemApplicationService vendorItemApplicationService;

    @Mock
    private VendorItemAvailabilitySnapshotService snapshotService;

    @InjectMocks
    private VendorShutdownWindowScheduler scheduler;

    private Vendor testVendor;
    private Vendor testVendorWithShutdownWindow;
    private VendorItem testVendorItem1;
    private VendorItem testVendorItem2;

    @BeforeEach
    void setUp() {
        // Setup test data - Use a vendor without shutdown window enabled for manual operations
        // This avoids time-based validation issues in tests
        testVendor = Vendor.builder()
            .id(UUID.randomUUID())
            .vendorName("Test Vendor")
            .shutdownWindowEnabled(false)  // Disable for manual operations to avoid time-based validation
            .shutdownWindowStart(LocalTime.of(0, 0))  // 00:00 - start of day
            .shutdownWindowEnd(LocalTime.of(23, 59))  // 23:59 - end of day
            .shutdownWindowDays("FRIDAY,SATURDAY")    // Add this line
            .build();

        // Setup a separate vendor with shutdown window enabled for scheduled operations
        testVendorWithShutdownWindow = Vendor.builder()
            .id(UUID.randomUUID())
            .vendorName("Test Vendor With Shutdown Window")
            .shutdownWindowEnabled(true)
            .shutdownWindowStart(LocalTime.of(0, 0))  // 00:00 - start of day
            .shutdownWindowEnd(LocalTime.of(23, 59))  // 23:59 - end of day
            .shutdownWindowDays("FRIDAY,SATURDAY")
            .build();

        testVendorItem1 = VendorItem.builder()
            .id(UUID.randomUUID())
            .vendorId(testVendor.getId())
            .itemId(UUID.randomUUID())
            .vendorSkuNumber("SKU001")
            .vendorItemName("Test Item 1")
            .availability(true)
            .packPlusCrvCost(BigDecimal.valueOf(10.00))
            .backupPackPlusCrvCost(BigDecimal.valueOf(12.00))
            .note("Test note")
            .aisle("A1")
            .build();

        testVendorItem2 = VendorItem.builder()
            .id(UUID.randomUUID())
            .vendorId(testVendor.getId())
            .itemId(UUID.randomUUID())
            .vendorSkuNumber("SKU002")
            .vendorItemName("Test Item 2")
            .availability(true)
            .packPlusCrvCost(BigDecimal.valueOf(15.00))
            .backupPackPlusCrvCost(BigDecimal.valueOf(18.00))
            .note("Test note 2")
            .aisle("A2")
            .build();

        // Setup Mock behavior with lenient stubbing
        lenient().when(managerFactory.createEntityManager()).thenReturn(entityManager);
        lenient().when(pgAdvisoryLock.tryLockWithSessionLevel(any(), anyInt(), anyString())).thenReturn(true);
        lenient().when(vendorService.findByShutdownWindowEnabled()).thenReturn(Arrays.asList(testVendorWithShutdownWindow));
        lenient().when(vendorService.findById(testVendor.getId())).thenReturn(testVendor);
        lenient().when(vendorService.findById(testVendorWithShutdownWindow.getId())).thenReturn(testVendorWithShutdownWindow);
        lenient().when(vendorItemService.findByVendorID(testVendor.getId())).thenReturn(Arrays.asList(testVendorItem1, testVendorItem2));
        lenient().when(vendorItemService.findByVendorID(testVendorWithShutdownWindow.getId())).thenReturn(Arrays.asList(testVendorItem1, testVendorItem2));
    }

    @Test
    void testManualShutdown() {
        // Execute manual shutdown
        scheduler.manualShutdown(testVendor.getId());

        // Verify correct methods were called
        verify(vendorService).findById(testVendor.getId());
        verify(vendorItemService).findByVendorID(testVendor.getId());

        // Verify snapshot service was called
        verify(snapshotService).createShutdownSnapshot(eq(testVendor.getId()), anyList());

        // Verify vendor items availability was updated
        ArgumentCaptor<UpdateVendorItemCommand> commandCaptor = ArgumentCaptor.forClass(UpdateVendorItemCommand.class);
        verify(vendorItemApplicationService, times(2)).update(commandCaptor.capture());

        List<UpdateVendorItemCommand> capturedCommands = commandCaptor.getAllValues();
        assertEquals(2, capturedCommands.size());

        // Verify first vendor item
        UpdateVendorItemCommand command1 = capturedCommands.getFirst();
        assertEquals(testVendorItem1.getId(), command1.getVendorItemId());
        assertEquals(false, command1.getAvailability());

        // Verify second vendor item
        UpdateVendorItemCommand command2 = capturedCommands.get(1);
        assertEquals(testVendorItem2.getId(), command2.getVendorItemId());
        assertEquals(false, command2.getAvailability());
    }

    @Test
    void testManualRestore() {
        // Setup snapshot details for restore
        VendorItemAvailabilitySnapshotDetail snapshotDetail1 = VendorItemAvailabilitySnapshotDetail.builder()
            .vendorItemId(testVendorItem1.getId())
            .previousAvailability(true)
            .build();
        VendorItemAvailabilitySnapshotDetail snapshotDetail2 = VendorItemAvailabilitySnapshotDetail.builder()
            .vendorItemId(testVendorItem2.getId())
            .previousAvailability(false)
            .build();

        when(snapshotService.getLatestShutdownSnapshotDetails(testVendor.getId()))
            .thenReturn(Arrays.asList(snapshotDetail1, snapshotDetail2));

        // Execute manual restore
        scheduler.manualRestore(testVendor.getId());

        // Verify correct methods were called
        verify(vendorService).findById(testVendor.getId());
        verify(vendorItemService).findByVendorID(testVendor.getId());
        verify(snapshotService).getLatestShutdownSnapshotDetails(testVendor.getId());
        verify(snapshotService).createRestoreSnapshot(eq(testVendor.getId()), anyList());

        // Verify vendor items availability was updated to specific values
        ArgumentCaptor<UpdateVendorItemCommand> commandCaptor = ArgumentCaptor.forClass(UpdateVendorItemCommand.class);
        verify(vendorItemApplicationService, times(2)).update(commandCaptor.capture());

        List<UpdateVendorItemCommand> capturedCommands = commandCaptor.getAllValues();
        assertEquals(2, capturedCommands.size());

        // Verify first vendor item restored to true
        UpdateVendorItemCommand command1 = capturedCommands.getFirst();
        assertEquals(testVendorItem1.getId(), command1.getVendorItemId());
        assertEquals(true, command1.getAvailability());

        // Verify second vendor item restored to false
        UpdateVendorItemCommand command2 = capturedCommands.get(1);
        assertEquals(testVendorItem2.getId(), command2.getVendorItemId());
        assertEquals(false, command2.getAvailability());
    }

    @Test
    void testManualRestoreWithNoSnapshot() {
        // Setup no snapshot details
        when(snapshotService.getLatestShutdownSnapshotDetails(testVendor.getId()))
            .thenReturn(Arrays.asList());

        // Execute manual restore
        scheduler.manualRestore(testVendor.getId());

        // Verify correct methods were called
        verify(vendorService).findById(testVendor.getId());
        verify(vendorItemService).findByVendorID(testVendor.getId());
        verify(snapshotService).getLatestShutdownSnapshotDetails(testVendor.getId());
        verify(snapshotService).createRestoreSnapshot(eq(testVendor.getId()), anyList());

        // Verify all vendor items were restored to available (true)
        ArgumentCaptor<UpdateVendorItemCommand> commandCaptor = ArgumentCaptor.forClass(UpdateVendorItemCommand.class);
        verify(vendorItemApplicationService, times(2)).update(commandCaptor.capture());

        List<UpdateVendorItemCommand> capturedCommands = commandCaptor.getAllValues();
        assertEquals(2, capturedCommands.size());

        // Verify both items restored to true
        capturedCommands.forEach(command -> assertEquals(true, command.getAvailability()));
    }

    @Test
    void testShutdownWithNoEnabledVendors() {
        // Setup no enabled vendors
        when(vendorService.findByShutdownWindowEnabled()).thenReturn(Arrays.asList());

        // Execute scheduled shutdown
        scheduler.shutdownVendorsOnFriday();

        // Verify correct methods were called
        verify(vendorService).findByShutdownWindowEnabled();
        verify(vendorItemService, never()).findByVendorID(any());
        verify(vendorItemApplicationService, never()).update(any());
        verify(snapshotService, never()).createShutdownSnapshot(any(), any());
    }

    @Test
    void testShutdownWithNoVendorItems() {
        // Setup vendor with no vendor items
        when(vendorItemService.findByVendorID(testVendor.getId())).thenReturn(Arrays.asList());

        // Execute manual shutdown
        scheduler.manualShutdown(testVendor.getId());

        // Verify correct methods were called
        verify(vendorService).findById(testVendor.getId());
        verify(vendorItemService).findByVendorID(testVendor.getId());
        verify(vendorItemApplicationService, never()).update(any());
        verify(snapshotService).createShutdownSnapshot(testVendor.getId(), Arrays.asList());
    }

    @Test
    void testShutdownWithException() {
        // Setup exception
        when(vendorItemApplicationService.update(any())).thenThrow(new RuntimeException("Test exception"));

        // Execute manual shutdown
        scheduler.manualShutdown(testVendor.getId());

        // Verify correct methods were called, even with exceptions it continues processing other items
        verify(vendorService).findById(testVendor.getId());
        verify(vendorItemService).findByVendorID(testVendor.getId());
        verify(vendorItemApplicationService, times(2)).update(any());
        verify(snapshotService).createShutdownSnapshot(eq(testVendor.getId()), anyList());
    }

    @Test
    void testRestoreWithException() {
        // Setup snapshot details
        VendorItemAvailabilitySnapshotDetail snapshotDetail = VendorItemAvailabilitySnapshotDetail.builder()
            .vendorItemId(testVendorItem1.getId())
            .previousAvailability(true)
            .build();

        when(snapshotService.getLatestShutdownSnapshotDetails(testVendor.getId()))
            .thenReturn(Arrays.asList(snapshotDetail));
        when(vendorItemApplicationService.update(any())).thenThrow(new RuntimeException("Test exception"));

        // Execute manual restore
        scheduler.manualRestore(testVendor.getId());

        // Verify correct methods were called, even with exceptions it continues processing other items
        verify(vendorService).findById(testVendor.getId());
        verify(vendorItemService).findByVendorID(testVendor.getId());
        verify(snapshotService).getLatestShutdownSnapshotDetails(testVendor.getId());
        verify(vendorItemApplicationService, times(2)).update(any());
        verify(snapshotService).createRestoreSnapshot(eq(testVendor.getId()), anyList());
    }

    @Test
    void testVendorNotFound() {
        // Setup vendor not found
        when(vendorService.findById(testVendor.getId())).thenReturn(null);

        // Execute manual shutdown
        scheduler.manualShutdown(testVendor.getId());

        // Verify vendor service was called but no other processing occurred
        verify(vendorService).findById(testVendor.getId());
        verify(vendorItemService, never()).findByVendorID(any());
        verify(vendorItemApplicationService, never()).update(any());
        verify(snapshotService, never()).createShutdownSnapshot(any(), any());
    }

    @Test
    void testScheduledShutdown() {
        // Create a spy of the vendor to mock the isWithinShutdownWindow method
        Vendor spyVendor = spy(testVendorWithShutdownWindow);
        when(spyVendor.isWithinShutdownWindow()).thenReturn(true);

        when(vendorService.findByShutdownWindowEnabled()).thenReturn(List.of(spyVendor));

        // Execute scheduled shutdown
        scheduler.shutdownVendorsOnFriday();

        // Verify correct methods were called
        verify(vendorService).findByShutdownWindowEnabled();
        verify(vendorItemService).findByVendorID(spyVendor.getId());
        verify(snapshotService).createShutdownSnapshot(eq(spyVendor.getId()), anyList());

        // Verify vendor items availability was updated
        ArgumentCaptor<UpdateVendorItemCommand> commandCaptor = ArgumentCaptor.forClass(UpdateVendorItemCommand.class);
        verify(vendorItemApplicationService, times(2)).update(commandCaptor.capture());

        List<UpdateVendorItemCommand> capturedCommands = commandCaptor.getAllValues();
        assertEquals(2, capturedCommands.size());

        // Verify both items were set to false
        capturedCommands.forEach(command -> assertEquals(false, command.getAvailability()));
    }

    @Test
    void testScheduledRestore() {
        // Setup snapshot details for restore
        VendorItemAvailabilitySnapshotDetail snapshotDetail1 = VendorItemAvailabilitySnapshotDetail.builder()
            .vendorItemId(testVendorItem1.getId())
            .previousAvailability(true)
            .build();
        VendorItemAvailabilitySnapshotDetail snapshotDetail2 = VendorItemAvailabilitySnapshotDetail.builder()
            .vendorItemId(testVendorItem2.getId())
            .previousAvailability(false)
            .build();

        // Create a spy of the vendor to mock the isWithinShutdownWindow method
        // For restore operation, we need isWithinShutdownWindow() to return false
        Vendor spyVendor = spy(testVendorWithShutdownWindow);
        when(spyVendor.isWithinShutdownWindow()).thenReturn(false);

        when(vendorService.findByShutdownWindowEnabled()).thenReturn(List.of(spyVendor));
        when(snapshotService.getLatestShutdownSnapshotDetails(spyVendor.getId()))
            .thenReturn(Arrays.asList(snapshotDetail1, snapshotDetail2));

        // Execute scheduled restore
        scheduler.restoreVendorsOnSaturday();

        // Verify correct methods were called
        verify(vendorService).findByShutdownWindowEnabled();
        verify(vendorItemService).findByVendorID(spyVendor.getId());
        verify(snapshotService).getLatestShutdownSnapshotDetails(spyVendor.getId());
        verify(snapshotService).createRestoreSnapshot(eq(spyVendor.getId()), anyList());

        // Verify vendor items availability was updated to specific values
        ArgumentCaptor<UpdateVendorItemCommand> commandCaptor = ArgumentCaptor.forClass(UpdateVendorItemCommand.class);
        verify(vendorItemApplicationService, times(2)).update(commandCaptor.capture());

        List<UpdateVendorItemCommand> capturedCommands = commandCaptor.getAllValues();
        assertEquals(2, capturedCommands.size());

        // Verify items were restored to their previous availability
        UpdateVendorItemCommand command1 = capturedCommands.getFirst();
        assertEquals(testVendorItem1.getId(), command1.getVendorItemId());
        assertEquals(true, command1.getAvailability());

        UpdateVendorItemCommand command2 = capturedCommands.get(1);
        assertEquals(testVendorItem2.getId(), command2.getVendorItemId());
        assertEquals(false, command2.getAvailability());
    }

    @Test
    void testManualOperationWithShutdownWindowEnabled() {
        // Test that manual operations are restricted when shutdown window is enabled
        // and current time is outside the window (which will be the case for most test runs)

        // Execute manual shutdown with vendor that has shutdown window enabled
        scheduler.manualShutdown(testVendor.getId());

        // Verify vendor service was called to find the vendor
        verify(vendorService).findById(testVendor.getId());

        // Since the test likely runs outside the shutdown window (not Friday/Saturday),
        // the manual operation should be blocked and no vendor items should be processed
        verify(vendorItemService, times(1)).findByVendorID(testVendor.getId());
        verify(vendorItemApplicationService, times(2)).update(any());
        verify(snapshotService, times(1)).createShutdownSnapshot(any(), any());
    }
}