package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.CategoryTreeDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class SearchCategoryRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_CATEGORY_REQUEST_URL = "/v1/search/category";

    public SearchCategoryRestApiUtil(Environment environment) {
        super(environment);
    }

    public List<CategoryTreeDto> searchDepartmentCategoryTrees() throws Exception {
        return getEntityList(SEARCH_CATEGORY_REQUEST_URL + "/department-tree", CategoryTreeDto.class);
    }
}