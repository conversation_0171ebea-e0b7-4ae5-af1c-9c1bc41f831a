package com.mercaso.ims.utils.itemregprice;

import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.enums.ItemRegPriceStatus;
import com.mercaso.ims.infrastructure.repository.itemregprice.jpa.dataobject.ItemRegPriceDo;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.UUID;

public class ItemRegPriceUtil {

    public static ItemRegPrice buildItemRegPrice(UUID itemId) {
        return ItemRegPrice.builder()
            .itemId(itemId)
            .regPrice(new BigDecimal("2.3"))
            .regPriceIndividual(new BigDecimal("2.3"))
            .regPricePlusCrv(new BigDecimal("2.3"))
            .itemRegPriceStatus(ItemRegPriceStatus.ACTIVE)
            .build();
    }

    public static ItemRegPriceDo buildItemRegPriceDo() {
        ItemRegPriceDo itemRegPriceDo = new ItemRegPriceDo();
        itemRegPriceDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemRegPriceDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemRegPriceDo.setCreatedUserName("janedoe");
        itemRegPriceDo.setCrv(new BigDecimal("2.3"));
        itemRegPriceDo.setCrvFlag(true);
        itemRegPriceDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemRegPriceDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemRegPriceDo.setDeletedUserName("janedoe");
        itemRegPriceDo.setId(UUID.randomUUID());
        itemRegPriceDo.setItemId(UUID.randomUUID());
        itemRegPriceDo.setItemRegPriceStatus(ItemRegPriceStatus.ACTIVE);
        itemRegPriceDo.setRegPrice(new BigDecimal("2.3"));
        itemRegPriceDo.setRegPriceIndividual(new BigDecimal("2.3"));
        itemRegPriceDo.setRegPricePlusCrv(new BigDecimal("2.3"));
        itemRegPriceDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemRegPriceDo.setUpdatedBy("2020-03-01");
        itemRegPriceDo.setUpdatedUserName("janedoe");
        return itemRegPriceDo;
    }

}
