package com.mercaso.ims.domain.category.service.impl;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.category.CategoryRepository;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {CategoryServiceImpl.class})
class CategoryServiceImplTest extends AbstractTest {

    @MockBean
    private CategoryRepository categoryRepository;

    @Autowired
    private CategoryServiceImpl categoryServiceImpl;


    @Test
    void testFindById() {
        // Arrange
        when(categoryRepository.findById(Mockito.<UUID>any())).thenReturn(null);

        // Act
        Category actualFindByIdResult = categoryServiceImpl.findById(UUID.randomUUID());

        // Assert
        verify(categoryRepository).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }


    @Test
    void testFindByName() {
        // Arrange
        when(categoryRepository.findByName(Mockito.<String>any())).thenReturn(null);

        // Act
        List<Category> actualFindByNameResult = categoryServiceImpl.findByName("Name");

        // Assert
        verify(categoryRepository).findByName("Name");
        assertNull(actualFindByNameResult);
    }


    @Test
    void testFindAllByNameIn_given42_whenArrayListAdd42() {
        // Arrange
        when(categoryRepository.findAllByNameIn(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        ArrayList<String> names = new ArrayList<>();
        names.add("42");
        names.add("foo");

        // Act
        List<Category> actualFindAllByNameInResult = categoryServiceImpl.findAllByNameIn(names);

        // Assert
        verify(categoryRepository).findAllByNameIn(isA(List.class));
        assertTrue(actualFindAllByNameInResult.isEmpty());
    }


    @Test
    void testFindAllByNameIn_givenFoo_whenArrayListAddFoo() {
        // Arrange
        when(categoryRepository.findAllByNameIn(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        ArrayList<String> names = new ArrayList<>();
        names.add("foo");

        // Act
        List<Category> actualFindAllByNameInResult = categoryServiceImpl.findAllByNameIn(names);

        // Assert
        verify(categoryRepository).findAllByNameIn(isA(List.class));
        assertTrue(actualFindAllByNameInResult.isEmpty());
    }


    @Test
    void testFindAllByNameIn_whenArrayList() {
        // Arrange
        when(categoryRepository.findAllByNameIn(Mockito.<List<String>>any())).thenReturn(new ArrayList<>());

        // Act
        List<Category> actualFindAllByNameInResult = categoryServiceImpl.findAllByNameIn(new ArrayList<>());

        // Assert
        verify(categoryRepository).findAllByNameIn(isA(List.class));
        assertTrue(actualFindAllByNameInResult.isEmpty());
    }


    @Test
    void testSave() {
        // Arrange
        when(categoryRepository.save(Mockito.<Category>any())).thenReturn(null);

        // Act
        Category actualSaveResult = categoryServiceImpl.save(null);

        // Assert
        verify(categoryRepository).save(isNull());
        assertNull(actualSaveResult);
    }


    @Test
    void testUpdate() {
        // Arrange
        when(categoryRepository.update(Mockito.<Category>any())).thenReturn(null);

        // Act
        Category actualUpdateResult = categoryServiceImpl.update(null);

        // Assert
        verify(categoryRepository).update(isNull());
        assertNull(actualUpdateResult);
    }


    @Test
    void testDelete() {
        // Arrange
        when(categoryRepository.deleteById(Mockito.<UUID>any())).thenReturn(null);

        // Act
        categoryServiceImpl.delete(UUID.randomUUID());

        // Assert that nothing has changed
        verify(categoryRepository).deleteById(isA(UUID.class));
    }


    @Test
    void testFindByNameAndStatus_NullName() {
        // Arrange
        String name = null;
        CategoryStatus status = CategoryStatus.ACTIVE;

        // Act
        List<Category> result = categoryServiceImpl.findByNameAndStatus(name, status);

        // Assert
        verify(categoryRepository).findByNameAndStatus(name, status);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByNameAndStatus_EmptyName() {
        // Arrange
        String name = "";
        CategoryStatus status = CategoryStatus.ACTIVE;
        when(categoryRepository.findByNameAndStatus(name, status)).thenReturn(new ArrayList<>());

        // Act
        List<Category> result = categoryServiceImpl.findByNameAndStatus(name, status);

        // Assert
        verify(categoryRepository).findByNameAndStatus(name, status);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByNameAndStatus_NullStatus() {
        // Arrange
        String name = "TestCategory";
        CategoryStatus status = null;

        // Act
        List<Category> result = categoryServiceImpl.findByNameAndStatus(name, status);

        // Assert
        verify(categoryRepository).findByNameAndStatus(name, status);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByNameAndStatus_WithResults() {
        // Arrange
        String name = "TestCategory";
        CategoryStatus status = CategoryStatus.ACTIVE;

        List<Category> expectedCategories = new ArrayList<>();
        expectedCategories.add(Category.builder()
            .id(UUID.randomUUID())
            .name(name)
            .status(status)
            .build());

        when(categoryRepository.findByNameAndStatus(name, status)).thenReturn(expectedCategories);

        // Act
        List<Category> result = categoryServiceImpl.findByNameAndStatus(name, status);

        // Assert
        verify(categoryRepository).findByNameAndStatus(name, status);
        Assertions.assertEquals(expectedCategories.size(), result.size());
        Assertions.assertEquals(expectedCategories.getFirst().getId(), result.getFirst().getId());
        Assertions.assertEquals(expectedCategories.getFirst().getName(), result.getFirst().getName());
        Assertions.assertEquals(expectedCategories.getFirst().getStatus(), result.getFirst().getStatus());
    }

    @Test
    void testFindByNameAndStatus_NoResults() {
        // Arrange
        String name = "NonExistentCategory";
        CategoryStatus status = CategoryStatus.ACTIVE;

        when(categoryRepository.findByNameAndStatus(name, status)).thenReturn(new ArrayList<>());

        // Act
        List<Category> result = categoryServiceImpl.findByNameAndStatus(name, status);

        // Assert
        verify(categoryRepository).findByNameAndStatus(name, status);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByNameAndStatus_MultipleResults() {
        // Arrange
        String name = "CommonCategory";
        CategoryStatus status = CategoryStatus.ACTIVE;

        List<Category> expectedCategories = new ArrayList<>();
        expectedCategories.add(Category.builder()
            .id(UUID.randomUUID())
            .name(name)
            .status(status)
            .build());
        expectedCategories.add(Category.builder()
            .id(UUID.randomUUID())
            .name(name)
            .status(status)
            .build());

        when(categoryRepository.findByNameAndStatus(name, status)).thenReturn(expectedCategories);

        // Act
        List<Category> result = categoryServiceImpl.findByNameAndStatus(name, status);

        // Assert
        verify(categoryRepository).findByNameAndStatus(name, status);
        Assertions.assertEquals(expectedCategories.size(), result.size());

        // Verify each category in the result
        for (int i = 0; i < expectedCategories.size(); i++) {
            Assertions.assertEquals(expectedCategories.get(i).getId(), result.get(i).getId());
            Assertions.assertEquals(expectedCategories.get(i).getName(), result.get(i).getName());
            Assertions.assertEquals(expectedCategories.get(i).getStatus(), result.get(i).getStatus());
        }
    }
}
