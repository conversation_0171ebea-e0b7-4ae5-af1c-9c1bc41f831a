package com.mercaso.ims.infrastructure.excel.generator;

import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BulkExportExcelGeneratorTest {

    @Mock
    private DocumentApplicationService documentApplicationService;

    @Mock
    private VendorItemService vendorItemService;

    @Mock
    private Executor taskExecutor;

    @InjectMocks
    private BulkExportExcelGenerator bulkExportExcelGenerator;

    private List<ItemSerachDto> mockItems;
    private List<FinaleAvailableStockDto> mockFinaleData;
    private Map<UUID, Vendor> mockVendorMap;

    private static final UUID FIXED_ITEM_ID = UUID.fromString("12345678-1234-1234-1234-123456789012");
    private static final UUID FIXED_CATEGORY_ID = UUID.fromString("*************-4321-4321-************");
    private static final UUID FIXED_VENDOR_ID = UUID.fromString("11111111-**************-************");

    @BeforeEach
    void setUp() {
        mockItems = createMockItems();
        mockFinaleData = createMockFinaleData();
        mockVendorMap = createMockVendorMap();

        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(taskExecutor).execute(any(Runnable.class));
    }


    @Test
    void shouldGenerateSupplierExportSuccessfully() {
        // Given
        List<VendorItem> mockVendorItems = createMockVendorItems();
        when(vendorItemService.findByItemIds(anyList())).thenReturn(mockVendorItems);

        // When
        byte[] result = bulkExportExcelGenerator.generateBulkExportReport(
                mockItems, Collections.emptyList(), mockVendorMap, ExportType.SUPPLIER
        );

        // Then
        assertNotNull(result);
        assertTrue(result.length > 0);

        verify(vendorItemService).findByItemIds(anyList());
    }

    @Test
    void shouldGenerateAllExportSuccessfully() {
        // Given
        List<VendorItem> mockVendorItems = createMockVendorItems();
        when(vendorItemService.findByItemIds(anyList())).thenReturn(mockVendorItems);
        when(documentApplicationService.getImsUrl(anyString())).thenReturn("http://example.com/photo.jpg");

        // When
        byte[] result = bulkExportExcelGenerator.generateBulkExportReport(
                mockItems, mockFinaleData, mockVendorMap, ExportType.ALL
        );

        // Then
        assertNotNull(result);
        assertTrue(result.length > 0);

        verify(documentApplicationService, atLeastOnce()).getImsUrl(anyString());
        verify(vendorItemService).findByItemIds(anyList());
    }

    @Test
    void shouldHandleSupplierDataMappingWithLargeBatches() {
        List<ItemSerachDto> largeItemList = new ArrayList<>();
        List<VendorItem> mockVendorItems = new ArrayList<>();

        for (int i = 0; i < 2500; i++) {
            UUID itemId = UUID.randomUUID();

            ItemSerachDto item = ItemSerachDto.builder()
                    .id(itemId)
                    .skuNumber("SKU" + i)
                    .title("Test Item " + i)
                    .build();
            largeItemList.add(item);

            VendorItem vendorItem = VendorItem.builder()
                    .id(UUID.randomUUID())
                    .vendorId(FIXED_VENDOR_ID)
                    .itemId(itemId) // 使用对应的itemId
                    .vendorSkuNumber("VENDOR-SKU-" + i)
                    .vendorItemName("Test Vendor Item " + i)
                    .aisle("A1")
                    .packPlusCrvCost(new BigDecimal("5.00"))
                    .backupPackPlusCrvCost(new BigDecimal("5.50"))
                    .vendorItemStatus(VendorItemStatus.ACTIVE)
                    .availability(true)
                    .costFreshnessTime(Instant.now())
                    .backupCostFreshnessTime(Instant.now())
                    .vendorItemType("DIRECT")
                    .build();
            mockVendorItems.add(vendorItem);
        }

        when(vendorItemService.findByItemIds(anyList())).thenReturn(mockVendorItems);

        // When
        byte[] result = bulkExportExcelGenerator.generateBulkExportReport(
                largeItemList, Collections.emptyList(), mockVendorMap, ExportType.SUPPLIER
        );

        // Then
        assertNotNull(result);
        assertTrue(result.length > 0);

        verify(vendorItemService, times(3)).findByItemIds(anyList());
    }

    private List<ItemSerachDto> createMockItems() {
        List<ItemSerachDto> items = new ArrayList<>();

        ItemSerachDto item1 = ItemSerachDto.builder()
                .id(FIXED_ITEM_ID)
                .skuNumber("SKU001")
                .title("Test Item 1")
                .availabilityStatus("ACTIVE")
                .note("Test note")
                .photoName("photo1.jpg")
                .categoryId(FIXED_CATEGORY_ID)
                .brandId(UUID.randomUUID())
                .brandName("Test Brand")
                .primaryVendorId(FIXED_VENDOR_ID)
                .primaryVendorName("Test Vendor")
                .primaryVendorSkuNumber("VENDOR-SKU-001")
                .primaryVendorCost(new BigDecimal("5.00"))
                .regPrice(new BigDecimal("10.00"))
                .regIndividualPrice(new BigDecimal("1.00"))
                .packageSize(10)
                .itemSize("12oz")
                .bottleSize("12oz")
                .eachUpc("123456789012")
                .caseUpc("123456789999")
                .itemTags("tag1,tag2")
                .createdAt(Instant.now())
                .updatedAt(Instant.now())
                .build();

        items.add(item1);
        return items;
    }

    private List<FinaleAvailableStockDto> createMockFinaleData() {
        List<FinaleAvailableStockDto> finaleData = new ArrayList<>();

        FinaleAvailableStockDto stockDto = new FinaleAvailableStockDto();
        stockDto.setSku("SKU001");
        stockDto.setShopifyQoh(100);
        stockDto.setMfcQoh(50);
        stockDto.setReservationsQoh(10);

        finaleData.add(stockDto);
        return finaleData;
    }

    private Map<UUID, Vendor> createMockVendorMap() {
        Map<UUID, Vendor> vendorMap = new HashMap<>();

        Vendor vendor = Vendor.builder()
                .id(FIXED_VENDOR_ID)
                .vendorName("Test Vendor")
                .vendorStatus(VendorStatus.ACTIVE)
                .externalPicking(false)
                .build();

        vendorMap.put(FIXED_VENDOR_ID, vendor);
        return vendorMap;
    }

    private List<VendorItem> createMockVendorItems() {
        List<VendorItem> vendorItems = new ArrayList<>();

        VendorItem vendorItem1 = VendorItem.builder()
                .id(UUID.randomUUID())
                .vendorId(FIXED_VENDOR_ID)
                .itemId(FIXED_ITEM_ID)
                .vendorSkuNumber("VENDOR-SKU-001")
                .vendorItemName("Test Vendor Item")
                .aisle("A1")
                .packPlusCrvCost(new BigDecimal("5.00"))
                .backupPackPlusCrvCost(new BigDecimal("5.50"))
                .vendorItemStatus(VendorItemStatus.ACTIVE)
                .availability(true)
                .costFreshnessTime(Instant.now())
                .backupCostFreshnessTime(Instant.now())
                .vendorItemType("DIRECT")
                .build();

        vendorItems.add(vendorItem1);
        return vendorItems;
    }
}