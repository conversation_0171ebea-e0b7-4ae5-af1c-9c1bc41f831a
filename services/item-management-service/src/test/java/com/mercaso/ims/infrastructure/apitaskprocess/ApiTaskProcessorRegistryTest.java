package com.mercaso.ims.infrastructure.apitaskprocess;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@DisplayName("ApiTaskProcessorRegistry Unit Tests")
class ApiTaskProcessorRegistryTest {

    @Mock
    private ApiTaskProcessor<String> finaleProcessor1;

    @Mock
    private ApiTaskProcessor<String> finaleProcessor2;

    @Mock
    private ApiTaskProcessor<String> universalProcessor;

    @Mock
    private ApiTaskProcessor<String> annotationProcessor;

    @Mock
    private ApiTaskProcessor<String> dynamicProcessor;

    private ApiTaskProcessorRegistry registry;
    private List<ApiTaskProcessor<?>> processors;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Setup mock processors
        when(finaleProcessor1.getTaskType()).thenReturn("FINALE_GET_PRODUCT");
        when(finaleProcessor1.canProcess(anyString())).thenReturn(false);

        when(finaleProcessor2.getTaskType()).thenReturn("FINALE_UPDATE_INVENTORY");
        when(finaleProcessor2.canProcess(anyString())).thenReturn(false);

        when(universalProcessor.getTaskType()).thenReturn("UNIVERSAL_PROCESSOR");
        when(universalProcessor.canProcess(anyString())).thenReturn(false);

        when(annotationProcessor.getTaskType()).thenReturn("ANNOTATION_PROCESSOR");
        when(annotationProcessor.canProcess(anyString())).thenReturn(false);

        when(dynamicProcessor.getTaskType()).thenReturn("DYNAMIC_PROCESSOR");
        when(dynamicProcessor.canProcess(anyString())).thenAnswer(invocation -> {
            String taskType = invocation.getArgument(0);
            return taskType.startsWith("DYNAMIC_");
        });

        processors = Arrays.asList(
                finaleProcessor1,
                finaleProcessor2,
                universalProcessor,
                annotationProcessor,
                dynamicProcessor
        );

        registry = new ApiTaskProcessorRegistry(processors);
    }

    @Test
    @DisplayName("Should find processor by exact task type match")
    void findProcessorForTaskType_ExactMatch_ShouldReturnCorrectProcessor() {
        // Given
        registry.initializeRegistry();

        // When
        ApiTaskProcessor<?> processor = registry.findProcessorForTaskType("FINALE_GET_PRODUCT");

        // Then
        assertNotNull(processor);
        assertEquals(finaleProcessor1, processor);
        assertEquals("FINALE_GET_PRODUCT", processor.getTaskType());
    }

    @Test
    @DisplayName("Should find processor using dynamic matching when exact match fails")
    void findProcessorForTaskType_DynamicMatch_ShouldReturnDynamicProcessor() {
        // Given
        registry.initializeRegistry();

        // When
        ApiTaskProcessor processor = registry.findProcessorForTaskType("DYNAMIC_CUSTOM_TASK");

        // Then
        assertNotNull(processor);
        assertEquals(dynamicProcessor, processor);
        verify(dynamicProcessor).canProcess("DYNAMIC_CUSTOM_TASK");
    }

    @Test
    @DisplayName("Should return null when no processor found")
    void findProcessorForTaskType_NoMatch_ShouldReturnNull() {
        // Given
        registry.initializeRegistry();

        // When
        ApiTaskProcessor processor = registry.findProcessorForTaskType("UNKNOWN_TASK_TYPE");

        // Then
        assertNull(processor);
    }

    @Test
    @DisplayName("Should get processor by exact task type")
    void getProcessor_ExistingTaskType_ShouldReturnProcessor() {
        // Given
        registry.initializeRegistry();

        // When
        ApiTaskProcessor processor = registry.getProcessor("FINALE_UPDATE_INVENTORY");

        // Then
        assertNotNull(processor);
        assertEquals(finaleProcessor2, processor);
    }

    @Test
    @DisplayName("Should return null for non-existing task type")
    void getProcessor_NonExistingTaskType_ShouldReturnNull() {
        // Given
        registry.initializeRegistry();

        // When
        ApiTaskProcessor processor = registry.getProcessor("NON_EXISTING_TYPE");

        // Then
        assertNull(processor);
    }

    @Test
    @DisplayName("Should check if processor exists using exact and dynamic matching")
    void hasProcessor_ExistingTaskType_ShouldReturnTrue() {
        // Given
        registry.initializeRegistry();

        // When & Then
        assertTrue(registry.hasProcessor("FINALE_GET_PRODUCT")); // Exact match
        assertTrue(registry.hasProcessor("DYNAMIC_NEW_TASK")); // Dynamic match
        assertFalse(registry.hasProcessor("UNKNOWN_TYPE")); // No match
    }

    @Test
    @DisplayName("Should return all supported task types")
    void getSupportedTaskTypes_ShouldReturnAllRegisteredTypes() {
        // Given
        registry.initializeRegistry();

        // When
        String[] supportedTypes = registry.getSupportedTaskTypes();

        // Then
        assertEquals(5, supportedTypes.length);
        List<String> typesList = Arrays.asList(supportedTypes);
        assertTrue(typesList.contains("FINALE_GET_PRODUCT"));
        assertTrue(typesList.contains("FINALE_UPDATE_INVENTORY"));
        assertTrue(typesList.contains("UNIVERSAL_PROCESSOR"));
        assertTrue(typesList.contains("ANNOTATION_PROCESSOR"));
        assertTrue(typesList.contains("DYNAMIC_PROCESSOR"));
    }

    @Test
    @DisplayName("Should calculate processor statistics correctly")
    void getProcessorStats_ShouldReturnCorrectStatistics() {
        // Given
        registry.initializeRegistry();

        // When
        ApiTaskProcessorRegistry.ProcessorStats stats = registry.getProcessorStats();

        // Then
        assertEquals(5, stats.getTotalProcessors());
        assertEquals(5, stats.getRegisteredProcessors());
        assertEquals(2, stats.getFinaleProcessors()); // FINALE_GET_PRODUCT, FINALE_UPDATE_INVENTORY
        assertEquals(2, stats.getUniversalProcessors()); // UNIVERSAL_PROCESSOR, ANNOTATION_PROCESSOR
    }

    @Test
    @DisplayName("Should handle empty processor list")
    void initializeRegistry_EmptyProcessorList_ShouldHandleGracefully() {
        // Given
        ApiTaskProcessorRegistry emptyRegistry = new ApiTaskProcessorRegistry(Arrays.asList());

        // When
        emptyRegistry.initializeRegistry();

        // Then
        assertNull(emptyRegistry.findProcessorForTaskType("ANY_TYPE"));
        assertFalse(emptyRegistry.hasProcessor("ANY_TYPE"));
        assertEquals(0, emptyRegistry.getSupportedTaskTypes().length);

        ApiTaskProcessorRegistry.ProcessorStats stats = emptyRegistry.getProcessorStats();
        assertEquals(0, stats.getTotalProcessors());
        assertEquals(0, stats.getRegisteredProcessors());
        assertEquals(0, stats.getFinaleProcessors());
        assertEquals(0, stats.getUniversalProcessors());
    }

    @Test
    @DisplayName("Should handle duplicate task types by keeping last processor")
    void initializeRegistry_DuplicateTaskTypes_ShouldKeepLastProcessor() {
        // Given
        ApiTaskProcessor<String> duplicateProcessor = mock(ApiTaskProcessor.class);
        when(duplicateProcessor.getTaskType()).thenReturn("FINALE_GET_PRODUCT"); // Same as finaleProcessor1
        when(duplicateProcessor.canProcess(anyString())).thenReturn(false);

        List<ApiTaskProcessor<?>> processorsWithDuplicate = Arrays.asList(
                finaleProcessor1,
                duplicateProcessor
        );

        ApiTaskProcessorRegistry registryWithDuplicate = new ApiTaskProcessorRegistry(processorsWithDuplicate);

        // When
        registryWithDuplicate.initializeRegistry();

        // Then
        ApiTaskProcessor<?> processor = registryWithDuplicate.getProcessor("FINALE_GET_PRODUCT");
        assertEquals(duplicateProcessor, processor); // Last one wins
    }

    @Test
    @DisplayName("Should prefer exact match over dynamic match")
    void findProcessorForTaskType_ExactAndDynamicMatch_ShouldPreferExact() {
        // Given
        when(dynamicProcessor.canProcess("FINALE_GET_PRODUCT")).thenReturn(true);
        registry.initializeRegistry();

        // When
        ApiTaskProcessor<?> processor = registry.findProcessorForTaskType("FINALE_GET_PRODUCT");

        // Then
        assertEquals(finaleProcessor1, processor); // Exact match preferred
        verify(dynamicProcessor, never()).canProcess("FINALE_GET_PRODUCT"); // Dynamic check not called
    }

    @Test
    @DisplayName("ProcessorStats toString should return formatted string")
    void processorStats_ToString_ShouldReturnFormattedString() {
        // Given
        registry.initializeRegistry();
        ApiTaskProcessorRegistry.ProcessorStats stats = registry.getProcessorStats();

        // When
        String statsString = stats.toString();

        // Then
        String expected = "ApiTaskProcessorRegistry.ProcessorStats(totalProcessors=5, registeredProcessors=5, finaleProcessors=2, universalProcessors=2)";
        assertEquals(expected, statsString);
    }

    @Test
    @DisplayName("Should handle processor with null task type gracefully")
    void initializeRegistry_ProcessorWithNullTaskType_ShouldSkipProcessor() {
        // Given
        ApiTaskProcessor<String> nullTypeProcessor = mock(ApiTaskProcessor.class);
        when(nullTypeProcessor.getTaskType()).thenReturn(null);

        List<ApiTaskProcessor<?>> processorsWithNull = Arrays.asList(
                finaleProcessor1,
                nullTypeProcessor
        );

        ApiTaskProcessorRegistry registryWithNull = new ApiTaskProcessorRegistry(processorsWithNull);

        // When & Then - Should not throw exception
        assertDoesNotThrow(registryWithNull::initializeRegistry);

        // Registry should still contain the valid processor
        assertNotNull(registryWithNull.getProcessor("FINALE_GET_PRODUCT"));
        
        // getProcessorStats should also work correctly with null task type processors
        assertDoesNotThrow(registryWithNull::getProcessorStats);
        
        ApiTaskProcessorRegistry.ProcessorStats stats = registryWithNull.getProcessorStats();
        assertEquals(2, stats.getTotalProcessors()); // Should count all processors including null one
        assertEquals(1, stats.getRegisteredProcessors()); // Should only count registered (non-null) processors
        assertEquals(1, stats.getFinaleProcessors()); // Should count only non-null FINALE_ processors
    }

    @Test
    @DisplayName("Should count finale processors correctly")
    void getProcessorStats_FinaleProcessorCounting_ShouldCountCorrectly() {
        // Given
        ApiTaskProcessor<String> additionalFinaleProcessor = mock(ApiTaskProcessor.class);
        when(additionalFinaleProcessor.getTaskType()).thenReturn("FINALE_CUSTOM_TASK");
        when(additionalFinaleProcessor.canProcess(anyString())).thenReturn(false);

        List<ApiTaskProcessor<?>> processorsWithAdditional = Arrays.asList(
                finaleProcessor1,
                finaleProcessor2,
                additionalFinaleProcessor,
                universalProcessor
        );

        ApiTaskProcessorRegistry registryWithAdditional = new ApiTaskProcessorRegistry(processorsWithAdditional);
        registryWithAdditional.initializeRegistry();

        // When
        ApiTaskProcessorRegistry.ProcessorStats stats = registryWithAdditional.getProcessorStats();

        // Then
        assertEquals(3, stats.getFinaleProcessors()); // Three FINALE_ prefixed processors
    }
}