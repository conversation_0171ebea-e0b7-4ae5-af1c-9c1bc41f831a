package com.mercaso.ims.utils.vendor;

import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import java.time.Instant;
import java.time.LocalTime;
import java.util.UUID;

public class VendorUtil {

    public static VendorDo buildVendorDo(String vendorName) {
        return VendorDo.builder()
            .vendorName(vendorName)
            .externalPicking(true)
            .vendorStatus(VendorStatus.ACTIVE)
            .shutdownWindowEnabled(false)
            .shutdownWindowStart(LocalTime.of(5, 0))
            .shutdownWindowEnd(LocalTime.of(6, 0))
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .createdBy("SYSTEM")
            .build();
    }

    public static Vendor buildVendor(String vendorName) {
        return Vendor.builder()
            .vendorName(vendorName)
            .vendorStatus(VendorStatus.ACTIVE)
            .externalPicking(true)
            .shutdownWindowEnabled(false)
            .shutdownWindowStart(LocalTime.of(5, 0))
            .shutdownWindowEnd(LocalTime.of(6, 0))
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .createdBy("SYSTEM")
            .id(UUID.randomUUID())
            .build();
    }

    public static Vendor buildVendor(UUID id) {
        return Vendor.builder()
            .id(id)
            .vendorName("vendorName")
            .vendorStatus(VendorStatus.ACTIVE)
            .shutdownWindowEnabled(false)
            .shutdownWindowStart(LocalTime.of(5, 0))
            .shutdownWindowEnd(LocalTime.of(6, 0))
            .build();
    }

    public static Vendor buildVendor(UUID id, String name) {
        return Vendor.builder()
            .id(id)
            .vendorName(name)
            .vendorStatus(VendorStatus.ACTIVE)
            .shutdownWindowEnabled(false)
            .shutdownWindowStart(LocalTime.of(5, 0))
            .shutdownWindowEnd(LocalTime.of(6, 0))
            .build();
    }
}
