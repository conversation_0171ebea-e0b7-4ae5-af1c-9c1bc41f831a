package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.ItemCategoryDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemPackingInfoDto;
import com.mercaso.ims.application.dto.QueryItemCategoryListDto;
import com.mercaso.ims.application.query.ItemCategoryQuery;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class QueryItemRestApiUtil extends IntegrationTestRestUtil {


    private static final String QUERY_ITEM_CATEGORY_BY_ID_IN_URL = "/v1/query/item/category/by-id-in";
    private static final String QUERY_ITEM_CATEGORY_BY_ID_SKU_URL = "/v1/query/item/category/by-sku-in";
    private static final String QUERY_ITEM_PACKING_INFO_BY_ID_IN_URL = "/v1/query/item/picking-info/by-id-in";
    private static final String QUERY_ITEM_BY_SKU_URL = "/v1/query/item";
    private static final String QUERY_ITEM_CATEGORY = "/v1/query/item/category/query";
    private static final String QUERY_ITEM_BY_ID_AND_VERSION_URL = "/v1/query/item";
    private static final String QUERY_ITEM_BY_UPC_URL = "/v1/query/item/by-upc";



    public QueryItemRestApiUtil(Environment environment) {
        super(environment);
    }

    public List<ItemCategoryDto> findItemsByIdIn(List<UUID> ids) throws Exception {
        List<String> idStrings = ids.stream().map(UUID::toString).toList();

        String encodedIds = String.join(",", idStrings);

        String url = String.format(QUERY_ITEM_CATEGORY_BY_ID_IN_URL + "?ids=%s", encodedIds);
        return getEntityList(url, ItemCategoryDto.class);
    }

    public List<ItemCategoryDto> findItemsBySkuIn(List<String> skus) throws Exception {
        String encodedSkus = String.join(",", skus);
        String url = String.format(QUERY_ITEM_CATEGORY_BY_ID_SKU_URL + "?skus=%s", encodedSkus);
        return getEntityList(url, ItemCategoryDto.class);
    }

    public List<ItemPackingInfoDto> findItemsPackingInfoByIdIn(List<UUID> ids) throws Exception {
        List<String> idStrings = ids.stream().map(UUID::toString).toList();

        String encodedIds = String.join(",", idStrings);

        String url = String.format(QUERY_ITEM_PACKING_INFO_BY_ID_IN_URL + "?ids=%s", encodedIds);
        return getEntityList(url, ItemPackingInfoDto.class);
    }

    public ItemDto itemDetailRequest(String sku) throws Exception {
        return getEntity(QUERY_ITEM_BY_SKU_URL + "/" + sku, ItemDto.class);
    }

    public QueryItemCategoryListDto queryItemCategoryList(ItemCategoryQuery itemCategoryQuery) throws Exception {

        return postEntity(QUERY_ITEM_CATEGORY, itemCategoryQuery, QueryItemCategoryListDto.class);
    }


    public ItemCategoryDto findByIdAndVersion(UUID id, Integer versionNumber) throws Exception {
        String url = QUERY_ITEM_BY_ID_AND_VERSION_URL + "/" + versionNumber + "/" + id;
        return getEntity(url, ItemCategoryDto.class);
    }

    public List<ItemDto> getItemsByUpc(String upcNumber) throws Exception {
        String url = QUERY_ITEM_BY_UPC_URL + "/" + upcNumber;
        return getEntityList(url, ItemDto.class);
    }

}
