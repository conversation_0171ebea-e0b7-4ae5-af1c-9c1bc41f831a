package com.mercaso.ims.domain.itemversion.service.impl;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.itemversion.ItemVersion;
import com.mercaso.ims.domain.itemversion.ItemVersionRepository;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemVersionServiceImpl.class})
class ItemVersionServiceImplTest extends AbstractTest {

    @MockBean
    private ItemVersionRepository itemVersionRepository;

    @Autowired
    private ItemVersionServiceImpl itemVersionServiceImpl;

    @Test
    void testDelete() {
        // Arrange
        when(itemVersionRepository.deleteById(Mockito.<UUID>any())).thenReturn(null);

        // Act
        com.mercaso.ims.domain.itemversion.ItemVersion actualDeleteResult = itemVersionServiceImpl
            .delete(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(itemVersionRepository).deleteById(isA(UUID.class));
        assertNull(actualDeleteResult);
    }

    @Test
    void testFindBySkuAndVersion() {
        // Arrange
        when(itemVersionRepository.findBySkuAndVersion(Mockito.<String>any(), Mockito.<Integer>any())).thenReturn(null);

        // Act
        ItemVersion actualFindBySkuAndVersionResult = itemVersionServiceImpl.findBySkuAndVersion("42", 1);

        // Assert
        verify(itemVersionRepository).findBySkuAndVersion("42", 1);
        assertNull(actualFindBySkuAndVersionResult);
    }

    @Test
    void testFindByItemIdAndVersion() {
        // Arrange
        when(itemVersionRepository.findByItemIdAndVersion(Mockito.<UUID>any(), Mockito.<Integer>any())).thenReturn(null);

        UUID uuid = UUID.randomUUID();
        // Act
        ItemVersion actualFindBySkuAndVersionResult = itemVersionServiceImpl.findByItemIdAndVersion(uuid, 1);

        // Assert
        verify(itemVersionRepository).findByItemIdAndVersion(uuid, 1);
        assertNull(actualFindBySkuAndVersionResult);
    }


    @Test
    void testSave() {
        // Arrange
        ItemVersion itemVersion = ItemVersion.builder().build();
        UUID itemId = UUID.randomUUID();
        itemVersion.setItemId(itemId);

        when(itemVersionRepository.save(Mockito.<ItemVersion>any())).thenReturn(itemVersion);

        // Act
        ItemVersion actualSaveResult = itemVersionServiceImpl.save(itemVersion);

        // Assert
        verify(itemVersionRepository).save(isA(ItemVersion.class));
        org.junit.jupiter.api.Assertions.assertEquals(itemId, actualSaveResult.getItemId());
    }

    @Test
    void testUpdate() {
        // Arrange
        ItemVersion existingItemVersion = ItemVersion.builder().build();
        UUID itemId = UUID.randomUUID();
        existingItemVersion.setItemId(itemId);
        existingItemVersion.setVersionNumber(1);

        ItemVersion updatedItemVersion = ItemVersion.builder().build();
        updatedItemVersion.setItemId(itemId);
        updatedItemVersion.setVersionNumber(1);

        when(itemVersionRepository.update(Mockito.<ItemVersion>any())).thenReturn(updatedItemVersion);

        // Act
        ItemVersion actualUpdateResult = itemVersionServiceImpl.update(updatedItemVersion);

        // Assert
        verify(itemVersionRepository).update(isA(ItemVersion.class));
        Assertions.assertEquals(1, actualUpdateResult.getVersionNumber());
    }

    @Test
    void testFindByItemId() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemVersion itemVersion = ItemVersion.builder().build();
        itemVersion.setItemId(itemId);

        when(itemVersionRepository.findByItemId(Mockito.<UUID>any())).thenReturn(List.of(itemVersion));

        // Act
        List<ItemVersion> actualFindByItemIdResult = itemVersionServiceImpl.findByItemId(itemId);

        // Assert
        verify(itemVersionRepository).findByItemId(itemId);
        Assertions.assertEquals(itemId, actualFindByItemIdResult.getFirst().getItemId());
    }
}
