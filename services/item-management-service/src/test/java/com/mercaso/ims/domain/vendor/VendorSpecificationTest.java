package com.mercaso.ims.domain.vendor;

import static org.mockito.Mockito.when;

import com.mercaso.ims.utils.vendor.VendorUtil;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class VendorSpecificationTest {

    @Mock
    VendorRepository vendorRepository;
    @InjectMocks
    VendorSpecification vendorSpecification;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testIsSatisfiedBackupVendorBySku_asBackupVendorIsNull() {
        String vendorName = "vendorName";
        Vendor vendor = VendorUtil.buildVendor(vendorName);
        when(vendorRepository.findByVendorName(vendorName)).thenReturn(vendor);

        boolean result = vendorSpecification.isSatisfiedBackupVendor("backupVendorName");
        Assertions.assertEquals(true, result);
    }


    @Test
    void successfullyTestIsSatisfiedBackupVendorBySku_asBackupVendorIs() {
        String vendorName = "vendorName";
        Vendor vendor = VendorUtil.buildVendor(vendorName);
        vendor.setexternalPicking(true);
        when(vendorRepository.findByVendorName(vendorName)).thenReturn(vendor);

        boolean result = vendorSpecification.isSatisfiedBackupVendor("backupVendorName");
        Assertions.assertEquals(true, result);
    }

    @Test
    void testIsSatisfiedBackupVendorById() {
        UUID vendorId = UUID.randomUUID();
        Vendor vendor = VendorUtil.buildVendor(vendorId);
        when(vendorRepository.findById(vendorId)).thenReturn(vendor);

        boolean result = vendorSpecification.isSatisfiedBackupVendor(vendorId);
        Assertions.assertEquals(false, result);
    }

    @Test
    void testIsSatisfiedBackupVendorById_asVendorNoeExist() {
        UUID vendorId = UUID.randomUUID();
        when(vendorRepository.findById(vendorId)).thenReturn(null);

        boolean result = vendorSpecification.isSatisfiedBackupVendor(vendorId);
        Assertions.assertEquals(false, result);
    }


}

