package com.mercaso.ims.utils.exceptionrecord;

import com.mercaso.ims.domain.exceptionrecord.ExceptionRecord;
import com.mercaso.ims.domain.exceptionrecord.enums.EntityType;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordStatus;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordType;
import java.util.UUID;

public class ExceptionRecordUtil {

    public static ExceptionRecord buildExceptionRecord() {
        return ExceptionRecord.builder()
            .businessEventId(UUID.randomUUID())
            .entityId(UUID.randomUUID())
            .entityType(EntityType.ITEM)
            .exceptionType(ExceptionRecordType.PRICE_EXCEPTION)
            .status(ExceptionRecordStatus.PENDING_REVIEW)
            .description("test")
            .build();


    }

    public static ExceptionRecord buildCostExceptionRecord() {
        return ExceptionRecord.builder()
            .businessEventId(UUID.randomUUID())
            .entityId(UUID.randomUUID())
            .entityType(EntityType.VENDOR_ITEM)
            .exceptionType(ExceptionRecordType.PO_COST_EXCEPTION)
            .status(ExceptionRecordStatus.PENDING_REVIEW)
            .description("test")
            .build();


    }
}
