package com.mercaso.ims.utils.itempricegroup;

import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import java.math.BigDecimal;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;

public class ItemPriceGroupUtil {


    public static ItemPriceGroup buildItemPriceGroup(UUID id , String groupName) {
        return ItemPriceGroup.builder()
            .id(id)
            .groupName(groupName)
            .price(BigDecimal.TEN)
            .build();
    }

    public static ItemPriceGroup buildItemPriceGroup(String groupName) {
        return ItemPriceGroup.builder()
            .id(UUID.randomUUID())
            .groupName(groupName)
            .price(BigDecimal.TEN)
            .build();
    }

    public static ItemPriceGroup buildItemPriceGroup() {
        return ItemPriceGroup.builder()
            .id(UUID.randomUUID())
            .groupName(RandomStringUtils.randomNumeric(5))
            .price(BigDecimal.TEN)
            .build();
    }


}
