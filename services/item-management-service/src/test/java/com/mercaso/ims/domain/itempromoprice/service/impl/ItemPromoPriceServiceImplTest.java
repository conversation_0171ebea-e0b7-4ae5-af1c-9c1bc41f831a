package com.mercaso.ims.domain.itempromoprice.service.impl;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPriceRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemPromoPriceServiceImpl.class})
class ItemPromoPriceServiceImplTest extends AbstractTest {

    @MockBean
    private ItemPromoPriceRepository itemPromoPriceRepository;

    @Autowired
    private ItemPromoPriceServiceImpl itemPromoPriceServiceImpl;

    @Test
    void testFindById() {
        // Arrange
        when(itemPromoPriceRepository.findById(Mockito.<UUID>any())).thenReturn(null);

        // Act
        ItemPromoPrice actualFindByIdResult = itemPromoPriceServiceImpl.findById(UUID.randomUUID());

        // Assert
        verify(itemPromoPriceRepository).findById(isA(UUID.class));
        assertNull(actualFindByIdResult);
    }

    @Test
    void testFindByItemId() {
        // Arrange
        when(itemPromoPriceRepository.findByItemId(Mockito.<UUID>any())).thenReturn(new ArrayList<>());

        // Act
        List<ItemPromoPrice> actualFindByItemIdResult = itemPromoPriceServiceImpl.findByItemId(UUID.randomUUID());

        // Assert
        verify(itemPromoPriceRepository).findByItemId(isA(UUID.class));
        assertTrue(actualFindByItemIdResult.isEmpty());
    }

    @Test
    void testSave() {
        // Arrange
        when(itemPromoPriceRepository.save(Mockito.<ItemPromoPrice>any())).thenReturn(null);

        // Act
        ItemPromoPrice actualSaveResult = itemPromoPriceServiceImpl.save(null);

        // Assert
        verify(itemPromoPriceRepository).save(isNull());
        assertNull(actualSaveResult);
    }

    @Test
    void testUpdate() {
        // Arrange
        when(itemPromoPriceRepository.update(Mockito.<ItemPromoPrice>any())).thenReturn(null);

        // Act
        ItemPromoPrice actualUpdateResult = itemPromoPriceServiceImpl.update(null);

        // Assert
        verify(itemPromoPriceRepository).update(isNull());
        assertNull(actualUpdateResult);
    }

    @Test
    void testDelete() {
        // Arrange
        when(itemPromoPriceRepository.deleteById(Mockito.<UUID>any())).thenReturn(null);

        // Act
        ItemPromoPrice actualDeleteResult = itemPromoPriceServiceImpl.delete(UUID.randomUUID());

        // Assert
        verify(itemPromoPriceRepository).deleteById(isA(UUID.class));
        assertNull(actualDeleteResult);
    }

    @Test
    void testFindByItemIds_givenRandomUUID_whenArrayListAddRandomUUID() {
        // Arrange
        when(itemPromoPriceRepository.findByItemIds(Mockito.<List<UUID>>any())).thenReturn(new ArrayList<>());

        ArrayList<UUID> itemIds = new ArrayList<>();
        itemIds.add(UUID.randomUUID());

        // Act
        List<ItemPromoPrice> actualFindByItemIdsResult = itemPromoPriceServiceImpl.findByItemIds(itemIds);

        // Assert
        verify(itemPromoPriceRepository).findByItemIds(isA(List.class));
        assertTrue(actualFindByItemIdsResult.isEmpty());
    }


    @Test
    void testFindByItemIds_givenRandomUUID_whenArrayListAddRandomUUID2() {
        // Arrange
        when(itemPromoPriceRepository.findByItemIds(Mockito.<List<UUID>>any())).thenReturn(new ArrayList<>());

        ArrayList<UUID> itemIds = new ArrayList<>();
        itemIds.add(UUID.randomUUID());
        itemIds.add(UUID.randomUUID());

        // Act
        List<ItemPromoPrice> actualFindByItemIdsResult = itemPromoPriceServiceImpl.findByItemIds(itemIds);

        // Assert
        verify(itemPromoPriceRepository).findByItemIds(isA(List.class));
        assertTrue(actualFindByItemIdsResult.isEmpty());
    }


    @Test
    void testFindByItemIds_whenArrayList() {
        // Arrange
        when(itemPromoPriceRepository.findByItemIds(Mockito.<List<UUID>>any())).thenReturn(new ArrayList<>());

        // Act
        List<ItemPromoPrice> actualFindByItemIdsResult = itemPromoPriceServiceImpl.findByItemIds(new ArrayList<>());

        // Assert
        verify(itemPromoPriceRepository).findByItemIds(isA(List.class));
        assertTrue(actualFindByItemIdsResult.isEmpty());
    }
}
