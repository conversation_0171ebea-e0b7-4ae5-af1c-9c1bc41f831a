package com.mercaso.ims.utils.vendor;

import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemType;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemDo;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

public class VendorItemUtil {

    public static VendorItemDo buildVendorItemDo() {
        return buildVendorItemDo("vendorSkuNumber", UUID.randomUUID(), UUID.randomUUID());
    }

    public static VendorItemDo buildVendorItemDo(String vendorSkuNumber, UUID vendorId, UUID itemId) {
        return VendorItemDo.builder()
            .vendorSkuNumber(vendorSkuNumber)
            .vendorId(vendorId)
            .itemId(itemId)
            .vendorItemName("Vendor Item Name")
            .note("Note")
            .aisle("Aisle")
            .packPlusCrvCost(BigDecimal.valueOf(10.00))
            .build();
    }

    public static VendorItem buildVendorItem() {
        return VendorItem.builder()
            .vendorSkuNumber("vendorSkuNumber")
            .vendorId(UUID.randomUUID())
            .itemId(UUID.randomUUID())
            .vendorItemName("Vendor Item Name")
            .note("Note")
            .aisle("Aisle")
            .packPlusCrvCost(BigDecimal.valueOf(10.00))
            .backupPackPlusCrvCost(BigDecimal.valueOf(10.00))
            .vendorItemType(VendorItemType.DIRECT_JIT.getTypeName())
            .availability(Boolean.TRUE)
            .backupCostFreshnessTime(Instant.now())
            .build();
    }

    public static VendorItem buildVendorItem(String vendorSkuNumber, UUID vendorId, UUID itemId) {
        return VendorItem.builder()
            .vendorSkuNumber(vendorSkuNumber)
            .vendorId(vendorId)
            .vendorItemName("Vendor Item Name")
            .note("Note")
            .aisle("Aisle")
            .itemId(itemId)
            .build();
    }

}
