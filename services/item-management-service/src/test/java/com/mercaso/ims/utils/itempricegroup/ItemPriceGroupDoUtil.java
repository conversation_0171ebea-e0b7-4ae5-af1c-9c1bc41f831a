package com.mercaso.ims.utils.itempricegroup;

import com.mercaso.ims.infrastructure.repository.itempricegroup.jpa.dataobject.ItemPriceGroupDo;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;

public class ItemPriceGroupDoUtil {


    public static ItemPriceGroupDo buildItemPriceGroupDo(UUID id , String groupName) {
        return ItemPriceGroupDo.builder()
            .id(id)
            .groupName(groupName)
            .build();
    }

    public static ItemPriceGroupDo buildItemPriceGroupDo(String groupName) {
        return ItemPriceGroupDo.builder()
            .id(UUID.randomUUID())
            .groupName(groupName)
            .build();
    }

    public static ItemPriceGroupDo buildItemPriceGroupDo() {
        return ItemPriceGroupDo.builder()
            .id(UUID.randomUUID())
            .groupName(RandomStringUtils.randomNumeric(5))
            .build();
    }


}
