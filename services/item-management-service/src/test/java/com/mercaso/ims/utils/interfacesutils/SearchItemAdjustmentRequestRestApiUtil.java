package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestListDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Component
public class SearchItemAdjustmentRequestRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_ITEM_ADJUSTMENT_REQUEST_URL = "/v1/search/item-adjustment-request";

    public SearchItemAdjustmentRequestRestApiUtil(Environment environment) {
        super(environment);
    }

    public ItemAdjustmentRequestListDto searchItemAdjustmentRequest(Instant createdStartDate, Instant createdEndDate, String createdUserName) throws Exception {

        List<String> queryParams = new ArrayList<>();

        if (!ObjectUtils.isEmpty(createdStartDate)) {
            queryParams.add("createdStartDate=" + createdStartDate);
        }
        if (!ObjectUtils.isEmpty(createdEndDate)) {
            queryParams.add("createdEndDate=" + createdEndDate);
        }
        if (!ObjectUtils.isEmpty(createdUserName)) {
            queryParams.add("createdUserName=" + createdUserName);
        }

        String queryString = queryParams.isEmpty() ? "" : "?" + String.join("&", queryParams);


        return getEntity(SEARCH_ITEM_ADJUSTMENT_REQUEST_URL + queryString, ItemAdjustmentRequestListDto.class);

    }
}
