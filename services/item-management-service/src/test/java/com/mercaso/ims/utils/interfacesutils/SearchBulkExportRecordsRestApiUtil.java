package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.BulkExportRecordsSearchDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.time.Instant;

@Component
public class SearchBulkExportRecordsRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_BULK_EXPORT_RECORDS_URL = "/v1/search/bulk-export-records";

    public SearchBulkExportRecordsRestApiUtil(Environment environment) {
        super(environment);
    }

    /**
     * Search bulk export records with default parameters
     */
    public BulkExportRecordsSearchDto searchBulkExportRecords() throws Exception {
        return getEntity(SEARCH_BULK_EXPORT_RECORDS_URL, BulkExportRecordsSearchDto.class);
    }

    /**
     * Search bulk export records with pagination
     */
    public BulkExportRecordsSearchDto searchBulkExportRecords(int page, int pageSize) throws Exception {
        String url = SEARCH_BULK_EXPORT_RECORDS_URL + "?page=" + page + "&pageSize=" + pageSize;
        return getEntity(url, BulkExportRecordsSearchDto.class);
    }

    /**
     * Search bulk export records with user name filter only
     */
    public BulkExportRecordsSearchDto searchBulkExportRecordsByUserName(String createdUserName) throws Exception {
        String url = SEARCH_BULK_EXPORT_RECORDS_URL + "?createdUserName=" + createdUserName;
        return getEntity(url, BulkExportRecordsSearchDto.class);
    }

    /**
     * Search bulk export records with date range only
     */
    public BulkExportRecordsSearchDto searchBulkExportRecordsByDateRange(
            Instant createdStartDate, 
            Instant createdEndDate) throws Exception {
        
        StringBuilder urlBuilder = new StringBuilder(SEARCH_BULK_EXPORT_RECORDS_URL);
        urlBuilder.append("?");
        
        if (createdStartDate != null) {
            urlBuilder.append("createdStartDate=").append(createdStartDate.toString());
        }
        
        if (createdEndDate != null) {
            if (createdStartDate != null) {
                urlBuilder.append("&");
            }
            urlBuilder.append("createdEndDate=").append(createdEndDate.toString());
        }
        
        return getEntity(urlBuilder.toString(), BulkExportRecordsSearchDto.class);
    }

    /**
     * Search bulk export records with all parameters
     */
    public BulkExportRecordsSearchDto searchBulkExportRecordsWithAllParams(
            int page,
            int pageSize,
            Instant createdStartDate,
            Instant createdEndDate,
            String createdUserName) throws Exception {
        
        StringBuilder urlBuilder = new StringBuilder(SEARCH_BULK_EXPORT_RECORDS_URL);
        urlBuilder.append("?page=").append(page);
        urlBuilder.append("&pageSize=").append(pageSize);
        
        if (createdStartDate != null) {
            urlBuilder.append("&createdStartDate=").append(createdStartDate.toString());
        }
        
        if (createdEndDate != null) {
            urlBuilder.append("&createdEndDate=").append(createdEndDate.toString());
        }
        
        if (createdUserName != null && !createdUserName.trim().isEmpty()) {
            urlBuilder.append("&createdUserName=").append(createdUserName);
        }
        
        return getEntity(urlBuilder.toString(), BulkExportRecordsSearchDto.class);
    }
}
