package com.mercaso.ims.utils.attributeenumvalue;

import com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.dataobject.AttributeEnumValueDo;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.UUID;

public class AttributeEnumValueUtil {

    public static AttributeEnumValueDo buildAttributeEnumValue() {
        AttributeEnumValueDo attributeEnumValueDo = new AttributeEnumValueDo();
        attributeEnumValueDo.setAttributeId(UUID.randomUUID());
        attributeEnumValueDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeEnumValueDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        attributeEnumValueDo.setCreatedUserName("janedoe");
        attributeEnumValueDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeEnumValueDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        attributeEnumValueDo.setDeletedUserName("janedoe");
        attributeEnumValueDo.setId(UUID.randomUUID());
        attributeEnumValueDo.setSortOrder(10.0f);
        attributeEnumValueDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        attributeEnumValueDo.setUpdatedBy("2020-03-01");
        attributeEnumValueDo.setUpdatedUserName("janedoe");
        attributeEnumValueDo.setValue("42");
        return attributeEnumValueDo;
    }
}
