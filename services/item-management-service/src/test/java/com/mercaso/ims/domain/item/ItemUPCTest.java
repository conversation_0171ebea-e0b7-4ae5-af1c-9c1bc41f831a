package com.mercaso.ims.domain.item;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ContextConfiguration;


@ContextConfiguration(classes = {ItemUPC.class})
class ItemUPCTest {


    @Test
    void testSameValueAs() {

        // Arrange
        ItemUPC itemUPC = new ItemUPC(mock(ItemUPC.ItemUPCBuilder.class));

        // Act and Assert
        assertTrue(itemUPC.sameValueAs(new ItemUPC(mock(ItemUPC.ItemUPCBuilder.class))));
    }


    @Test
    void testSameValueAs2() {

        // Arrange, Act and Assert
        assertFalse((new ItemUPC(mock(ItemUPC.ItemUPCBuilder.class))).sameValueAs(null));
    }


    @Test
    void testSameValueAs3() {

        // Arrange
        ItemUPC itemUPC = new ItemUPC(mock(ItemUPC.ItemUPCBuilder.class));
        itemUPC.setItemId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemUPC.sameValueAs(new ItemUPC(mock(ItemUPC.ItemUPCBuilder.class))));
    }


    @Test
    void testSameValueAs4() {

        // Arrange
        ItemUPC itemUPC = new ItemUPC(mock(ItemUPC.ItemUPCBuilder.class));

        ItemUPC other = new ItemUPC(mock(ItemUPC.ItemUPCBuilder.class));
        other.setItemId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemUPC.sameValueAs(other));
    }


    @Test
    void testSameValueAs5() {

        // Arrange
        ItemUPC itemUPC = new ItemUPC(mock(ItemUPC.ItemUPCBuilder.class));
        itemUPC.setItemId(UUID.randomUUID());

        ItemUPC other = new ItemUPC(mock(ItemUPC.ItemUPCBuilder.class));
        other.setItemId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemUPC.sameValueAs(other));
    }
}
