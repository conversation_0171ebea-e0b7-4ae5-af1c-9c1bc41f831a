package com.mercaso.ims.interfaces.rest.search;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.CategoryTreeDto;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class SearchCategoryRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenSearchDepartmentCategoryTrees() throws Exception {
        // Act
        List<CategoryTreeDto> result = searchCategoryRestApiUtil.searchDepartmentCategoryTrees();

        // Assert
        Assertions.assertNotNull(result);
    }
}