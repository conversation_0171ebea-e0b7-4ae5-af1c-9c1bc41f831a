package com.mercaso.ims.infrastructure.excel.strategy;

import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemListDto;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.query.ItemQuery;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AbstractBulkExportStrategyTest {

    @Mock
    private ItemQueryApplicationService itemQueryApplicationService;

    @Mock
    private Executor taskExecutor;

    @Mock
    private ItemSearchApplicationService itemSearchApplicationService;

    private TestableAbstractBulkExportStrategy strategy;

    @BeforeEach
    void setUp() {
        // Mock taskExecutor to run tasks synchronously
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run();
            return null;
        }).when(taskExecutor).execute(any(Runnable.class));
        
        strategy = new TestableAbstractBulkExportStrategy(
                itemQueryApplicationService,
                taskExecutor,
                itemSearchApplicationService
        );
    }

    @Test
    void testFetchItemsWithSimpleData_SinglePage() {
        // Given
        String customFilter = "{\"availabilityStatus\":\"active\"}";
        UUID itemId1 = UUID.randomUUID();
        UUID itemId2 = UUID.randomUUID();
        
        List<UUID> itemIds = Arrays.asList(itemId1, itemId2);
        
        ItemDto itemDto1 = ItemDto.builder()
                .id(itemId1)
                .skuNumber("SKU001")
                .build();
        ItemDto itemDto2 = ItemDto.builder()
                .id(itemId2)
                .skuNumber("SKU002")
                .build();
        List<ItemDto> itemDtos = Arrays.asList(itemDto1, itemDto2);

        when(itemSearchApplicationService.searchItemListIds(any(ItemQuery.class)))
                .thenReturn(itemIds);
        when(itemQueryApplicationService.findByIdIn(anyList()))
                .thenReturn(itemDtos);

        // When
        List<ItemSerachDto> result = strategy.testFetchItemsWithSimpleData(customFilter);

        // Then
        assertEquals(2, result.size());
        assertEquals(itemId1, result.get(0).getId());
        assertEquals("SKU001", result.get(0).getSkuNumber());
        assertEquals(itemId2, result.get(1).getId());
        assertEquals("SKU002", result.get(1).getSkuNumber());
    }

    @Test
    void testFetchItemsWithSimpleData_MultipleBatches() {
        // Given
        String customFilter = "{\"brandId\":\"12345-67890\"}";
        List<UUID> itemIds = new ArrayList<>();

        for (int i = 0; i < 300; i++) {
            itemIds.add(UUID.randomUUID());
        }

        when(itemSearchApplicationService.searchItemListIds(any(ItemQuery.class)))
                .thenReturn(itemIds);

        when(itemQueryApplicationService.findByIdIn(anyList()))
                .thenAnswer(invocation -> {
                    List<UUID> ids = invocation.getArgument(0);
                    if (ids == null || ids.isEmpty()) {
                        return Collections.emptyList();
                    }
                    return ids.stream()
                            .map(id -> ItemDto.builder().id(id).skuNumber("SKU_" + id.toString().substring(0, 8)).build())
                            .toList();
                });

        // When
        List<ItemSerachDto> result = strategy.testFetchItemsWithSimpleData(customFilter);

        // Then
        assertEquals(300, result.size());
        verify(itemQueryApplicationService, atLeastOnce()).findByIdIn(anyList());
    }

    @Test
    void testFetchItemsWithSimpleData_ExceptionHandling() {
        // Given
        String customFilter = "{\"skuNumber\":\"TEST-SKU\"}";
        UUID itemId = UUID.randomUUID();
        List<UUID> itemIds = Arrays.asList(itemId);

        when(itemSearchApplicationService.searchItemListIds(any(ItemQuery.class)))
                .thenReturn(itemIds);
        when(itemQueryApplicationService.findByIdIn(anyList()))
                .thenThrow(new RuntimeException("Database error"));

        // When
        List<ItemSerachDto> result = strategy.testFetchItemsWithSimpleData(customFilter);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void testFetchItemsWithSimpleData_InvalidJsonFormat() {
        // Given
        String customFilter = "invalid-json-format";

        // When & Then
        assertThrows(Exception.class, () -> {
            strategy.testFetchItemsWithSimpleData(customFilter);
        });
    }

    @Test
    void testFetchFullItemData_EmptyResult() {
        // Given
        String customFilter = "{\"availabilityStatus\":\"archived\"}";
        ItemListDto emptyItemListDto = ItemListDto.builder()
                .data(Collections.emptyList())
                .build();

        when(itemSearchApplicationService.searchItemListV2(any(ItemQuery.class)))
                .thenReturn(emptyItemListDto);

        // When
        List<ItemSerachDto> result = strategy.testFetchFullItemData(customFilter);

        // Then
        assertTrue(result.isEmpty());
        verify(itemSearchApplicationService, times(1)).searchItemListV2(any(ItemQuery.class));
    }

    @Test
    void testFetchFullItemData_NullCustomFilter() {
        // Given
        String customFilter = null;
        ItemListDto emptyItemListDto = ItemListDto.builder()
                .data(Collections.emptyList())
                .build();

        when(itemSearchApplicationService.searchItemListV2(any(ItemQuery.class)))
                .thenReturn(emptyItemListDto);

        // When
        List<ItemSerachDto> result = strategy.testFetchFullItemData(customFilter);

        // Then
        assertTrue(result.isEmpty());
        verify(itemSearchApplicationService, times(1)).searchItemListV2(any(ItemQuery.class));
    }

    @Test
    void testFetchFullItemData_SinglePage() {
        // Given
        String customFilter = "{\"categoryId\":\"11111-22222\"}";
        ItemSerachDto item1 = ItemSerachDto.builder()
                .id(UUID.randomUUID())
                .skuNumber("SKU001")
                .build();
        ItemSerachDto item2 = ItemSerachDto.builder()
                .id(UUID.randomUUID())
                .skuNumber("SKU002")
                .build();
        
        ItemListDto itemListDto = ItemListDto.builder()
                .data(Arrays.asList(item1, item2))
                .build();

        when(itemSearchApplicationService.searchItemListV2(any(ItemQuery.class)))
                .thenReturn(itemListDto);

        // When
        List<ItemSerachDto> result = strategy.testFetchFullItemData(customFilter);

        // Then
        assertEquals(2, result.size());
        assertEquals(item1.getId(), result.get(0).getId());
        assertEquals(item1.getSkuNumber(), result.get(0).getSkuNumber());
        assertEquals(item2.getId(), result.get(1).getId());
        assertEquals(item2.getSkuNumber(), result.get(1).getSkuNumber());
    }

    @Test
    void testFetchFullItemData_MultiplePages() {
        // Given
        String customFilter = "{\"primaryVendorId\":\"vendor-123\"}";
        
        List<ItemSerachDto> page1Items = new ArrayList<>();
        for (int i = 0; i < 2000; i++) {
            page1Items.add(ItemSerachDto.builder()
                    .id(UUID.randomUUID())
                    .skuNumber("SKU" + String.format("%04d", i))
                    .build());
        }
        ItemListDto page1 = ItemListDto.builder()
                .data(page1Items)
                .build();

        List<ItemSerachDto> page2Items = new ArrayList<>();
        for (int i = 2000; i < 2500; i++) {
            page2Items.add(ItemSerachDto.builder()
                    .id(UUID.randomUUID())
                    .skuNumber("SKU" + String.format("%04d", i))
                    .build());
        }
        ItemListDto page2 = ItemListDto.builder()
                .data(page2Items)
                .build();

        when(itemSearchApplicationService.searchItemListV2(any(ItemQuery.class)))
                .thenReturn(page1, page2);

        // When
        List<ItemSerachDto> result = strategy.testFetchFullItemData(customFilter);

        // Then
        assertEquals(2500, result.size());
        verify(itemSearchApplicationService, times(2)).searchItemListV2(any(ItemQuery.class));
    }

    private static class TestableAbstractBulkExportStrategy extends AbstractBulkExportStrategy {

        protected TestableAbstractBulkExportStrategy(
                ItemQueryApplicationService itemQueryApplicationService,
                Executor taskExecutor,
                ItemSearchApplicationService itemSearchApplicationService) {
            super(itemQueryApplicationService, taskExecutor, itemSearchApplicationService);
        }

        @Override
        public ExportType getExportType() {
            return ExportType.ALL;
        }

        @Override
        public byte[] execute(String customFilter) {
            return "test data".getBytes();
        }

        public List<ItemSerachDto> testFetchItemsWithSimpleData(String customFilter) {
            return fetchItemsWithSimpleData(customFilter);
        }

        public List<ItemSerachDto> testFetchFullItemData(String customFilter) {
            return fetchFullItemData(customFilter);
        }
    }
} 