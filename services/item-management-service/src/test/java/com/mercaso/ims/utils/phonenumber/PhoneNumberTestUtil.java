package com.mercaso.ims.utils.phonenumber;

import com.mercaso.ims.domain.phone.PhoneNumber;
import com.mercaso.ims.domain.phone.enums.PhoneType;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.UUID;

public class PhoneNumberTestUtil {

    /**
     * Build a PhoneNumber for testing purposes
     */
    public static PhoneNumber buildPhoneNumber() {
        return buildPhoneNumber("Vendor", UUID.randomUUID(), PhoneType.WORK);
    }

    /**
     * Build a PhoneNumber with specific entity type and entity ID
     */
    public static PhoneNumber buildPhoneNumber(String entityType, UUID entityId) {
        return buildPhoneNumber(entityType, entityId, PhoneType.WORK);
    }

    /**
     * Build a PhoneNumber with specific entity type, entity ID and phone type
     */
    public static PhoneNumber buildPhoneNumber(String entityType, UUID entityId, PhoneType phoneType) {
        return PhoneNumber.builder()
            .entityType(entityType)
            .entityId(entityId)
            .phoneType(phoneType)
            .phoneNumber("555-" + RandomStringUtils.randomNumeric(3) + "-" + RandomStringUtils.randomNumeric(4))
            .extension(RandomStringUtils.randomNumeric(3))
            .build();
    }

    /**
     * Build a PhoneNumber with specific phone number
     */
    public static PhoneNumber buildPhoneNumber(String phoneNumber) {
        return PhoneNumber.builder()
            .entityType("Vendor")
            .entityId(UUID.randomUUID())
            .phoneType(PhoneType.WORK)
            .phoneNumber(phoneNumber)
            .extension("101")
            .build();
    }

    /**
     * Build a PhoneNumber with specific entity type and phone type
     */
    public static PhoneNumber buildPhoneNumber(String entityType, PhoneType phoneType) {
        return buildPhoneNumber(entityType, UUID.randomUUID(), phoneType);
    }
}
