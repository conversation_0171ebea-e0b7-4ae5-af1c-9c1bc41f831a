package com.mercaso.ims.utils.item;

import com.google.api.client.util.Lists;
import com.mercaso.ims.application.command.BatchUpdateItemStatusCommand;
import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.command.UpdateItemRegPriceCommand;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.item.enums.ItemType;
import com.mercaso.ims.domain.item.enums.PackageType;
import com.mercaso.ims.domain.item.enums.SalesStatus;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

public class ItemCommandUtil {

    UUID id = UUID.randomUUID();
    UUID categoryId = UUID.randomUUID();
    UUID brandId = UUID.randomUUID();
    UUID primaryVendorId = UUID.randomUUID();

    public static CreateItemCommand buildCreateItemCommand() {
        return buildCreateItemCommand(null);
    }

    public static CreateItemCommand buildCreateItemCommand(UUID itemAdjustmentRequestDetailId) {
        return CreateItemCommand.builder()
            .categoryId(UUID.randomUUID())
            .brandId(UUID.randomUUID())
            .primaryVendorId(UUID.randomUUID())
            .name("title")
            .title("title")
            .skuNumber("skuNumber_" + UUID.randomUUID())
            .description("description")
            .note("note")
            .photoName("photo")
            .primaryVendorId(UUID.randomUUID())
            .detail("detail")
            .packageType(PackageType.PACK)
            .packageSize(12)
            .shelfLife("shelfLife")
            .itemType(ItemType.SELF_OPERATED)
            .companyId(10000L)
            .locationId(3000L)
            .handle("handle")
            .itemAdjustmentRequestDetailId(itemAdjustmentRequestDetailId)
            .build();
    }

    public static UpdateItemCommand buildUpdateItemCommand(UUID id, String skuNumber, UUID itemAdjustmentRequestDetailId) {
        return UpdateItemCommand.builder()
            .id(id)
            .categoryId(UUID.randomUUID())
            .brandId(UUID.randomUUID())
            .skuNumber(skuNumber)
            .name("title_update")
            .title("title_update")
            .description("description_update")
            .note("note_update")
            .primaryVendorId(UUID.randomUUID())
            .detail("detail_update")
            .packageType(PackageType.PACK)
            .packageSize(24)
            .shelfLife("shelfLife_update")
            .itemType(ItemType.SELF_OPERATED)
            .companyId(100001L)
            .locationId(30001L)
            .handle("handle_update")
            .itemAdjustmentRequestDetailId(itemAdjustmentRequestDetailId)
            .build();
    }

    public static UpdateItemCommand buildUpdateItemCommand(UUID id, UUID categoryId, UUID brandId, UUID primaryVendorId) {
        return new UpdateItemCommand(id,
            categoryId,
            brandId,
            "Name",
            "Dr",
            "42",
            "The characteristics of someone or something",
            "Note",
            "<EMAIL>",
            "https://cdn.shopify.com/s/files/1/0588/2701/4293/files/DW110400-24.png?v=1723519416",
            primaryVendorId,
            null,
            "Detail",
            PackageType.UNKNOWN,
            3,
            "Shelf Life",
            ItemType.SELF_OPERATED,
            SalesStatus.LISTING,
            AvailabilityStatus.ACTIVE,
            1L,
            1L,
            "Handle",
            UUID.randomUUID(),
            "New Description",
            true,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null, null,null, null, null, null, null);

    }

    public static BatchUpdateItemStatusCommand buildBatchUpdateItemStatusCommand(List<UUID> ids, AvailabilityStatus status) {
        return BatchUpdateItemStatusCommand.builder()
            .itemIds(ids)
            .status(status)
            .build();
    }

    public static List<UpdateItemRegPriceCommand> buildBatchUpdateItemRegPrice(List<UUID> ids) {

        List<UpdateItemRegPriceCommand> commands = Lists.newArrayList();
        ids.forEach(id -> {
            commands.add(UpdateItemRegPriceCommand.builder()
                .itemId(id)
                .regPrice(new BigDecimal(20))
                .build());
        });

        return commands;
    }
}
