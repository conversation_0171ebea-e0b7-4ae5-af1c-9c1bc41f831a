package com.mercaso.ims.utils.exceptionrecord;

import com.mercaso.ims.application.dto.ExceptionRecordDto;
import com.mercaso.ims.domain.exceptionrecord.enums.EntityType;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordStatus;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordType;
import java.util.UUID;

public class ExceptionRecordDtoUtil {

    public static ExceptionRecordDto buildExceptionRecordDto() {
        return ExceptionRecordDto.builder()
            .businessEventId(UUID.randomUUID())
            .entityId(UUID.randomUUID())
            .entityType(EntityType.ITEM)
            .exceptionType(ExceptionRecordType.PRICE_EXCEPTION)
            .status(ExceptionRecordStatus.PENDING_REVIEW)
            .description("test")
            .build();


    }

    public static ExceptionRecordDto buildCostExceptionRecordDto() {
        return ExceptionRecordDto.builder()
            .businessEventId(UUID.randomUUID())
            .entityId(UUID.randomUUID())
            .entityType(EntityType.VENDOR_ITEM)
            .exceptionType(ExceptionRecordType.PO_COST_EXCEPTION)
            .status(ExceptionRecordStatus.PENDING_REVIEW)
            .description("test")
            .build();


    }
}
