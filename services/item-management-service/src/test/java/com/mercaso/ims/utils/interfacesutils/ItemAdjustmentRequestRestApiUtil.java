package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.File;
import java.nio.file.Files;
import java.util.UUID;

@Component
public class ItemAdjustmentRequestRestApiUtil extends IntegrationTestRestUtil {
    private static final String UPLOAD_ITEM_ADJUSTMENT_REQUEST_URL = "/v1/item-adjustment-request/upload";
    private static final String DOWNLOAD_ITEM_ADJUSTMENT_DETAIL = "/v1/item-adjustment-request/{id}/detail/download";

    public ItemAdjustmentRequestRestApiUtil(Environment environment) {
        super(environment);
    }


    public ItemAdjustmentRequestDto uploadItemAdjustmentRequest(File file, ItemAdjustmentRequestType type) throws Exception {
        byte[] fileContent = Files.readAllBytes(file.toPath());
        ByteArrayResource fileResource = new ByteArrayResource(fileContent) {
            @Override
            public String getFilename() {
                return file.getName();
            }
        };
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("requestFile", fileResource);
        body.add("type", type.name());
        return uploadFile(UPLOAD_ITEM_ADJUSTMENT_REQUEST_URL, body, ItemAdjustmentRequestDto.class);
    }

    public DocumentResponse downloadItemAdjustmentDetail(UUID itemAdjustmentRequestId) throws Exception {
        return getEntity(DOWNLOAD_ITEM_ADJUSTMENT_DETAIL.replace("{id}", itemAdjustmentRequestId.toString()), DocumentResponse.class);
    }


}
