package com.mercaso.ims.utils.item;

import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.item.enums.ItemType;
import com.mercaso.ims.domain.item.enums.PackageType;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import java.util.List;
import java.util.UUID;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomStringUtils;

public class ItemDoUtil {

    public static ItemDo buildItemDo(String skuNumber, UUID vendorId, String department,
        String category,
        String subCategory,
        String clazz, UUID brandId, List<ItemUPCDo> itemUPCs) {
        return ItemDo.builder()
            .brandId(null != brandId ? brandId : UUID.randomUUID())
            .skuNumber(skuNumber)
            .detail("detail")
            .availabilityStatus(AvailabilityStatus.ACTIVE)
            .description("description")
            .handle("handle")
            .companyId(1000L)
            .locationId(1001L)
            .itemType(ItemType.SELF_OPERATED)
            .name("name")
            .title(RandomStringUtils.randomAlphabetic(5))
            .packageSize(10)
            .packageType(PackageType.PACK)
            .itemImages(Lists.newArrayList())
            .itemTags(Lists.newArrayList())
            .itemUPCs(itemUPCs)
            .itemAttributes(Lists.newArrayList())
            .primaryVendorId(vendorId)
            .createdBy("createdBy")
            .createdUserName("createdUserName")
            .department(department)
            .category(category)
            .subCategory(subCategory)
            .clazz(clazz)
            .photo(skuNumber + ".jpg")
            .width(1.1)
            .height(1.1)
            .length(1.1)
            .categoryId(UUID.randomUUID())
            .build();
    }
}
