package com.mercaso.ims.utils.item;

import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.payload.ItemCreatedPayloadDto;
import java.util.UUID;

public class ItemCreatedPayloadDtoUtil {

    public static ItemCreatedPayloadDto buildItemCreatedPayloadDto(UUID itemId, UUID itemAdjustmentRequestDetailId) {
        ItemDto item = ItemDTOUtil.buildItemDTO();
        return ItemCreatedPayloadDto.builder()
            .itemId(itemId)
            .itemAdjustmentRequestDetailId(itemAdjustmentRequestDetailId)
            .data(item)
            .build();
    }
}
