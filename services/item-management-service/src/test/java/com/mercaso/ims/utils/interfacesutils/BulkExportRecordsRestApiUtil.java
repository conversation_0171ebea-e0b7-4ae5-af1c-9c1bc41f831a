package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.BulkExportRecordsCommand;
import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class BulkExportRecordsRestApiUtil extends IntegrationTestRestUtil {

    private static final String BULK_EXPORT_RECORDS_URL = "/v1/bulk-export-records";

    public BulkExportRecordsRestApiUtil(Environment environment) {
        super(environment);
    }

    public DocumentResponse getBulkExportRecordsFile(UUID id) throws Exception {
        return getEntity(BULK_EXPORT_RECORDS_URL + "/" + id + "/file", DocumentResponse.class);
    }
}
