package com.mercaso.ims.domain.email;

import com.mercaso.ims.domain.email.enums.EmailType;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class EmailTest {

    @Test
    void testEmailCreation() {
        // Arrange
        UUID id = UUID.randomUUID();
        String entityType = "Vendor";
        UUID entityId = UUID.randomUUID();
        EmailType emailType = EmailType.WORK;
        String email = "<EMAIL>";
        String extension = "ext123";

        // Act
        Email emailObj = Email.builder()
                .id(id)
                .entityType(entityType)
                .entityId(entityId)
                .emailType(emailType)
                .email(email)
                .extension(extension)
                .build();

        // Assert
        assertNotNull(emailObj);
        assertEquals(id, emailObj.getId());
        assertEquals(entityType, emailObj.getEntityType());
        assertEquals(entityId, emailObj.getEntityId());
        assertEquals(emailType, emailObj.getEmailType());
        assertEquals(email, emailObj.getEmail());
        assertEquals(extension, emailObj.getExtension());
    }

    @Test
    void testSameValueAs() {
        // Arrange
        UUID id1 = UUID.randomUUID();
        UUID id2 = UUID.randomUUID();
        String entityType = "Customer";
        UUID entityId = UUID.randomUUID();
        EmailType emailType = EmailType.PERSONAL;
        String email = "<EMAIL>";
        String extension = "ext456";

        Email email1 = Email.builder()
                .id(id1)
                .entityType(entityType)
                .entityId(entityId)
                .emailType(emailType)
                .email(email)
                .extension(extension)
                .build();

        Email email2 = Email.builder()
                .id(id2)
                .entityType(entityType)
                .entityId(entityId)
                .emailType(emailType)
                .email(email)
                .extension(extension)
                .build();

        // Act & Assert
        assertTrue(email1.sameValueAs(email2));
        assertTrue(email2.sameValueAs(email1));
    }

    @Test
    void testSameValueAsWithDifferentValues() {
        // Arrange
        UUID id = UUID.randomUUID();
        String entityType = "Vendor";
        UUID entityId = UUID.randomUUID();
        EmailType emailType = EmailType.WORK;
        String email = "<EMAIL>";
        String extension = "ext789";

        Email email1 = Email.builder()
                .id(id)
                .entityType(entityType)
                .entityId(entityId)
                .emailType(emailType)
                .email(email)
                .extension(extension)
                .build();

        Email email2 = Email.builder()
                .id(id)
                .entityType(entityType)
                .entityId(entityId)
                .emailType(EmailType.PERSONAL) // Different email type
                .email(email)
                .extension(extension)
                .build();

        // Act & Assert
        assertFalse(email1.sameValueAs(email2));
        assertFalse(email2.sameValueAs(email1));
    }
}
