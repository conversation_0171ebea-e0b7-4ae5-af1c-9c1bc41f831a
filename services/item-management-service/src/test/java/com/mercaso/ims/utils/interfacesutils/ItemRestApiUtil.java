package com.mercaso.ims.utils.interfacesutils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.ims.application.command.BatchUpdateItemPhotoCommand;
import com.mercaso.ims.application.command.BatchUpdateItemStatusCommand;
import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.command.UpdateItemBackupVendorCommand;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.command.UpdateItemPrimaryVendorCommand;
import com.mercaso.ims.application.command.UpdateItemPromoPriceCommand;
import com.mercaso.ims.application.command.UpdateItemUpcCommand;
import com.mercaso.ims.application.command.ValidateBarcodeCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemPhotoResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemPromoPriceResultDto;
import com.mercaso.ims.application.dto.BatchUpdateItemStatusResultDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ValidateBarcodeResultDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

@Component
public class ItemRestApiUtil extends IntegrationTestRestUtil {

    private static final String ITEM_REST_API_URL = "/v1/item";

    public ItemRestApiUtil(Environment environment) {
        super(environment);
    }

    public ItemDto itemDetailRequest(UUID id) throws Exception {
        return getEntity(ITEM_REST_API_URL + "/" + id, ItemDto.class);
    }

    public void deleteItemRequest(UUID id) {
        deleteEntity(ITEM_REST_API_URL + "/" + id);
    }

    public ItemDto createItemRequest(CreateItemCommand command) throws Exception {
        return createEntity(ITEM_REST_API_URL, command, ItemDto.class);
    }

    public ItemDto updateItemRequest(UUID id, UpdateItemCommand command) throws Exception {
        return updateEntity(ITEM_REST_API_URL + "/" + id, command, ItemDto.class);
    }

    public ItemDto activeItemRequest(UUID id) throws JsonProcessingException {
        return updateEntity(ITEM_REST_API_URL + "/active/" + id, null, ItemDto.class);
    }

    public ItemDto draftItemRequest(UUID id) throws JsonProcessingException {
        return updateEntity(ITEM_REST_API_URL + "/draft/" + id, null, ItemDto.class);
    }

    public ItemDto archiveItemRequest(UUID id, UpdateItemCommand command) throws JsonProcessingException {
        return updateEntity(ITEM_REST_API_URL + "/archive/" + id, command, ItemDto.class);
    }

    public ItemDto setPrimaryVendor(UUID id, UUID primaryVendorId) throws JsonProcessingException {
        return updateEntity(ITEM_REST_API_URL + "/" + id + "/primary-vendor",
            UpdateItemPrimaryVendorCommand.builder().id(id).primaryVendorId(primaryVendorId).build(),
            ItemDto.class);
    }

    public ItemDto setBackupVendor(UUID id, UUID backupVendorId) throws JsonProcessingException {

        return updateEntity(ITEM_REST_API_URL + "/" + id + "/backup-vendor",
            UpdateItemBackupVendorCommand.builder().id(id).backupVendorId(backupVendorId).build(),
            ItemDto.class);
    }

    public BatchUpdateItemStatusResultDto batchUpdateItemStatus(BatchUpdateItemStatusCommand command)
        throws JsonProcessingException {
        return updateEntity(ITEM_REST_API_URL + "/batch-update-status", command, BatchUpdateItemStatusResultDto.class);
    }

    public ValidateBarcodeResultDto validateBarcode(ValidateBarcodeCommand command)
        throws JsonProcessingException {
        return postEntity(ITEM_REST_API_URL + "/barcode/validate", command, ValidateBarcodeResultDto.class);
    }

    public BatchUpdateItemPhotoResultDto batchUpdatePhoto(List<BatchUpdateItemPhotoCommand> commands)
        throws JsonProcessingException {
        return updateEntity(ITEM_REST_API_URL + "/batch-update-photo", commands, BatchUpdateItemPhotoResultDto.class);
    }

    public ItemDto updatePromoPrice(UpdateItemPromoPriceCommand command)
        throws JsonProcessingException {
        return updateEntity(ITEM_REST_API_URL + "/" + command.getItemId() + "/promo-price", command, ItemDto.class);
    }

    public ItemDto updateItemUpc(UpdateItemUpcCommand command)
        throws JsonProcessingException {
        return updateEntity(ITEM_REST_API_URL + "/" + command.getItemId() + "/upc", command, ItemDto.class);
    }

    public BatchUpdateItemPromoPriceResultDto batchUpdatePromoPrice(List<UpdateItemPromoPriceCommand> commands)
        throws JsonProcessingException {
        return updateEntity(ITEM_REST_API_URL + "/batch-update-promo-price", commands, BatchUpdateItemPromoPriceResultDto.class);
    }

    public ItemDto bindingSkuPhoto(File file) throws IOException {
        byte[] fileContent = Files.readAllBytes(file.toPath());
        ByteArrayResource fileResource = new ByteArrayResource(fileContent) {
            @Override
            public String getFilename() {
                return file.getName();
            }
        };
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("requestFile", fileResource);
        return uploadFile(ITEM_REST_API_URL + "/binding-photo", body, ItemDto.class);
    }

}
