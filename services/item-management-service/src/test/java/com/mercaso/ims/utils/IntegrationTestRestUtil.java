package com.mercaso.ims.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.http.HttpMethod.POST;
import static org.springframework.http.HttpMethod.PUT;
import static org.springframework.http.MediaType.MULTIPART_FORM_DATA;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.util.List;
import java.util.Map;
import org.springframework.boot.test.web.client.LocalHostUriTemplateHandler;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;


public abstract class IntegrationTestRestUtil {


    private final LocalHostUriTemplateHandler uriTemplateHandler;

    private final RestTemplate restTemplate = new RestTemplate();

    private final ObjectMapper objectMapper = new ObjectMapper()
        .registerModule(new JavaTimeModule())
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    public IntegrationTestRestUtil(Environment environment) {
        this.uriTemplateHandler = new LocalHostUriTemplateHandler(environment);

        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(5000);
        restTemplate.setRequestFactory(requestFactory);
    }

    private HttpHeaders createHeaders() {
        return createHeaders(MediaType.APPLICATION_JSON);
    }

    private HttpHeaders createHeaders(MediaType type) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(type);
        return headers;
    }


    public <T> T createEntity(String path, Object payload, Class<T> dtoClass) throws Exception {
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, createHeaders());
        String url = uriTemplateHandler.getRootUri() + path;

        return restTemplate.postForObject(url, httpentity, dtoClass);
    }

    public <T> T getEntity(String path, Class<T> dtoClass) throws Exception {
        String url = uriTemplateHandler.getRootUri() + path;
        ResponseEntity<T> response = restTemplate.getForEntity(url, dtoClass);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        return response.getBody();

    }


    public <T> T createEntity(String path, Map<String, Object> body, Class<T> dtoClass) throws Exception {
        HttpEntity<Object> httpentity = new HttpEntity<>(body, createHeaders(MULTIPART_FORM_DATA));
        String url = uriTemplateHandler.getRootUri() + path;

        return restTemplate.postForObject(url, httpentity, dtoClass);
    }

    public <T> T uploadFile(String path, MultiValueMap<String, Object> body, Class<T> dtoClass) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MULTIPART_FORM_DATA);

        HttpEntity<MultiValueMap<String, Object>> httpentity = new HttpEntity<>(body, headers);
        String url = uriTemplateHandler.getRootUri() + path;
        return restTemplate.postForObject(url, httpentity, dtoClass);
    }

    public <T> List<T> getEntityList(String url, Class<T> dtoClass) throws Exception {
        HttpEntity<String> entity = new HttpEntity<>(null, createHeaders());
        ResponseEntity<String> response = performRequest(url, null, entity, HttpMethod.GET);

        assertEquals(HttpStatus.OK, response.getStatusCode());

        CollectionType listType = objectMapper.getTypeFactory().constructCollectionType(List.class, dtoClass);
        return objectMapper.readValue(response.getBody(), listType);
    }

    private ResponseEntity<String> performRequest(String path,
        Map<String, String> params,
        HttpEntity<String> entity,
        HttpMethod method) {
        String url = uriTemplateHandler.getRootUri() + path;

        if (params != null) {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
            builder.build(false);
            params.forEach(builder::queryParam);
            url = builder.toUriString();
        }

        return restTemplate.exchange(url, method, entity, String.class);
    }

    protected void deleteEntity(String url) {
        HttpEntity<String> entity = new HttpEntity<>(null, createHeaders());
        ResponseEntity<String> response = performRequest(url, null, entity, HttpMethod.DELETE);

        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    public <T> T updateEntity(String path, Object payload, Class<T> dtoClass) throws JsonProcessingException {
        String url = uriTemplateHandler.getRootUri() + path;
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, createHeaders());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, PUT, httpentity, String.class);

        return responseEntity.getBody() != null ? objectMapper.readValue(responseEntity.getBody(), dtoClass) : null;
    }

    public <T> T postEntity(String path, Object payload, Class<T> dtoClass) throws JsonProcessingException {
        String url = uriTemplateHandler.getRootUri() + path;
        HttpEntity<Object> httpentity = new HttpEntity<>(payload, createHeaders());
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, POST, httpentity, String.class);

        return responseEntity.getBody() != null ? objectMapper.readValue(responseEntity.getBody(), dtoClass) : null;
    }


}
