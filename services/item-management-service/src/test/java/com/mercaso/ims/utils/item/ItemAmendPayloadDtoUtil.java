package com.mercaso.ims.utils.item;

import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import java.util.UUID;

public class ItemAmendPayloadDtoUtil {

    public static ItemAmendPayloadDto buildItemAmendPayloadDto() {
        ItemDto previous = ItemDTOUtil.buildItemDTO();
        ItemDto current = ItemDTOUtil.buildItemDTO();
        current.setTitle("new title");
        return ItemAmendPayloadDto.builder()
            .itemId(UUID.randomUUID())
            .previous(previous)
            .current(current)
            .build();
    }
}
