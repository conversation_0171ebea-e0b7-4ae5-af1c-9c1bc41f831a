package com.mercaso.ims.utils.itemcostcollection;

import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;

public class ItemCostCollectionUtil {

    public static ItemCostCollection buildItemCostCollection() {
        return buildItemCostCollection(UUID.randomUUID());
    }

    public static ItemCostCollection buildItemCostCollection(UUID vendorId) {

        return ItemCostCollection.builder()
            .vendorId(vendorId)
            .vendorName("Vendor Item Name")
            .fileName(RandomStringUtils.randomAlphabetic(10))
            .collectionNumber(RandomStringUtils.randomAlphabetic(10))
            .vendorCollectionNumber(RandomStringUtils.randomAlphabetic(10))
            .type(ItemCostCollectionTypes.CSV_FILE)
            .source(ItemCostCollectionSources.MANUAL_UPLOADED)
            .build();
    }

}
