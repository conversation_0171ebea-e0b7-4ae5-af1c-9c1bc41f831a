package com.mercaso.ims.domain.attribute;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.application.command.CreateAttributeCommand;
import com.mercaso.ims.domain.attribute.enums.AttributeFormat;
import com.mercaso.ims.domain.attribute.enums.AttributeStatus;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class AttributeFactoryTest {

    @Test
    void testCreate() {
        // Arrange
        UUID categoryId = UUID.randomUUID();

        // Act
        Attribute actualCreateResult = AttributeFactory.create(new CreateAttributeCommand("Name", categoryId,
            "The characteristics of someone or something", AttributeFormat.TEXT));

        // Assert
        assertEquals("Name", actualCreateResult.getName());
        assertEquals("The characteristics of someone or something", actualCreateResult.getDescription());
        assertNull(actualCreateResult.getCreatedBy());
        assertNull(actualCreateResult.getCreatedUserName());
        assertNull(actualCreateResult.getDeletedBy());
        assertNull(actualCreateResult.getDeletedUserName());
        assertNull(actualCreateResult.getUpdatedBy());
        assertNull(actualCreateResult.getUpdatedUserName());
        assertNull(actualCreateResult.getCreatedAt());
        assertNull(actualCreateResult.getDeletedAt());
        assertNull(actualCreateResult.getUpdatedAt());
        assertNull(actualCreateResult.getAttributeEnumValues());
        assertNull(actualCreateResult.getId());
        assertEquals(AttributeFormat.TEXT, actualCreateResult.getAttributeFormat());
        assertEquals(AttributeStatus.ACTIVE, actualCreateResult.getStatus());
        assertSame(categoryId, actualCreateResult.getCategoryId());
    }


    @Test
    void testVerifyCreate() {
        // Arrange
        CreateAttributeCommand command = mock(CreateAttributeCommand.class);
        when(command.getAttributeFormat()).thenReturn(AttributeFormat.TEXT);
        when(command.getDescription()).thenReturn("The characteristics of someone or something");
        when(command.getName()).thenReturn("Name");
        UUID randomUUIDResult = UUID.randomUUID();
        when(command.getCategoryId()).thenReturn(randomUUIDResult);

        // Act
        Attribute actualCreateResult = AttributeFactory.create(command);

        // Assert
        verify(command).getAttributeFormat();
        verify(command).getCategoryId();
        verify(command).getDescription();
        verify(command).getName();
        assertEquals("Name", actualCreateResult.getName());
        assertEquals("The characteristics of someone or something", actualCreateResult.getDescription());
        assertNull(actualCreateResult.getCreatedBy());
        assertNull(actualCreateResult.getCreatedUserName());
        assertNull(actualCreateResult.getDeletedBy());
        assertNull(actualCreateResult.getDeletedUserName());
        assertNull(actualCreateResult.getUpdatedBy());
        assertNull(actualCreateResult.getUpdatedUserName());
        assertNull(actualCreateResult.getCreatedAt());
        assertNull(actualCreateResult.getDeletedAt());
        assertNull(actualCreateResult.getUpdatedAt());
        assertNull(actualCreateResult.getAttributeEnumValues());
        assertNull(actualCreateResult.getId());
        assertEquals(AttributeFormat.TEXT, actualCreateResult.getAttributeFormat());
        assertEquals(AttributeStatus.ACTIVE, actualCreateResult.getStatus());
        assertSame(randomUUIDResult, actualCreateResult.getCategoryId());
    }
}
