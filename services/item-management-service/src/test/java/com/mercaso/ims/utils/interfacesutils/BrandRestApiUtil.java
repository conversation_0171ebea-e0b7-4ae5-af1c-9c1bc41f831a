package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.command.CreateBrandCommand;
import com.mercaso.ims.application.command.UpdateBrandCommand;
import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class BrandRestApiUtil extends IntegrationTestRestUtil {

    private static final String BRAND_REQUEST_URL = "/v1/brands";

    public BrandRestApiUtil(Environment environment) {
        super(environment);
    }


    public BrandDto createBrand(CreateBrandCommand command) throws Exception {
        return createEntity(BRAND_REQUEST_URL, command, BrandDto.class);
    }

    public BrandDto updateBrand(UUID brandId, UpdateBrandCommand command) throws Exception {
        return updateEntity(BRAND_REQUEST_URL + "/" + brandId, command, BrandDto.class);
    }

    public void deleteBrand(UUID brandId) {
        deleteEntity(BRAND_REQUEST_URL + "/" + brandId);
    }

}