package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.ItemPriceGroupDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class QueryItemPriceGroupRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_ITEM_PRICE_GROUP_URL = "/v1/query/item-price-group";

    public QueryItemPriceGroupRestApiUtil(Environment environment) {
        super(environment);
    }

    public List<ItemPriceGroupDto> searchItemPriceGroup(String itemPriceGroupName) throws Exception {
        String url = String.format(SEARCH_ITEM_PRICE_GROUP_URL + "?itemPriceGroupName=%s", itemPriceGroupName);
        return getEntityList(url, ItemPriceGroupDto.class);
    }
}
