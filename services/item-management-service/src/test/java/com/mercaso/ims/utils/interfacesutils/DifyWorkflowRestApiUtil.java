package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.DifyCategoryMatchingFinalResultDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

@Component
public class DifyWorkflowRestApiUtil extends IntegrationTestRestUtil {

    private static final String DIFY_WORKFLOW_REQUEST_URL = "/v1/dify-workflow";

    public DifyWorkflowRestApiUtil(Environment environment) {
        super(environment);
    }

    public DifyCategoryMatchingFinalResultDto getRecommendedCategories(String description) throws Exception {
        String url = UriComponentsBuilder.fromPath(DIFY_WORKFLOW_REQUEST_URL + "/recommendation/category")
                .queryParam("description", description)
                .build()
                .toUriString();
        
        return getEntity(url, DifyCategoryMatchingFinalResultDto.class);
    }
} 