package com.mercaso.ims.utils.item;

import com.mercaso.ims.application.dto.ItemImageDto;
import com.mercaso.ims.domain.item.ItemImage;
import com.mercaso.ims.domain.item.enums.ImageType;
import java.util.UUID;

public class ItemImageUtil {

    private ItemImageUtil() {
        throw new IllegalStateException("Utility class");
    }


    public static ItemImage buildItemImage(UUID id, UUID itemId, String imageName, ImageType imageType) {
        return ItemImage.builder()
            .id(id)
            .itemId(itemId)
            .fileName(imageName)
            .imageType(imageType)
            .build();
    }

    public static ItemImage buildItemImage(String imageName, ImageType imageType) {
        return buildItemImage(UUID.randomUUID(), UUID.randomUUID(), imageName, imageType);
    }

    public static ItemImageDto buildItemImageDto(String imageName, ImageType imageType) {
        return ItemImageDto.builder()
            .fileName(imageName)
            .imageType(imageType)
            .build();
    }

}
