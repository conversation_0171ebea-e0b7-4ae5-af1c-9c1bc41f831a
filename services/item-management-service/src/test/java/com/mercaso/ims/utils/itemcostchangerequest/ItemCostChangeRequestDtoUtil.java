package com.mercaso.ims.utils.itemcostchangerequest;

import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import java.math.BigDecimal;
import java.util.UUID;

public class ItemCostChangeRequestDtoUtil {

    public static ItemCostChangeRequestDto buildItemCostChangeRequestDto() {
        return buildItemCostChangeRequestDto(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID());
    }

    public static ItemCostChangeRequestDto buildItemCostChangeRequestDto(UUID itemCostCollectionId,
        UUID vendorId,
        UUID matchedItemId) {
        return ItemCostChangeRequestDto.builder()
            .itemCostCollectionId(itemCostCollectionId)
            .vendorSkuNumber("vendorSkuNumber")
            .itemId(matchedItemId)
            .vendorId(vendorId)
            .vendorItemName("Vendor Item Name")
            .previousCost(BigDecimal.valueOf(9))
            .targetCost(BigDecimal.valueOf(10))
            .matchType(MatchedType.AUTO_MATCHED_AND_UPDATED)
            .status(ItemCostChangeRequestStatus.PENDING)
            .build();
    }
}
