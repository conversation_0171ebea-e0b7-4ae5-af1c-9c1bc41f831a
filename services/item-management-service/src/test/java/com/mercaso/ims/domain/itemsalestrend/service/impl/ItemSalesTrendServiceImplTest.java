package com.mercaso.ims.domain.itemsalestrend.service.impl;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrend;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrendRepository;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import com.mercaso.ims.utils.itemsalestrend.ItemSalesTrendTestUtil;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ContextConfiguration(classes = {ItemSalesTrendServiceImpl.class})
class ItemSalesTrendServiceImplTest extends AbstractTest {

    @MockBean
    private ItemSalesTrendRepository itemSalesTrendRepository;

    @Autowired
    private ItemSalesTrendServiceImpl itemSalesTrendServiceImpl;

    @Test
    void testFindByItemIdAndTimeGrain_whenRepositoryReturnsEmptyList_thenReturnEmptyList() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.DAY;
        when(itemSalesTrendRepository.findByItemIdAndTimeGrain(anyString(), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(new ArrayList<>());

        // Act
        List<ItemSalesTrend> result = itemSalesTrendServiceImpl.findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendRepository).findByItemIdAndTimeGrain(itemId.toString(), timeGrain);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByItemIdAndTimeGrain_whenRepositoryReturnsData_thenReturnSameData() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.WEEK;
        
        ItemSalesTrend itemSalesTrend1 = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, timeGrain, 100);
        ItemSalesTrend itemSalesTrend2 = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, timeGrain, 150);
        List<ItemSalesTrend> expectedList = List.of(itemSalesTrend1, itemSalesTrend2);
        
        when(itemSalesTrendRepository.findByItemIdAndTimeGrain(anyString(), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(expectedList);

        // Act
        List<ItemSalesTrend> result = itemSalesTrendServiceImpl.findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendRepository).findByItemIdAndTimeGrain(itemId.toString(), timeGrain);
        assertEquals(2, result.size());
        assertEquals(expectedList, result);
        assertEquals(itemSalesTrend1.getSalesQuantity(), result.get(0).getSalesQuantity());
        assertEquals(itemSalesTrend2.getSalesQuantity(), result.get(1).getSalesQuantity());
    }

    @Test
    void testFindByItemIdAndTimeGrain_withDifferentTimeGrains_shouldCallRepositoryCorrectly() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.MONTH;
        
        ItemSalesTrend itemSalesTrend = ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, timeGrain, 200);
        List<ItemSalesTrend> expectedList = List.of(itemSalesTrend);
        
        when(itemSalesTrendRepository.findByItemIdAndTimeGrain(anyString(), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(expectedList);

        // Act
        List<ItemSalesTrend> result = itemSalesTrendServiceImpl.findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendRepository).findByItemIdAndTimeGrain(itemId.toString(), timeGrain);
        assertEquals(1, result.size());
        assertEquals(ItemSalesTrendTimeGrain.MONTH, result.getFirst().getTimeGrain());
        assertEquals(200, result.getFirst().getSalesQuantity());
    }

    @Test
    void testFindByItemIdAndTimeGrain_withNullItemId_shouldHandleCorrectly() {
        // Arrange
        UUID itemId = null;
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.DAY;
        when(itemSalesTrendRepository.findByItemIdAndTimeGrain(anyString(), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(new ArrayList<>());

        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            itemSalesTrendServiceImpl.findByItemIdAndTimeGrain(itemId, timeGrain);
        });
    }

    @Test
    void testFindByItemIdAndTimeGrain_withUnknownTimeGrain_shouldCallRepository() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.UNKNOWN;
        when(itemSalesTrendRepository.findByItemIdAndTimeGrain(anyString(), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(new ArrayList<>());

        // Act
        List<ItemSalesTrend> result = itemSalesTrendServiceImpl.findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendRepository).findByItemIdAndTimeGrain(itemId.toString(), timeGrain);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindByItemIdAndTimeGrain_shouldConvertUuidToString() {
        // Arrange
        UUID itemId = UUID.fromString("123e4567-e89b-12d3-a456-************");
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.DAY;
        String expectedItemIdString = "123e4567-e89b-12d3-a456-************";
        
        when(itemSalesTrendRepository.findByItemIdAndTimeGrain(anyString(), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(new ArrayList<>());

        // Act
        itemSalesTrendServiceImpl.findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendRepository).findByItemIdAndTimeGrain(expectedItemIdString, timeGrain);
    }

    @Test
    void testFindByItemIdAndTimeGrain_withLargeDataSet_shouldReturnAllData() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.DAY;
        
        List<ItemSalesTrend> largeDataSet = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            largeDataSet.add(ItemSalesTrendTestUtil.buildItemSalesTrend(itemId, timeGrain, i * 10));
        }
        
        when(itemSalesTrendRepository.findByItemIdAndTimeGrain(anyString(), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(largeDataSet);

        // Act
        List<ItemSalesTrend> result = itemSalesTrendServiceImpl.findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendRepository).findByItemIdAndTimeGrain(itemId.toString(), timeGrain);
        assertEquals(100, result.size());
        assertEquals(largeDataSet, result);
    }

    @Test
    void testFindByItemIdAndTimeGrain_verifyRepositoryMethodCalledOnce() {
        // Arrange
        UUID itemId = UUID.randomUUID();
        ItemSalesTrendTimeGrain timeGrain = ItemSalesTrendTimeGrain.WEEK;
        when(itemSalesTrendRepository.findByItemIdAndTimeGrain(anyString(), any(ItemSalesTrendTimeGrain.class)))
            .thenReturn(new ArrayList<>());

        // Act
        itemSalesTrendServiceImpl.findByItemIdAndTimeGrain(itemId, timeGrain);

        // Assert
        verify(itemSalesTrendRepository, times(1)).findByItemIdAndTimeGrain(itemId.toString(), timeGrain);
        verifyNoMoreInteractions(itemSalesTrendRepository);
    }
}
