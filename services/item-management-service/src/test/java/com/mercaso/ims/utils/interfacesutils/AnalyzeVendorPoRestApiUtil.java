package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.io.File;
import java.nio.file.Files;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

@Component
public class AnalyzeVendorPoRestApiUtil extends IntegrationTestRestUtil {
    private static final String ANALYZE_DOWNEY_COST = "/v1/analyze-po-invoice/downey-cost";

    public AnalyzeVendorPoRestApiUtil(Environment environment) {
        super(environment);
    }


    public void uploadAnalyzeFile(File file) throws Exception {
        byte[] fileContent = Files.readAllBytes(file.toPath());
        ByteArrayResource fileResource = new ByteArrayResource(fileContent) {
            @Override
            public String getFilename() {
                return file.getName();
            }
        };
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("requestFile", fileResource);
        uploadFile(ANALYZE_DOWNEY_COST, body, Void.class);
    }


}
