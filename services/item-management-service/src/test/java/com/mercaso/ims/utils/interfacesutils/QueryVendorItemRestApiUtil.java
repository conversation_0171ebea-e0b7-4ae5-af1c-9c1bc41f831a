package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class QueryVendorItemRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_VENDOR_ITEMS_URL = "/v1/query/vendor-item";

    public QueryVendorItemRestApiUtil(Environment environment) {
        super(environment);
    }

    public List<VendorItemDto> searchVendorItems(UUID vendorId, List<UUID> itemIds) throws Exception {
        String itemIdsParam = String.join(",", itemIds.stream().map(UUID::toString).toList());
        String url = String.format("%s?vendorId=%s&itemIds=%s", SEARCH_VENDOR_ITEMS_URL, vendorId, itemIdsParam);
        return getEntityList(url, VendorItemDto.class);
    }
}
