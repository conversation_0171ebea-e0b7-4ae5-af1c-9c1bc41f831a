package com.mercaso.ims.utils.itemcostcollection;

import com.mercaso.ims.application.dto.ItemCostCollectionDetailDto;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import java.time.Instant;
import java.util.UUID;

public class ItemCostCollectionSearchUtil {

    public static ItemCostCollectionDetailDto buildItemCostCollectionDto(UUID id) {
        return new ItemCostCollectionDetailDto(id,
            ItemCostCollectionSources.MANUAL_UPLOADED,
            new UUID(0L, 0L),
            "vendorName",
            "collectionNumber",
            "collectionNumber",
            "fileName",
            ItemCostCollectionTypes.CSV_FILE,
            "fileName",
            Integer.valueOf(0),
            Integer.valueOf(0),
            Integer.valueOf(0),
            Integer.valueOf(0),
            Instant.now(),
            "createdBy",
            "createdUserName", Instant.now(),
            "updateddUserName");
    }

}
