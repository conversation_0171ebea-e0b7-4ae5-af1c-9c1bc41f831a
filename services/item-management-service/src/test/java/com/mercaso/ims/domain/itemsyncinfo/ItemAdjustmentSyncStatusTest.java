package com.mercaso.ims.domain.itemsyncinfo;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemAdjustmentSyncStatus.class})
class ItemAdjustmentSyncStatusTest {

    @Test
    void testSameValueAs() {

        // Arrange
        ItemAdjustmentSyncStatus itemAdjustmentSyncStatus = new ItemAdjustmentSyncStatus(
            mock(ItemAdjustmentSyncStatus.ItemAdjustmentSyncStatusBuilder.class));

        // Act and Assert
        assertTrue(itemAdjustmentSyncStatus.sameValueAs(
            new ItemAdjustmentSyncStatus(mock(ItemAdjustmentSyncStatus.ItemAdjustmentSyncStatusBuilder.class))));
    }

    @Test
    void testSameValueAs2() {

        // Arrange, Act and Assert
        assertFalse((new ItemAdjustmentSyncStatus(mock(ItemAdjustmentSyncStatus.ItemAdjustmentSyncStatusBuilder.class)))
            .sameValueAs(null));
    }


    @Test
    void testSameValueAs3() {

        // Arrange
        ItemAdjustmentSyncStatus itemAdjustmentSyncStatus = new ItemAdjustmentSyncStatus(
            mock(ItemAdjustmentSyncStatus.ItemAdjustmentSyncStatusBuilder.class));
        itemAdjustmentSyncStatus.setBusinessEventId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemAdjustmentSyncStatus.sameValueAs(
            new ItemAdjustmentSyncStatus(mock(ItemAdjustmentSyncStatus.ItemAdjustmentSyncStatusBuilder.class))));
    }

    @Test
    void testSameValueAs4() {

        // Arrange
        ItemAdjustmentSyncStatus itemAdjustmentSyncStatus = new ItemAdjustmentSyncStatus(
            mock(ItemAdjustmentSyncStatus.ItemAdjustmentSyncStatusBuilder.class));

        ItemAdjustmentSyncStatus other = new ItemAdjustmentSyncStatus(
            mock(ItemAdjustmentSyncStatus.ItemAdjustmentSyncStatusBuilder.class));
        other.setBusinessEventId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemAdjustmentSyncStatus.sameValueAs(other));
    }

    @Test
    void testSameValueAs5() {

        // Arrange
        ItemAdjustmentSyncStatus itemAdjustmentSyncStatus = new ItemAdjustmentSyncStatus(
            mock(ItemAdjustmentSyncStatus.ItemAdjustmentSyncStatusBuilder.class));
        itemAdjustmentSyncStatus.setBusinessEventId(UUID.randomUUID());

        ItemAdjustmentSyncStatus other = new ItemAdjustmentSyncStatus(
            mock(ItemAdjustmentSyncStatus.ItemAdjustmentSyncStatusBuilder.class));
        other.setBusinessEventId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemAdjustmentSyncStatus.sameValueAs(other));
    }
}
