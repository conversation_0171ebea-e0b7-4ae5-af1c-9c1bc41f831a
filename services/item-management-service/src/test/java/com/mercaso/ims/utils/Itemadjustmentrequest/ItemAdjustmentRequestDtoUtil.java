package com.mercaso.ims.utils.Itemadjustmentrequest;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.ZoneOffset;
import java.util.UUID;

public class ItemAdjustmentRequestDtoUtil {

    public static ItemAdjustmentRequestDto buildItemAdjustmentRequestDto(UUID id) {
        return new ItemAdjustmentRequestDto(
            id,
            "requestFile",
            ItemAdjustmentRequestType.DOWNEY_ADJUSTMENT,
            ItemAdjustmentRequestStatus.UPLOADED,
            "test",
            Integer.valueOf(0),
            Integer.valueOf(0),
            Integer.valueOf(0),
            Integer.valueOf(0),
            Integer.valueOf(0),
            Integer.valueOf(0),
            Integer.valueOf(0),
            Integer.valueOf(0),
            Integer.valueOf(0),
            LocalDateTime.of(2024, Month.OCTOBER, 10, 18, 4, 27).toInstant(
                ZoneOffset.UTC),
            "createdBy",
            "createdUserName",
            "detailFile",
            LocalDateTime.of(2024, Month.OCTOBER, 10, 18, 4, 27).toInstant(
                ZoneOffset.UTC),
            "lastModifiedUserName",
            "updatedUserName"
        );
    }

}
