package com.mercaso.ims.utils.item;

import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.item.enums.ItemType;
import com.mercaso.ims.domain.item.enums.PackageType;
import com.mercaso.ims.domain.itemattribute.ItemAttribute;
import com.mercaso.ims.utils.itemattribute.ItemAttributeUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.apache.commons.compress.utils.Lists;

public class ItemUtil {

    public static Item buildItem(String skuNumber, UUID vendorId, UUID categoryId, UUID brandId) {
        List<ItemAttribute> attributes = new ArrayList<>();
        attributes.add(ItemAttributeUtil.buildItemAttribute());
        return Item.builder()
            .id(UUID.randomUUID())
            .brandId(null != brandId ? brandId : UUID.randomUUID())
            .categoryId(null != categoryId ? categoryId : UUID.randomUUID())
            .skuNumber(skuNumber)
            .detail("detail")
            .availabilityStatus(AvailabilityStatus.ACTIVE)
            .description("description")
            .handle("handle")
            .companyId(1000L)
            .locationId(1001L)
            .itemType(ItemType.SELF_OPERATED)
            .name("name")
            .title("title")
            .packageSize(10)
            .packageType(PackageType.PACK)
            .itemImages(Lists.newArrayList())
            .itemTags(Lists.newArrayList())
            .itemUPCs(Lists.newArrayList())
            .itemAttributes(attributes)
            .primaryVendorId(vendorId)
            .createdBy("createdBy")
            .createdUserName("createdUserName")
            .department("Decoration")
            .category("Breakfast, Breads & Tortillas")
            .subCategory("Auto Tools & Parts")
            .clazz("clazz")
            .itemAttributes(attributes)
            .build();
    }

    public static Item buildItem() {
        return buildItem("sku", UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID());
    }
}
