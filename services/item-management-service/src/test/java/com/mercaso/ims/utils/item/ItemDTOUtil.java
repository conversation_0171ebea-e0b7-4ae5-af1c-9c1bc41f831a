package com.mercaso.ims.utils.item;

import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.dto.ItemAttributeDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemImageDto;
import com.mercaso.ims.application.dto.ItemRegPriceDto;
import com.mercaso.ims.application.dto.ItemTagDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.domain.item.enums.AttributeType;
import com.mercaso.ims.domain.item.enums.ImageType;
import com.mercaso.ims.domain.item.enums.ItemParetoGrade;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemStatus;
import com.mercaso.ims.domain.vendoritem.enums.VendorItemType;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

public class ItemDTOUtil {

    public static ItemDto buildItemDTO() {
        return buildItemDTO(UUID.randomUUID(), UUID.randomUUID());
    }

    public static ItemDto buildItemDTO(UUID id, UUID vendorId) {
        ItemDto itemDto = ItemDto.builder()
            .id(id)
            .name("Beer Nuts, Cashews, 4 oz (24 Pack)")
            .title("Beer Nuts, Cashews, 4 oz (24 Pack)")
            .skuNumber("DW168888-24")
            .description("")
            .note(null)
            .detail("")
            .packageType("PACK")
            .packageSize(24)
            .itemType("SELF_OPERATED")
            .availabilityStatus("DRAFT")
            .handle("beer-nuts-cashews-4-oz-24-pack")
            .shelfLife("")
            .categoryId(UUID.fromString("514e2b61-10c3-43da-95dd-7e1b7bd9a7a0"))
            .categoryName("Nuts & Seeds")
            .clazz("Peanuts")
            .photoName("DW168080-12-20240827105635.jpg")
            .photoUrl(
                "https://svcs.us-west-2.dev.aws.mercaso.store/item-management/v1/document/public-paths/DW168080-12-20240827105635.jpg")
            .brand(BrandDto.builder()
                .brandId(UUID.fromString("f8af8936-4506-406a-99a2-27ee4004a0af"))
                .brandName("Beer Nuts")
                .build())
            .primaryVendorItem(VendorItemDto.builder()
                .vendorItemId(UUID.fromString("bab46eca-7860-427a-8098-d028a2ed2a06"))
                .vendorId(vendorId)
                .vendorName("Downey Wholesale")
                .vendorSkuNumber("168080")
                .vendorItemName("")
                .note("")
                .statusChangeReason("")
                .aisle("6D")
                .lowestCost(null)
                .highestCost(null)
                .cost(BigDecimal.TEN)
                .vendorItemType(VendorItemType.DIRECT.getTypeName())
                .vendorFinaleId("test")
                .vendorItemStatus(VendorItemStatus.ACTIVE)
                .build())
            .itemAttributes(Collections.singletonList(
                ItemAttributeDto.builder()
                    .attributeId(UUID.fromString("a8e3f151-2151-44f8-a2d8-3ce0b90b8cdc"))
                    .attributeName("item size")
                    .value("24.0")
                    .unit("oz")
                    .attributeType(AttributeType.KEY_ATTRIBUTES)
                    .sort(1)
                    .build()
            ))
            .itemImages(Collections.singletonList(
                ItemImageDto.builder()
                    .fileName("DW168080-12-20240827105635.jpg")
                    .url(
                        "https://svcs.us-west-2.dev.aws.mercaso.store/item-management/v1/document/public-paths/DW168080-12-20240827105635.jpg")
                    .imageType(ImageType.VARIANT_IMAGE)
                    .sort(null)
                    .status(null)
                    .build()
            ))
            .itemUPCs(Collections.emptyList())
            .itemTags(Arrays.asList(
                ItemTagDto.builder().tagName("Candy & Snacks").build(),
                ItemTagDto.builder().tagName("Snacks & Chips & Cookies").build(),
                ItemTagDto.builder().tagName("Nuts & Seeds").build(),
                ItemTagDto.builder().tagName("Brand_Beer Nuts").build(),
                ItemTagDto.builder().tagName("Type_Peanuts").build(),
                ItemTagDto.builder().tagName("UPP: 24").build(),
                ItemTagDto.builder().tagName("March Madness").build()
            ))
            .itemRegPrice(ItemRegPriceDto.builder()
                .itemRegPriceId(UUID.fromString("e11d5e59-796f-4552-a73c-ec514ec80610"))
                .regPrice(BigDecimal.TEN)
                .regPriceIndividual(BigDecimal.TWO)
                .crv(BigDecimal.ZERO)
                .regPricePlusCrv(BigDecimal.TEN)
                .build())
            .itemPromoPrices(null)
            .build();
        VendorItemDto buildResult = VendorItemDto.builder().vendorItemId(UUID.randomUUID())
            .vendorId(vendorId)
            .vendorItemName("Vendor Item Name")
            .vendorItemStatus(VendorItemStatus.ACTIVE)
            .vendorName("Vendor Name")
            .vendorSkuNumber("42")
            .build();
        itemDto.setVendorItemDtos(List.of(buildResult));
        itemDto.setLastVersionNumber(1);
        itemDto.setItemGrade(ItemParetoGrade.A);
        return itemDto;

    }

}
