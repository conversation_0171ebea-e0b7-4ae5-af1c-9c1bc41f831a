package com.mercaso.ims.utils.bulkexportrecords;

import com.mercaso.ims.domain.bulkexportrecords.BulkExportRecords;
import com.mercaso.ims.domain.bulkexportrecords.service.BulkExportRecordsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class BulkExportRecordsTestUtil {

    private final BulkExportRecordsService bulkExportRecordsService;

    /**
     * Creates test bulk export records for integration testing
     */
    public List<BulkExportRecords> createTestBulkExportRecords() {
        List<BulkExportRecords> records = new ArrayList<>();
        
        // Create test record 1
        BulkExportRecords record1 = BulkExportRecords.builder()
            .fileName("test_export_1.csv")
            .searchTime(Instant.now().minusSeconds(3600))
            .sendEmailTime(Instant.now().minusSeconds(3000))
            .customFilter("{\"status\":\"ACTIVE\"}")
            .exportBy("testUser")
            .build();
        records.add(bulkExportRecordsService.save(record1));

        // Create test record 2
        BulkExportRecords record2 = BulkExportRecords.builder()
            .fileName("test_export_2.xlsx")
            .searchTime(Instant.now().minusSeconds(7200))
            .sendEmailTime(Instant.now().minusSeconds(6600))
            .customFilter("{\"category\":\"electronics\"}")
            .exportBy("testUser2")
            .build();
        records.add(bulkExportRecordsService.save(record2));

        // Create test record 3
        BulkExportRecords record3 = BulkExportRecords.builder()
            .fileName("test_export_3.csv")
            .searchTime(Instant.now().minusSeconds(1800))
            .sendEmailTime(Instant.now().minusSeconds(1200))
            .customFilter("{\"vendor\":\"testVendor\"}")
            .exportBy("testUser")
            .build();
        records.add(bulkExportRecordsService.save(record3));

        return records;
    }

    /**
     * Creates a single test bulk export record
     */
    public BulkExportRecords createSingleTestRecord(String fileName, String exportBy) {
        BulkExportRecords record = BulkExportRecords.builder()
            .fileName(fileName)
            .searchTime(Instant.now())
            .sendEmailTime(Instant.now().plusSeconds(60))
            .customFilter("{\"test\":\"data\"}")
            .exportBy(exportBy)
            .build();
        
        return bulkExportRecordsService.save(record);
    }

    /**
     * Creates a test record with specific time range
     */
    public BulkExportRecords createTestRecordWithTime(Instant searchTime, Instant sendEmailTime, String exportBy) {
        BulkExportRecords record = BulkExportRecords.builder()
            .fileName("time_test_export.csv")
            .searchTime(searchTime)
            .sendEmailTime(sendEmailTime)
            .customFilter("{\"timeTest\":true}")
            .exportBy(exportBy)
            .build();
        
        return bulkExportRecordsService.save(record);
    }

    /**
     * Creates test records for a specific user
     */
    public List<BulkExportRecords> createTestRecordsForUser(String userName, int count) {
        List<BulkExportRecords> records = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            BulkExportRecords record = BulkExportRecords.builder()
                .fileName("user_export_" + i + ".csv")
                .searchTime(Instant.now().minusSeconds(i * 1800))
                .sendEmailTime(Instant.now().minusSeconds(i * 1200))
                .customFilter("{\"userTest\":\"" + userName + "\"}")
                .exportBy(userName)
                .build();
            records.add(bulkExportRecordsService.save(record));
        }
        
        return records;
    }

    /**
     * Creates test records within a specific date range
     */
    public List<BulkExportRecords> createTestRecordsInDateRange(Instant startTime, Instant endTime, String exportBy) {
        List<BulkExportRecords> records = new ArrayList<>();
        
        // Create record at start time
        BulkExportRecords record1 = BulkExportRecords.builder()
            .fileName("range_start_export.csv")
            .searchTime(startTime)
            .sendEmailTime(startTime.plusSeconds(300))
            .customFilter("{\"rangeTest\":\"start\"}")
            .exportBy(exportBy)
            .build();
        records.add(bulkExportRecordsService.save(record1));
        
        // Create record at end time
        BulkExportRecords record2 = BulkExportRecords.builder()
            .fileName("range_end_export.csv")
            .searchTime(endTime)
            .sendEmailTime(endTime.plusSeconds(300))
            .customFilter("{\"rangeTest\":\"end\"}")
            .exportBy(exportBy)
            .build();
        records.add(bulkExportRecordsService.save(record2));
        
        return records;
    }
}
