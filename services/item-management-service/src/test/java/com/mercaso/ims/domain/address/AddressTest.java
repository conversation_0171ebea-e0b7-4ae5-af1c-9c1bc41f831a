package com.mercaso.ims.domain.address;

import com.mercaso.ims.domain.address.enums.AddressPurpose;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class AddressTest {

    @Test
    void testSameValueAs_WithSameValues_ShouldReturnTrue() {
        // Given
        UUID entityId = UUID.randomUUID();
        Address address1 = Address.builder()
            .entityType("Vendor")
            .entityId(entityId)
            .streetAddress("123 Main St")
            .city("New York")
            .state("NY")
            .postalCode("10001")
            .country("USA")
            .directions("Near Central Park")
            .purpose(AddressPurpose.BUSINESS)
            .additionalLines("Suite 100")
            .build();

        Address address2 = Address.builder()
            .entityType("Vendor")
            .entityId(entityId)
            .streetAddress("123 Main St")
            .city("New York")
            .state("NY")
            .postalCode("10001")
            .country("USA")
            .directions("Near Central Park")
            .purpose(AddressPurpose.BUSINESS)
            .additionalLines("Suite 100")
            .build();

        // When & Then
        assertTrue(address1.sameValueAs(address2));
    }

    @Test
    void testSameValueAs_WithDifferentValues_ShouldReturnFalse() {
        // Given
        UUID entityId = UUID.randomUUID();
        Address address1 = Address.builder()
            .entityType("Vendor")
            .entityId(entityId)
            .streetAddress("123 Main St")
            .city("New York")
            .state("NY")
            .postalCode("10001")
            .country("USA")
            .directions("Near Central Park")
            .purpose(AddressPurpose.BUSINESS)
            .additionalLines("Suite 100")
            .build();

        Address address2 = Address.builder()
            .entityType("Vendor")
            .entityId(entityId)
            .streetAddress("456 Oak Ave")
            .city("New York")
            .state("NY")
            .postalCode("10001")
            .country("USA")
            .directions("Near Central Park")
            .purpose(AddressPurpose.BUSINESS)
            .additionalLines("Suite 100")
            .build();

        // When & Then
        assertFalse(address1.sameValueAs(address2));
    }

    @Test
    void testSameValueAs_WithNull_ShouldReturnFalse() {
        // Given
        Address address = Address.builder()
            .entityType("Vendor")
            .entityId(UUID.randomUUID())
            .streetAddress("123 Main St")
            .city("New York")
            .state("NY")
            .postalCode("10001")
            .country("USA")
            .directions("Near Central Park")
            .purpose(AddressPurpose.BUSINESS)
            .additionalLines("Suite 100")
            .build();

        // When & Then
        assertFalse(address.sameValueAs(null));
    }

    @Test
    void testSameValueAs_WithDifferentEntityType_ShouldReturnFalse() {
        // Given
        UUID entityId = UUID.randomUUID();
        Address address1 = Address.builder()
            .entityType("Vendor")
            .entityId(entityId)
            .streetAddress("123 Main St")
            .city("New York")
            .state("NY")
            .postalCode("10001")
            .country("USA")
            .directions("Near Central Park")
            .purpose(AddressPurpose.BUSINESS)
            .additionalLines("Suite 100")
            .build();

        Address address2 = Address.builder()
            .entityType("Customer")
            .entityId(entityId)
            .streetAddress("123 Main St")
            .city("New York")
            .state("NY")
            .postalCode("10001")
            .country("USA")
            .directions("Near Central Park")
            .purpose(AddressPurpose.BUSINESS)
            .additionalLines("Suite 100")
            .build();

        // When & Then
        assertFalse(address1.sameValueAs(address2));
    }

    @Test
    void testSameValueAs_WithDifferentPurpose_ShouldReturnFalse() {
        // Given
        UUID entityId = UUID.randomUUID();
        Address address1 = Address.builder()
            .entityType("Vendor")
            .entityId(entityId)
            .streetAddress("123 Main St")
            .city("New York")
            .state("NY")
            .postalCode("10001")
            .country("USA")
            .directions("Near Central Park")
            .purpose(AddressPurpose.BUSINESS)
            .additionalLines("Suite 100")
            .build();

        Address address2 = Address.builder()
            .entityType("Vendor")
            .entityId(entityId)
            .streetAddress("123 Main St")
            .city("New York")
            .state("NY")
            .postalCode("10001")
            .country("USA")
            .directions("Near Central Park")
            .purpose(AddressPurpose.SHIPPING)
            .additionalLines("Suite 100")
            .build();

        // When & Then
        assertFalse(address1.sameValueAs(address2));
    }

    @Test
    void testBuilder_ShouldCreateAddressWithAllFields() {
        // Given
        UUID entityId = UUID.randomUUID();
        String entityType = "Vendor";
        String streetAddress = "123 Main St";
        String city = "New York";
        String state = "NY";
        String postalCode = "10001";
        String country = "USA";
        String directions = "Near Central Park";
        AddressPurpose purpose = AddressPurpose.BUSINESS;
        String additionalLines = "Suite 100";

        // When
        Address address = Address.builder()
            .entityType(entityType)
            .entityId(entityId)
            .streetAddress(streetAddress)
            .city(city)
            .state(state)
            .postalCode(postalCode)
            .country(country)
            .directions(directions)
            .purpose(purpose)
            .additionalLines(additionalLines)
            .build();

        // Then
        assertEquals(entityType, address.getEntityType());
        assertEquals(entityId, address.getEntityId());
        assertEquals(streetAddress, address.getStreetAddress());
        assertEquals(city, address.getCity());
        assertEquals(state, address.getState());
        assertEquals(postalCode, address.getPostalCode());
        assertEquals(country, address.getCountry());
        assertEquals(directions, address.getDirections());
        assertEquals(purpose, address.getPurpose());
        assertEquals(additionalLines, address.getAdditionalLines());
    }
}
