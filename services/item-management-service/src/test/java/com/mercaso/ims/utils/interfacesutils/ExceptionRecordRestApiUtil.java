package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.command.BatchReviewExceptionRecordCommand;
import com.mercaso.ims.application.dto.BatchReviewExceptionRecordResultDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class ExceptionRecordRestApiUtil extends IntegrationTestRestUtil {

    private static final String EXCEPTION_RECORD_REQUEST_URL = "/v1/exception-record";

    public ExceptionRecordRestApiUtil(Environment environment) {
        super(environment);
    }

    public BatchReviewExceptionRecordResultDto batchReview(BatchReviewExceptionRecordCommand command)
        throws Exception {
        return updateEntity(EXCEPTION_RECORD_REQUEST_URL + "/batch-review", command, BatchReviewExceptionRecordResultDto.class);
    }

}
