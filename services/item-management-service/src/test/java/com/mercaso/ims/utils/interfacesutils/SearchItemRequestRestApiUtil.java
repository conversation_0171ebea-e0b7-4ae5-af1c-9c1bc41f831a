package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.ItemListDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class SearchItemRequestRest<PERSON><PERSON><PERSON>til extends IntegrationTestRestUtil {


    private static final String SEARCH_ITEM_V2_REQUEST_URL = "/v2/search/items";

    public SearchItemRequestRestApiUtil(Environment environment) {
        super(environment);
    }

    public ItemListDto searchItemListV2ByUpdateAtRequest(int pageSize) throws Exception {
        return getEntity(
            SEARCH_ITEM_V2_REQUEST_URL + "?page=1&pageSize=" + pageSize, ItemListDto.class);
    }

    public ItemListDto searchItemListV2ByTitleRequest(int pageSize, String customFilter) throws Exception {
        return getEntity(SEARCH_ITEM_V2_REQUEST_URL + "?page=1&pageSize=" + pageSize + "&customFilter=" + customFilter, ItemListDto.class);
    }


}
