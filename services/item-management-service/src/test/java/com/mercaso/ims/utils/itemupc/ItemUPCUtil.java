package com.mercaso.ims.utils.itemupc;

import com.mercaso.ims.application.dto.ItemUPCDto;
import com.mercaso.ims.domain.item.ItemUPC;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import java.util.UUID;

public class ItemUPCUtil {


    public static ItemUPC buildItemUPC(UUID itemId, String upcNumber, ItemUpcType type) {
        return ItemUPC.builder()
            .itemId(itemId)
            .upcNumber(upcNumber)
            .itemUpcType(type)
            .build();
    }


    public static ItemUPCDo buildItemUPCDo(String upcNumber, ItemUpcType type) {
        ItemUPCDo itemUPCDo = new ItemUPCDo();
        itemUPCDo.setUpcNumber(upcNumber);
        itemUPCDo.setItemUpcType(type);
        return itemUPCDo;
    }

    public static ItemUPCDto buildItemUPCDto(String upcNumber, ItemUpcType type) {
        ItemUPCDto itemUPCDto = new ItemUPCDto();
        itemUPCDto.setUpcNumber(upcNumber);
        itemUPCDto.setItemUpcType(type);
        return itemUPCDto;
    }


}
