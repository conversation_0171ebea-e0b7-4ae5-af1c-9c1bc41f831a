package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class QueryBrandRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_BRANDS_URL = "/v1/query/brand";

    public QueryBrandRestApiUtil(Environment environment) {
        super(environment);
    }

    public List<BrandDto> searchBrands (String brandName) throws Exception {
        String url = String.format(SEARCH_BRANDS_URL + "?brandName=%s", brandName);
        return getEntityList(url, BrandDto.class);
    }
}
