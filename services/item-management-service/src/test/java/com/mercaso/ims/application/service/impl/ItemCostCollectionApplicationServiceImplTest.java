package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.dto.ItemCostCollectionDto;
import com.mercaso.ims.application.mapper.itemcostcollection.ItemCostCollectionApplicationMapper;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.util.UUID;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SessionDelegatorBaseImpl;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemCostCollectionApplicationServiceImpl.class})
class ItemCostCollectionApplicationServiceImplTest extends AbstractTest {

    @MockBean
    private EntityManagerFactory entityManagerFactory;

    @Autowired
    private ItemCostCollectionApplicationServiceImpl itemCostCollectionApplicationServiceImpl;

    @MockBean
    private BusinessEventService businessEventService;

    @MockBean
    private ItemCostCollectionApplicationMapper itemCostCollectionApplicationMapper;

    @MockBean
    private ItemCostCollectionService itemCostCollectionService;

    @MockBean
    private PgAdvisoryLock pgAdvisoryLock;
    @MockBean
    private VendorService vendorService;


    @MockBean
    private FinaleExternalApiAdaptor finaleExternalApiAdaptor;
    @MockBean
    private DocumentApplicationService documentApplicationService;


    @Test
    void testCreate_thenReturnCollectionNumberIs42() throws HibernateException {
        // Arrange
        when(itemCostCollectionService.save(Mockito.<ItemCostCollection>any())).thenReturn(null);
        ItemCostCollectionDto buildResult = ItemCostCollectionDto.builder()
            .approvedCount(3)
            .collectionNumber("42")
            .fileName("foo.txt")
            .id(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .pendingCount(3)
            .rejectedCount(3)
            .source(ItemCostCollectionSources.MANUAL_UPLOADED)
            .type(ItemCostCollectionTypes.CSV_FILE)
            .vendorCollectionNumber("42")
            .vendorId(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID)
            .vendorName("Vendor Name")
            .build();
        when(itemCostCollectionApplicationMapper.domainToDto(Mockito.<ItemCostCollection>any())).thenReturn(buildResult);
        when(businessEventService.dispatch(Mockito.<BusinessEventPayloadDto<ItemCostCollectionDto>>any())).thenReturn(null);
        when(pgAdvisoryLock.tryLockWithSessionLevel(Mockito.<EntityManager>any(), Mockito.<Integer>any(),
            Mockito.<String>any())).thenReturn(true);
        when(pgAdvisoryLock.unLock(Mockito.<EntityManager>any(), Mockito.<Integer>any(), Mockito.<String>any()))
            .thenReturn(true);
        SessionDelegatorBaseImpl delegate = mock(SessionDelegatorBaseImpl.class);
        doNothing().when(delegate).close();
        SessionDelegatorBaseImpl sessionDelegatorBaseImpl = new SessionDelegatorBaseImpl(delegate);
        when(entityManagerFactory.createEntityManager()).thenReturn(sessionDelegatorBaseImpl);

        // Act
        ItemCostCollectionDto actualCreateResult = itemCostCollectionApplicationServiceImpl
            .create(new CreateItemCostCollectionCommand(ItemCostCollectionSources.MANUAL_UPLOADED, "Vendor Name",
                AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID, ItemCostCollectionTypes.CSV_FILE, "42", "foo.txt"));

        // Assert
        verify(itemCostCollectionApplicationMapper).domainToDto(isNull());
        verify(businessEventService).dispatch(isA(BusinessEventPayloadDto.class));
        verify(itemCostCollectionService).save(isA(ItemCostCollection.class));
        verify(entityManagerFactory).createEntityManager();
        verify(delegate).close();
        assertEquals("42", actualCreateResult.getCollectionNumber());
        assertEquals("42", actualCreateResult.getVendorCollectionNumber());
        assertEquals("Vendor Name", actualCreateResult.getVendorName());
        UUID id = actualCreateResult.getId();
        assertEquals("d6b888b8-8e53-4dcd-b5e3-63be5036e0c0", id.toString());
        assertEquals("foo.txt", actualCreateResult.getFileName());
        assertEquals(3, actualCreateResult.getApprovedCount().intValue());
        assertEquals(3, actualCreateResult.getPendingCount().intValue());
        assertEquals(3, actualCreateResult.getRejectedCount().intValue());
        assertEquals(ItemCostCollectionSources.MANUAL_UPLOADED, actualCreateResult.getSource());
        assertEquals(ItemCostCollectionTypes.CSV_FILE, actualCreateResult.getType());
        assertSame(id, actualCreateResult.getVendorId());
    }


    @Test
    void testCreate_thenThrowImsBusinessException() {
        // Arrange
        when(entityManagerFactory.createEntityManager()).thenThrow(new ImsBusinessException("yyyyMMddHHmmss"));
        CreateItemCostCollectionCommand command = new CreateItemCostCollectionCommand(ItemCostCollectionSources.MANUAL_UPLOADED,
            "Vendor Name",
            AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID,
            ItemCostCollectionTypes.CSV_FILE,
            "42",
            "foo.txt");
        // Act and Assert
        assertThrows(ImsBusinessException.class,
            () -> itemCostCollectionApplicationServiceImpl
                .create(command));
        verify(entityManagerFactory).createEntityManager();
    }

    @Test
    void testGetItemCostCollectionFile() {
        // Arrange
        UUID id = UUID.randomUUID();
        ItemCostCollection itemCostCollection = ItemCostCollection.builder()
            .fileName("test.xlsx")
            .build();
        when(itemCostCollectionService.findById(id)).thenReturn(itemCostCollection);
        when(documentApplicationService.getSignedUrl("test.xlsx")).thenReturn("http://test.com/test.xlsx");

        // Act
        DocumentResponse result = itemCostCollectionApplicationServiceImpl.getItemCostCollectionFile(id);

        // Assert
        assertEquals("test.xlsx", result.getName());
        assertEquals("http://test.com/test.xlsx", result.getSignedUrl());
    }


    @Test
    void testGetItemCostCollectionFile_givenItemCostCollectionServiceFindByIdReturnNull() {
        // Arrange
        when(itemCostCollectionService.findById(Mockito.<UUID>any())).thenReturn(null);

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemCostCollectionApplicationServiceImpl
            .getItemCostCollectionFile(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID));
        verify(itemCostCollectionService).findById(isA(UUID.class));
    }

    @Test
    void testGetItemCostCollectionFile_thenReturnNameIsFooTxt() {
        // Arrange
        ItemCostCollection itemCostCollection = mock(ItemCostCollection.class);
        when(itemCostCollection.getFileName()).thenReturn("foo.txt");
        when(itemCostCollectionService.findById(Mockito.<UUID>any())).thenReturn(itemCostCollection);
        when(documentApplicationService.getSignedUrl(Mockito.<String>any())).thenReturn("https://example.org/example");

        // Act
        DocumentResponse actualItemCostCollectionFile = itemCostCollectionApplicationServiceImpl
            .getItemCostCollectionFile(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID);

        // Assert
        verify(documentApplicationService).getSignedUrl("foo.txt");
        verify(itemCostCollection, atLeast(1)).getFileName();
        verify(itemCostCollectionService).findById(isA(UUID.class));
        assertEquals("foo.txt", actualItemCostCollectionFile.getName());
        assertEquals("https://example.org/example", actualItemCostCollectionFile.getSignedUrl());
    }
}
