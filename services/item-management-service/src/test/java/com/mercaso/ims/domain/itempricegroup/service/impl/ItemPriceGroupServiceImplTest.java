package com.mercaso.ims.domain.itempricegroup.service.impl;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroupRepository;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.utils.itempricegroup.ItemPriceGroupUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class ItemPriceGroupServiceImplTest {

    @Mock
    ItemPriceGroupRepository itemPriceGroupRepository;
    @InjectMocks
    ItemPriceGroupServiceImpl itemPriceGroupServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSave() {
        ItemPriceGroup itemPriceGroup = ItemPriceGroupUtil.buildItemPriceGroup();
        when(itemPriceGroupRepository.save(any())).thenReturn(itemPriceGroup);

        ItemPriceGroup result = itemPriceGroupServiceImpl.save(itemPriceGroup);
        Assertions.assertEquals(itemPriceGroup.getGroupName(), result.getGroupName());
    }

    @Test
    void testUpdateGroupName() {
        ItemPriceGroup itemPriceGroup = ItemPriceGroupUtil.buildItemPriceGroup();

        when(itemPriceGroupRepository.findByGroupName(anyString())).thenReturn(null);
        when(itemPriceGroupRepository.save(any())).thenReturn(itemPriceGroup);
        when(itemPriceGroupRepository.findById(any())).thenReturn(itemPriceGroup);

        ItemPriceGroup result = itemPriceGroupServiceImpl.updateGroupName(itemPriceGroup.getId(), "groupName");
        Assertions.assertEquals(itemPriceGroup.getGroupName(), result.getGroupName());
    }

    @Test
    void shouldThrowExceptionAsUpdateExistGroupName() {
        ItemPriceGroup itemPriceGroup = ItemPriceGroupUtil.buildItemPriceGroup();
        UUID id = itemPriceGroup.getId();
        when(itemPriceGroupRepository.findByGroupName(anyString())).thenReturn(itemPriceGroup);
        Assert.assertThrows(ImsBusinessException.class, () -> itemPriceGroupServiceImpl.updateGroupName(id, "groupName"));
    }

    @Test
    void testFindByGroupName() {
        ItemPriceGroup itemPriceGroup = ItemPriceGroupUtil.buildItemPriceGroup();

        when(itemPriceGroupRepository.findByGroupName(anyString())).thenReturn(itemPriceGroup);

        ItemPriceGroup result = itemPriceGroupServiceImpl.findByGroupName("groupName");
        Assertions.assertEquals(itemPriceGroup.getGroupName(), result.getGroupName());
    }

    @Test
    @DisplayName("Test findAll(); given ItemPriceGroupRepository findAll() return ArrayList(); then return Empty")
    void testFindAll_givenItemPriceGroupRepositoryFindAllReturnArrayList_thenReturnEmpty() {
        // Arrange
        when(itemPriceGroupRepository.findAll()).thenReturn(new ArrayList<>());

        // Act
        List<ItemPriceGroup> actualFindAllResult = itemPriceGroupServiceImpl.findAll();

        // Assert
        verify(itemPriceGroupRepository).findAll();
        assertTrue(actualFindAllResult.isEmpty());
    }

    /**
     * Test {@link ItemPriceGroupServiceImpl#findAll()}.
     * <ul>
     *   <li>Then throw {@link ImsBusinessException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link ItemPriceGroupServiceImpl#findAll()}
     */
    @Test
    @DisplayName("Test findAll(); then throw ImsBusinessException")
    void testFindAll_thenThrowImsBusinessException() {
        // Arrange
        when(itemPriceGroupRepository.findAll()).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemPriceGroupServiceImpl.findAll());
        verify(itemPriceGroupRepository).findAll();
    }

    /**
     * Test {@link ItemPriceGroupServiceImpl#findByFuzzyName(String)}.
     * <ul>
     *   <li>Then return Empty.</li>
     * </ul>
     * <p>
     * Method under test: {@link ItemPriceGroupServiceImpl#findByFuzzyName(String)}
     */
    @Test
    @DisplayName("Test findByFuzzyName(String); then return Empty")
    void testFindByFuzzyName_thenReturnEmpty() {
        // Arrange
        when(itemPriceGroupRepository.findByFuzzyName(Mockito.<String>any())).thenReturn(new ArrayList<>());

        // Act
        List<ItemPriceGroup> actualFindByFuzzyNameResult = itemPriceGroupServiceImpl.findByFuzzyName("Group Name");

        // Assert
        verify(itemPriceGroupRepository).findByFuzzyName("Group Name");
        assertTrue(actualFindByFuzzyNameResult.isEmpty());
    }

    /**
     * Test {@link ItemPriceGroupServiceImpl#findByFuzzyName(String)}.
     * <ul>
     *   <li>Then throw {@link ImsBusinessException}.</li>
     * </ul>
     * <p>
     * Method under test: {@link ItemPriceGroupServiceImpl#findByFuzzyName(String)}
     */
    @Test
    @DisplayName("Test findByFuzzyName(String); then throw ImsBusinessException")
    void testFindByFuzzyName_thenThrowImsBusinessException() {
        // Arrange
        when(itemPriceGroupRepository.findByFuzzyName(Mockito.<String>any())).thenThrow(new ImsBusinessException("Code"));

        // Act and Assert
        assertThrows(ImsBusinessException.class, () -> itemPriceGroupServiceImpl.findByFuzzyName("Group Name"));
        verify(itemPriceGroupRepository).findByFuzzyName("Group Name");
    }
}