package com.mercaso.ims.utils.vendoritem;

import com.mercaso.ims.domain.vendoritem.VendorItem;
import java.math.BigDecimal;
import java.util.UUID;

public class VendorItemUtil {
    
    public static VendorItem buildVendorItem(UUID id) {
        return VendorItem.builder()
            .id(id)
            .vendorSkuNumber("DEFAULT-SKU")
            .vendorItemName("Default Item")
            .packNoCrvCost(BigDecimal.ZERO)
            .note("Test Note")
            .aisle("Test Aisle")
            .availability(true)
            .build();
    }
} 