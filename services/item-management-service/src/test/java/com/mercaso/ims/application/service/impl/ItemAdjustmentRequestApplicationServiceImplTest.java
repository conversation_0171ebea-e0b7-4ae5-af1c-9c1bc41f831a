package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentFailureProcessedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestCompletedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestProcessedPayloadDto;
import com.mercaso.ims.application.mapper.itemadjustmentrequest.ItemAdjustmentRequestDtoApplicationMapper;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequest;
import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequestRepository;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentTransitionEvents;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.service.ItemAdjustmentRequestDetailService;
import com.mercaso.ims.infrastructure.excel.generator.ItemAdjustmentDetailExcelGenerator;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.statemachine.processor.StateMachineProcessor;
import com.mercaso.ims.infrastructure.util.SpringContextUtil;
import com.mercaso.ims.utils.Itemadjustmentrequest.ItemAdjustmentRequestDtoUtil;
import com.mercaso.ims.utils.Itemadjustmentrequest.ItemAdjustmentRequestUtil;
import com.mercaso.ims.utils.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailUtil;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class ItemAdjustmentRequestApplicationServiceImplTest {

    @Mock
    ItemAdjustmentDetailExcelGenerator itemAdjustmentDetailExcelGenerator;
    @Mock
    ItemAdjustmentRequestDtoApplicationMapper itemAdjustmentRequestDtoApplicationMapper;
    @Mock
    DocumentOperations documentOperations;
    @Mock
    DocumentApplicationService documentApplicationService;
    @Mock
    ItemAdjustmentRequestRepository itemAdjustmentRequestRepository;
    @Mock
    BusinessEventService businessEventService;
    @Mock
    StateMachineProcessor<ItemAdjustmentRequestDetail, ItemAdjustmentStatus, ItemAdjustmentTransitionEvents> stateMachineProcessor;
    @Mock
    ItemAdjustmentRequestDetailService itemAdjustmentRequestDetailService;

    @InjectMocks
    ItemAdjustmentRequestApplicationServiceImpl itemAdjustmentRequestApplicationServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void testProcessedFailure() {

        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {
            when(itemAdjustmentRequestDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequest.class))).thenReturn(
                ItemAdjustmentRequestDtoUtil.buildItemAdjustmentRequestDto(UUID.randomUUID()));
            when(itemAdjustmentRequestRepository.findById(any())).thenReturn(ItemAdjustmentRequestUtil.buildItemAdjustmentRequest(
                UUID.randomUUID()));
            when(itemAdjustmentRequestDtoApplicationMapper.domainToDto(any())).thenReturn(ItemAdjustmentRequestDtoUtil.buildItemAdjustmentRequestDto(
                UUID.randomUUID()));
            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            itemAdjustmentRequestApplicationServiceImpl.processedFailure(UUID.randomUUID(), "test");
            verify(businessEventService).dispatch(any(ItemAdjustmentFailureProcessedPayloadDto.class));
        }
    }

    @Test
    void testDownloadItemAdjustmentDetail() {

        UUID itemAdjustmentRequestId = UUID.randomUUID();
        byte[] content = new byte[0];

        ItemAdjustmentRequest itemAdjustmentRequest = ItemAdjustmentRequestUtil.buildItemAdjustmentRequest(itemAdjustmentRequestId);

        when(itemAdjustmentRequestRepository.findById(itemAdjustmentRequestId)).thenReturn(itemAdjustmentRequest);

        when(itemAdjustmentDetailExcelGenerator.generate(itemAdjustmentRequestId)).thenReturn(content);
        when(documentApplicationService.uploadExcel(any(), any())).thenReturn(new DocumentResponse("signedUrl",
            "name"));

        itemAdjustmentRequestApplicationServiceImpl.downloadItemAdjustmentDetail(itemAdjustmentRequestId);

        verify(itemAdjustmentRequestRepository, times(1)).findById(itemAdjustmentRequestId);
        verify(itemAdjustmentDetailExcelGenerator, times(1)).generate(itemAdjustmentRequestId);
        verify(documentApplicationService, times(1)).uploadExcel(any(), any());


    }

    @Test
    void testDownloadItemAdjustmentDetail_ItemAdjustmentRequestNotFound() {
        UUID id = UUID.randomUUID();
        when(itemAdjustmentRequestRepository.findById(any(UUID.class))).thenReturn(null);

        assertThrows(ImsBusinessException.class,
            () -> itemAdjustmentRequestApplicationServiceImpl.downloadItemAdjustmentDetail(id));

        verify(itemAdjustmentRequestRepository, times(1)).findById(any(UUID.class));

    }

    @Test
    void testCheckAndComplete_ItemNotFound() {
        UUID id = UUID.randomUUID();

        when(itemAdjustmentRequestRepository.findById(id)).thenReturn(null);

        assertThrows(ImsBusinessException.class, () -> itemAdjustmentRequestApplicationServiceImpl.checkAndComplete(id));
    }

    @Test
    void testCheckAndComplete_StatusNotFileProcessed() {
        UUID id = UUID.randomUUID();
        ItemAdjustmentRequest itemAdjustmentRequest = ItemAdjustmentRequestUtil.buildItemAdjustmentRequest(id);
        itemAdjustmentRequest.setStatus(ItemAdjustmentRequestStatus.UPLOADED);

        when(itemAdjustmentRequestRepository.findById(id)).thenReturn(itemAdjustmentRequest);

        itemAdjustmentRequestApplicationServiceImpl.checkAndComplete(id);

        verify(itemAdjustmentRequestRepository, never()).save(any());
        verify(businessEventService, never()).dispatch(any(ItemAdjustmentRequestCompletedPayloadDto.class));
    }

    @Test
    void testCheckAndComplete_AllDetailsCompleted() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {
            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            UUID id = UUID.randomUUID();
            ItemAdjustmentRequest itemAdjustmentRequest = ItemAdjustmentRequestUtil.buildItemAdjustmentRequest(id);
            itemAdjustmentRequest.setStatus(ItemAdjustmentRequestStatus.FILE_PROCESSED);

            ItemAdjustmentRequestDetail detail1 = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(
                UUID.randomUUID());
            detail1.setStatus(ItemAdjustmentStatus.IMS_UPDATED);
            ItemAdjustmentRequestDetail detai2 = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(
                UUID.randomUUID());
            detai2.setStatus(ItemAdjustmentStatus.SHOPIFY_SYNCHRONIZED);
            List<ItemAdjustmentRequestDetail> details = Arrays.asList(detail1, detai2);

            when(itemAdjustmentRequestRepository.findById(id)).thenReturn(itemAdjustmentRequest);
            when(itemAdjustmentRequestRepository.save(any())).thenReturn(itemAdjustmentRequest);
            when(itemAdjustmentRequestDetailService.findByItemAdjustmentRequestId(id)).thenReturn(details);
            when(itemAdjustmentRequestDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequest.class))).thenReturn(
                ItemAdjustmentRequestDtoUtil.buildItemAdjustmentRequestDto(UUID.randomUUID()));
            itemAdjustmentRequestApplicationServiceImpl.checkAndComplete(id);

            verify(itemAdjustmentRequestRepository).save(itemAdjustmentRequest);
            verify(businessEventService).dispatch(any(ItemAdjustmentRequestCompletedPayloadDto.class));
        }
    }

    @Test
    void testCheckAndComplete_SomeDetailsNotCompleted() {
        UUID id = UUID.randomUUID();
        ItemAdjustmentRequest itemAdjustmentRequest = ItemAdjustmentRequestUtil.buildItemAdjustmentRequest(id);
        itemAdjustmentRequest.setStatus(ItemAdjustmentRequestStatus.FILE_PROCESSED);

        ItemAdjustmentRequestDetail detail1 = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(
            UUID.randomUUID());
        detail1.setStatus(ItemAdjustmentStatus.IMS_UPDATED);
        ItemAdjustmentRequestDetail detai2 = ItemAdjustmentRequestDetailUtil.buildItemAdjustmentRequestDetail(
            UUID.randomUUID());
        detai2.setStatus(ItemAdjustmentStatus.PENDING);
        List<ItemAdjustmentRequestDetail> details = Arrays.asList(detail1, detai2);

        when(itemAdjustmentRequestRepository.findById(id)).thenReturn(itemAdjustmentRequest);
        when(itemAdjustmentRequestDetailService.findByItemAdjustmentRequestId(id)).thenReturn(details);

        itemAdjustmentRequestApplicationServiceImpl.checkAndComplete(id);

        verify(itemAdjustmentRequestRepository, never()).save(any());
        verify(businessEventService, never()).dispatch(any(ItemAdjustmentRequestCompletedPayloadDto.class));
    }

    @Test
    void testFinishProcessed() {
        try (MockedStatic<SpringContextUtil> mockedStatic = Mockito.mockStatic(SpringContextUtil.class)) {
            mockedStatic.when((Verification) SpringContextUtil.getBean(any(Class.class))).thenReturn(stateMachineProcessor);
            UUID id = UUID.randomUUID();
            ItemAdjustmentRequest itemAdjustmentRequest = ItemAdjustmentRequestUtil.buildItemAdjustmentRequest(id);

            when(itemAdjustmentRequestRepository.findById(id)).thenReturn(itemAdjustmentRequest);
            when(itemAdjustmentRequestRepository.save(any())).thenReturn(itemAdjustmentRequest);
            when(itemAdjustmentRequestDtoApplicationMapper.domainToDto(any(ItemAdjustmentRequest.class))).thenReturn(
                ItemAdjustmentRequestDtoUtil.buildItemAdjustmentRequestDto(UUID.randomUUID()));

            itemAdjustmentRequestApplicationServiceImpl.finishProcessed(id);

            verify(itemAdjustmentRequestRepository).save(itemAdjustmentRequest);

            verify(businessEventService).dispatch(any(ItemAdjustmentRequestProcessedPayloadDto.class));
        }
    }

    @Test
    void testFinishProcessedNotFound() {
        UUID id = UUID.randomUUID();

        when(itemAdjustmentRequestRepository.findById(id)).thenReturn(null);

        assertThrows(ImsBusinessException.class, () -> itemAdjustmentRequestApplicationServiceImpl.finishProcessed(id));
    }

}

