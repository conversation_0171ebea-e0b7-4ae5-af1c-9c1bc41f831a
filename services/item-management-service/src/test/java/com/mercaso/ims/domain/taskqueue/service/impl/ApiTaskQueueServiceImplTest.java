package com.mercaso.ims.domain.taskqueue.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;
import com.mercaso.ims.domain.taskqueue.repository.ApiTaskQueueRepository;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

/**
 * Unit tests for ApiTaskQueueServiceImpl
 */
@ContextConfiguration(classes = {ApiTaskQueueServiceImpl.class})
class ApiTaskQueueServiceImplTest extends AbstractTest {

    @MockBean
    private ApiTaskQueueRepository apiTaskQueueRepository;

    @Autowired
    private ApiTaskQueueServiceImpl apiTaskQueueService;

    private ApiTaskQueue mockTask;
    private UUID taskId;
    private final String taskType = "TEST_TASK";
    private final String apiEndpoint = "/api/test";
    private final String httpMethod = "POST";
    private final String requestPayload = "{\"test\": \"data\"}";

    @BeforeEach
    void setUp() {
        taskId = UUID.randomUUID();
        mockTask = mock(ApiTaskQueue.class); // Create a proper mock instead of using builder

        // Set up basic mock behavior for commonly used getters
        when(mockTask.getId()).thenReturn(taskId);
        when(mockTask.getTaskType()).thenReturn(taskType);
        when(mockTask.getApiEndpoint()).thenReturn(apiEndpoint);
        when(mockTask.getHttpMethod()).thenReturn(httpMethod);
        when(mockTask.getRequestPayload()).thenReturn(requestPayload);
        when(mockTask.getStatus()).thenReturn(TaskStatus.PENDING);
        when(mockTask.getPriority()).thenReturn(0);
        when(mockTask.getMaxRetryCount()).thenReturn(3);
        when(mockTask.getCurrentRetryCount()).thenReturn(0);
        when(mockTask.getCreatedAt()).thenReturn(Instant.now());
    }

    @Test
    void shouldCreateTaskWithAllParameters() {
        // Arrange
        Integer priority = 5;
        Integer maxRetryCount = 5;
        when(apiTaskQueueRepository.save(any(ApiTaskQueue.class))).thenReturn(mockTask);

        // Act
        ApiTaskQueue result = apiTaskQueueService.createTask(taskType, apiEndpoint, httpMethod, requestPayload, priority, maxRetryCount);

        // Assert
        assertNotNull(result);
        verify(apiTaskQueueRepository).save(argThat(task ->
                task.getTaskType().equals(taskType) &&
                        task.getApiEndpoint().equals(apiEndpoint) &&
                        task.getHttpMethod().equals(httpMethod) &&
                        task.getRequestPayload().equals(requestPayload) &&
                        task.getStatus() == TaskStatus.PENDING &&
                        task.getPriority().equals(priority) &&
                        task.getMaxRetryCount().equals(maxRetryCount) &&
                        task.getCurrentRetryCount() == 0
        ));
    }


    @Test
    void shouldCreateScheduledTask() {
        // Arrange
        Instant scheduledAt = Instant.now().plusSeconds(3600);
        when(apiTaskQueueRepository.save(any(ApiTaskQueue.class))).thenReturn(mockTask);

        // Act
        ApiTaskQueue result = apiTaskQueueService.createScheduledTask(taskType, apiEndpoint, httpMethod, requestPayload, scheduledAt);

        // Assert
        assertNotNull(result);
        verify(apiTaskQueueRepository).save(argThat(task ->
                task.getScheduledAt().equals(scheduledAt) &&
                        task.getStatus() == TaskStatus.PENDING
        ));
    }

    @Test
    void shouldUseDefaultValuesForNullParameters() {
        // Arrange
        when(apiTaskQueueRepository.save(any(ApiTaskQueue.class))).thenReturn(mockTask);

        // Act
        apiTaskQueueService.createTask(taskType, apiEndpoint, httpMethod, requestPayload, null, null);

        // Assert
        verify(apiTaskQueueRepository).save(argThat(task ->
                task.getPriority() == 0 &&
                        task.getMaxRetryCount() == 3
        ));
    }

    @Test
    void shouldGetExecutableTasksWithTaskTypeFilter() {
        // Arrange
        List<ApiTaskQueue> mockTasks = List.of(
                createMockTaskWithPriority(1, "TYPE_A"),
                createMockTaskWithPriority(2, "TYPE_A"),
                createMockTaskWithPriority(1, "TYPE_B")
        );
        when(apiTaskQueueRepository.findExecutableTasks(any(), eq(10))).thenReturn(mockTasks);

        // Act
        List<ApiTaskQueue> result = apiTaskQueueService.getExecutableTasks(10);

        // Assert
        assertEquals(3, result.size());
        assertTrue(result.stream().allMatch(task -> task.getTaskType().contains("TYPE_")));
        verify(apiTaskQueueRepository).findExecutableTasks(List.of(TaskStatus.PENDING, TaskStatus.RETRY), 10);
    }

    @Test
    void shouldGetAllExecutableTasksWhenTaskTypeIsNull() {
        // Arrange
        List<ApiTaskQueue> mockTasks = List.of(
                createMockTaskWithPriority(1, "TYPE_A"),
                createMockTaskWithPriority(2, "TYPE_B")
        );
        when(apiTaskQueueRepository.findExecutableTasks(any(), eq(5))).thenReturn(mockTasks);

        // Act
        List<ApiTaskQueue> result = apiTaskQueueService.getExecutableTasks(5);

        // Assert
        assertEquals(2, result.size());
    }

    @Test
    void shouldFilterOutScheduledTasksNotYetReady() {
        // Arrange
        ApiTaskQueue futureTask = createMockTaskWithScheduledAt(Instant.now().plusSeconds(3600));
        ApiTaskQueue readyTask = createMockTaskWithScheduledAt(Instant.now().minusSeconds(3600));
        List<ApiTaskQueue> mockTasks = List.of(futureTask, readyTask);
        when(apiTaskQueueRepository.findExecutableTasks(any(), eq(10))).thenReturn(mockTasks);

        // Act
        List<ApiTaskQueue> result = apiTaskQueueService.getExecutableTasks( 10);

        // Assert
        assertEquals(1, result.size());
        assertEquals(readyTask.getId(), result.get(0).getId());
    }

    @Test
    void shouldSortTasksByPriorityThenByCreatedTime() {
        // Arrange
        Instant baseTime = Instant.now();
        ApiTaskQueue lowPriorityOld = createMockTaskWithPriorityAndTime(1, baseTime.minusSeconds(100));
        ApiTaskQueue lowPriorityNew = createMockTaskWithPriorityAndTime(1, baseTime.minusSeconds(50));
        ApiTaskQueue highPriorityOld = createMockTaskWithPriorityAndTime(5, baseTime.minusSeconds(80));

        List<ApiTaskQueue> mockTasks = List.of(lowPriorityNew, highPriorityOld, lowPriorityOld);
        when(apiTaskQueueRepository.findExecutableTasks(any(), eq(10))).thenReturn(mockTasks);

        // Act
        List<ApiTaskQueue> result = apiTaskQueueService.getExecutableTasks(10);

        // Assert
        assertEquals(3, result.size());
        assertEquals(highPriorityOld.getId(), result.get(0).getId()); // Highest priority first
        assertEquals(lowPriorityOld.getId(), result.get(1).getId());  // Earlier created first
        assertEquals(lowPriorityNew.getId(), result.get(2).getId());  // Later created last
    }

    @Test
    void shouldGetPendingTaskTypes() {
        // Arrange
        List<String> expectedTypes = List.of("TYPE_A", "TYPE_B", "TYPE_C");
        when(apiTaskQueueRepository.findDistinctTaskTypesByStatus(any())).thenReturn(expectedTypes);

        // Act
        List<String> result = apiTaskQueueService.getPendingTaskTypes();

        // Assert
        assertEquals(expectedTypes, result);
        verify(apiTaskQueueRepository).findDistinctTaskTypesByStatus(List.of(TaskStatus.PENDING, TaskStatus.RETRY));
    }

    @Test
    void shouldGetTaskById() {
        // Arrange
        when(apiTaskQueueRepository.findById(taskId)).thenReturn(Optional.of(mockTask));

        // Act
        Optional<ApiTaskQueue> result = apiTaskQueueService.getTaskById(taskId);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(mockTask, result.get());
    }

    @Test
    void shouldReturnEmptyWhenTaskNotFound() {
        // Arrange
        when(apiTaskQueueRepository.findById(taskId)).thenReturn(Optional.empty());

        // Act
        Optional<ApiTaskQueue> result = apiTaskQueueService.getTaskById(taskId);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    void shouldUpdateTaskStatus() {
        // Arrange
        when(apiTaskQueueRepository.findById(taskId)).thenReturn(Optional.of(mockTask));
        when(apiTaskQueueRepository.save(mockTask)).thenReturn(mockTask);

        // Act
        ApiTaskQueue result = apiTaskQueueService.updateTaskStatus(taskId, TaskStatus.PROCESSING);

        // Assert
        assertNotNull(result);
        verify(mockTask).setStatus(TaskStatus.PROCESSING);
        verify(apiTaskQueueRepository).save(mockTask);
    }

    @Test
    void shouldThrowExceptionWhenUpdatingNonExistentTaskStatus() {
        // Arrange
        when(apiTaskQueueRepository.findById(taskId)).thenReturn(Optional.empty());

        // Act and Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> apiTaskQueueService.updateTaskStatus(taskId, TaskStatus.PROCESSING));
        assertEquals("Task not found: " + taskId, exception.getMessage());
    }

    @Test
    void shouldMarkTaskAsStarted() {
        // Arrange
        when(apiTaskQueueRepository.findById(taskId)).thenReturn(Optional.of(mockTask));
        when(apiTaskQueueRepository.save(mockTask)).thenReturn(mockTask);

        // Act
        ApiTaskQueue result = apiTaskQueueService.markTaskAsStarted(taskId);

        // Assert
        assertNotNull(result);
        verify(mockTask).markAsStarted();
        verify(apiTaskQueueRepository).save(mockTask);
    }

    @Test
    void shouldGetTasksByStatus() {
        // Arrange
        List<ApiTaskQueue> expectedTasks = List.of(mockTask);
        when(apiTaskQueueRepository.findByStatus(TaskStatus.PENDING)).thenReturn(expectedTasks);

        // Act
        List<ApiTaskQueue> result = apiTaskQueueService.getTasksByStatus(TaskStatus.PENDING);

        // Assert
        assertEquals(expectedTasks, result);
    }

    @Test
    void shouldGetTaskCountByStatus() {
        // Arrange
        when(apiTaskQueueRepository.countByStatus(TaskStatus.PENDING)).thenReturn(5L);

        // Act
        long result = apiTaskQueueService.getTaskCountByStatus(TaskStatus.PENDING);

        // Assert
        assertEquals(5L, result);
    }

    @Test
    void shouldGetTaskCountByTypeAndStatus() {
        // Arrange
        when(apiTaskQueueRepository.countByTaskTypeAndStatus(taskType, TaskStatus.PENDING)).thenReturn(3L);

        // Act
        long result = apiTaskQueueService.getTaskCountByTypeAndStatus(taskType, TaskStatus.PENDING);

        // Assert
        assertEquals(3L, result);
    }

    @Test
    void shouldFindStuckTasks() {
        // Arrange
        Duration timeout = Duration.ofMinutes(30);
        List<ApiTaskQueue> stuckTasks = List.of(mockTask);
        when(apiTaskQueueRepository.findStuckProcessingTasks(any(Instant.class))).thenReturn(stuckTasks);

        // Act
        List<ApiTaskQueue> result = apiTaskQueueService.findStuckTasks(timeout);

        // Assert
        assertEquals(stuckTasks, result);
        verify(apiTaskQueueRepository).findStuckProcessingTasks(any(Instant.class));
    }

    @Test
    void shouldResetStuckTasks() {
        // Arrange
        Duration timeout = Duration.ofMinutes(30);
        ApiTaskQueue stuckTask1 = mock(ApiTaskQueue.class);
        ApiTaskQueue stuckTask2 = mock(ApiTaskQueue.class);
        List<ApiTaskQueue> stuckTasks = List.of(stuckTask1, stuckTask2);

        when(apiTaskQueueRepository.findStuckProcessingTasks(any(Instant.class))).thenReturn(stuckTasks);
        when(stuckTask1.getId()).thenReturn(UUID.randomUUID());
        when(stuckTask2.getId()).thenReturn(UUID.randomUUID());
        when(stuckTask1.getStartedAt()).thenReturn(Instant.now().minusSeconds(3600));
        when(stuckTask2.getStartedAt()).thenReturn(Instant.now().minusSeconds(3600));

        // Act
        int result = apiTaskQueueService.resetStuckTasks(timeout);

        // Assert
        assertEquals(2, result);
        verify(stuckTask1).setStatus(TaskStatus.PENDING);
        verify(stuckTask1).setStartedAt(null);
        verify(stuckTask1).setErrorMessage("Reset due to timeout");
        verify(stuckTask2).setStatus(TaskStatus.PENDING);
        verify(stuckTask2).setStartedAt(null);
        verify(stuckTask2).setErrorMessage("Reset due to timeout");
        verify(apiTaskQueueRepository, times(2)).save(any(ApiTaskQueue.class));
    }

    @Test
    void shouldCleanupCompletedTasks() {
        // Arrange
        Duration olderThan = Duration.ofDays(7);
        when(apiTaskQueueRepository.cleanupCompletedTasks(any(Instant.class))).thenReturn(10);

        // Act
        int result = apiTaskQueueService.cleanupCompletedTasks(olderThan);

        // Assert
        assertEquals(10, result);
        verify(apiTaskQueueRepository).cleanupCompletedTasks(any(Instant.class));
    }

    @Test
    void shouldReturnTaskWhenCompletedWithinTimeout() {
        // Arrange
        Duration timeout = Duration.ofSeconds(5);
        ApiTaskQueue completedTask = mock(ApiTaskQueue.class);
        when(completedTask.getStatus()).thenReturn(TaskStatus.COMPLETED);
        when(apiTaskQueueRepository.findById(taskId))
                .thenReturn(Optional.of(completedTask));
        // Remove isTerminal() mock - TaskStatus.COMPLETED.isTerminal() returns true naturally

        // Act
        Optional<ApiTaskQueue> result = apiTaskQueueService.waitForTaskCompletion(taskId, timeout);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(completedTask, result.get());
    }

    @Test
    void shouldReturnEmptyWhenTimeoutExceeded() {
        // Arrange
        Duration timeout = Duration.ofMillis(1); // Very short timeout
        ApiTaskQueue processingTask = mock(ApiTaskQueue.class);
        when(processingTask.getStatus()).thenReturn(TaskStatus.PROCESSING);
        when(apiTaskQueueRepository.findById(taskId))
                .thenReturn(Optional.of(processingTask));
        // Remove isTerminal() mock - TaskStatus.PROCESSING.isTerminal() returns false naturally

        // Act
        Optional<ApiTaskQueue> result = apiTaskQueueService.waitForTaskCompletion(taskId, timeout);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    void shouldReturnEmptyWhenTaskNotFoundInWaitForCompletion() {
        // Arrange
        Duration timeout = Duration.ofSeconds(1);
        when(apiTaskQueueRepository.findById(taskId)).thenReturn(Optional.empty());

        // Act
        Optional<ApiTaskQueue> result = apiTaskQueueService.waitForTaskCompletion(taskId, timeout);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    void shouldHandleInterruptCorrectly() {
        // Arrange
        Duration timeout = Duration.ofSeconds(1);
        ApiTaskQueue processingTask = mock(ApiTaskQueue.class);
        when(processingTask.getStatus()).thenReturn(TaskStatus.PROCESSING);
        when(apiTaskQueueRepository.findById(taskId))
                .thenReturn(Optional.of(processingTask));
        // Remove isTerminal() mock - TaskStatus.PROCESSING.isTerminal() returns false naturally

        // Interrupt the current thread after a short delay
        Thread.currentThread().interrupt();

        // Act
        Optional<ApiTaskQueue> result = apiTaskQueueService.waitForTaskCompletion(taskId, timeout);

        // Assert
        assertFalse(result.isPresent());
        assertTrue(Thread.currentThread().isInterrupted());
    }

    @Test
    void shouldMarkTaskAsCompleted() {
        // Arrange
        String responsePayload = "{\"result\": \"success\"}";
        when(apiTaskQueueRepository.findById(taskId)).thenReturn(Optional.of(mockTask));
        when(apiTaskQueueRepository.save(mockTask)).thenReturn(mockTask);

        // Act
        ApiTaskQueue result = apiTaskQueueService.markTaskAsCompleted(taskId, responsePayload);

        // Assert
        assertNotNull(result);
        verify(mockTask).markAsCompleted(responsePayload);
        verify(apiTaskQueueRepository).save(mockTask);
    }

    @Test
    void shouldMarkTaskAsFailed() {
        // Arrange
        String errorMessage = "Task failed due to network error";
        when(apiTaskQueueRepository.findById(taskId)).thenReturn(Optional.of(mockTask));
        when(apiTaskQueueRepository.save(mockTask)).thenReturn(mockTask);

        // Act
        ApiTaskQueue result = apiTaskQueueService.markTaskAsFailed(taskId, errorMessage);

        // Assert
        assertNotNull(result);
        verify(mockTask).markAsFailed(errorMessage);
        verify(apiTaskQueueRepository).save(mockTask);
    }

    @Test
    void shouldMarkTaskForRetryWhenNotReachedMaxRetries() {
        // Arrange
        String errorMessage = "Temporary error";
        Duration retryDelay = Duration.ofMinutes(5);
        when(apiTaskQueueRepository.findById(taskId)).thenReturn(Optional.of(mockTask));
        when(mockTask.hasReachedMaxRetries()).thenReturn(false);
        when(apiTaskQueueRepository.save(mockTask)).thenReturn(mockTask);

        // Act
        ApiTaskQueue result = apiTaskQueueService.markTaskForRetry(taskId, errorMessage, retryDelay);

        // Assert
        assertNotNull(result);
        verify(mockTask).markForRetry(errorMessage, retryDelay.getSeconds());
        verify(apiTaskQueueRepository).save(mockTask);
    }

    @Test
    void shouldMarkTaskAsFailedWhenReachedMaxRetries() {
        // Arrange
        String errorMessage = "Persistent error";
        Duration retryDelay = Duration.ofMinutes(5);
        when(apiTaskQueueRepository.findById(taskId)).thenReturn(Optional.of(mockTask));
        when(mockTask.hasReachedMaxRetries()).thenReturn(true);
        when(apiTaskQueueRepository.save(mockTask)).thenReturn(mockTask);

        // Act
        ApiTaskQueue result = apiTaskQueueService.markTaskForRetry(taskId, errorMessage, retryDelay);

        // Assert
        assertNotNull(result);
        verify(mockTask).markAsFailed(errorMessage + " (Max retries exceeded)");
        verify(apiTaskQueueRepository).save(mockTask);
    }

    private ApiTaskQueue createMockTaskWithPriority(int priority, String taskType) {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType(taskType)
                .priority(priority)
                .status(TaskStatus.PENDING)
                .createdAt(Instant.now())
                .build();
    }

    private ApiTaskQueue createMockTaskWithScheduledAt(Instant scheduledAt) {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType(taskType)
                .status(TaskStatus.PENDING)
                .scheduledAt(scheduledAt)
                .createdAt(Instant.now())
                .build();
    }

    private ApiTaskQueue createMockTaskWithPriorityAndTime(int priority, Instant createdAt) {
        return ApiTaskQueue.builder()
                .id(UUID.randomUUID())
                .taskType(taskType)
                .priority(priority)
                .status(TaskStatus.PENDING)
                .createdAt(createdAt)
                .build();
    }
}