package com.mercaso.ims.utils.vendor;

import com.mercaso.ims.application.command.CreateVendorCommand;
import com.mercaso.ims.application.command.UpdateVendorCommand;
import java.util.UUID;

public class VendorCommandUtil {


    public static CreateVendorCommand buildCreateVendorCommand(String vendorName) {
        return CreateVendorCommand.builder()
            .vendorName(vendorName)
            .build();
    }

    public static UpdateVendorCommand buildUpdateVendorCommand(UUID id, String vendorName) {
        return UpdateVendorCommand.builder()
            .vendorId(id)
            .vendorName(vendorName)
            .build();
    }

}
