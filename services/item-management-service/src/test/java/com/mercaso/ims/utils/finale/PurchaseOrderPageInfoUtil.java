package com.mercaso.ims.utils.finale;

import com.google.common.collect.Lists;
import com.mercaso.ims.infrastructure.external.finale.dto.CompletedPurchaseOrderDto;
import com.mercaso.ims.infrastructure.external.finale.dto.CompletedPurchaseOrderItemDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleCompletedPurchaseOrderReportDto;
import com.mercaso.ims.infrastructure.external.finale.dto.QueryFinalePurchaseOrderResultDto.PartySupplierGroupName;
import com.mercaso.ims.infrastructure.external.finale.dto.QueryFinalePurchaseOrderResultDto.PurchaseOrderPageInfo;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class PurchaseOrderPageInfoUtil {

    public static PurchaseOrderPageInfo buildPurchaseOrderPageInfo() {
        PartySupplierGroupName partySupplierGroupName = new PartySupplierGroupName();
        partySupplierGroupName.setName("Vendor Name");
        partySupplierGroupName.setPartyId(RandomStringUtils.randomAlphabetic(6));

        PurchaseOrderPageInfo purchaseOrderPageInfo = new PurchaseOrderPageInfo();
        purchaseOrderPageInfo.setOrderId(RandomStringUtils.randomAlphabetic(6));
        purchaseOrderPageInfo.setOrderDateFormatted("2024-03-12T10:00:00Z");
        purchaseOrderPageInfo.setShipmentsFormatted("Received 4/15/2025");
        purchaseOrderPageInfo.setReceiveDateFormatted("4/15/2025");
        purchaseOrderPageInfo.setStatusIdFormatted("Completed");
        purchaseOrderPageInfo.setPartySupplierGroupName(partySupplierGroupName);
        return purchaseOrderPageInfo;


    }

    public static FinaleCompletedPurchaseOrderReportDto buildFinaleCompletedPurchaseOrderReportDto() {
        return FinaleCompletedPurchaseOrderReportDto.builder()
                .orderId("102824")
                .orderDate("8/5/2025")
                .amount(3659.88)
                .apptDate("8/25/2025")
                .status("Completed")
                .apptTime("06:00 AM")
                .recordLastUpdated("Aug 25 2025 10:24:37 pm")
                .recordLastUpdatedUser("Ivan")
                .dueDate("8/5/2025")
                .customer(null)
                .supplierProductId("100149")
                .supplier("Niagara Bottling")
                .supplierPartyId("100149")
                .productId("AW10000-6")
                .packing(null)
                .pricePerUnit(4.765465)
                .amount(3659.88)
                .productUnitsOrdered("768")
                .description("Niagara, Purified Water, 1 gal (6 Pack) - Pack Plus CRV")
                .shipmentsSummary("Received 8/25/2025")
                .shipmentsStatusSummary("Fully received")
                .type("Purchase")
            .build();
    }

    public static List<CompletedPurchaseOrderDto> buildCompletedPurchaseOrderDto () {
        FinaleCompletedPurchaseOrderReportDto finaleCompletedPurchaseOrderReportDto = buildFinaleCompletedPurchaseOrderReportDto();

        List<FinaleCompletedPurchaseOrderReportDto> reportDtos = Lists.newArrayList(finaleCompletedPurchaseOrderReportDto);

        Map<String, List<FinaleCompletedPurchaseOrderReportDto>> groupedByOrderId = reportDtos.stream()
                .collect(Collectors.groupingBy(FinaleCompletedPurchaseOrderReportDto::getOrderId));

        return groupedByOrderId.values().stream()
                .map(finaleCompletedPurchaseOrderReportDtos -> {
                    FinaleCompletedPurchaseOrderReportDto first = finaleCompletedPurchaseOrderReportDtos.getFirst();
                    List<CompletedPurchaseOrderItemDto> items = finaleCompletedPurchaseOrderReportDtos.stream()
                            .map(dto -> CompletedPurchaseOrderItemDto.builder()
                                    .supplierProductId(dto.getSupplierProductId())
                                    .productId(dto.getProductId())
                                    .packing(dto.getPacking())
                                    .pricePerUnit(dto.getPricePerUnit())
                                    .amount(dto.getAmount())
                                    .productUnitsOrdered(dto.getProductUnitsOrdered())
                                    .description(dto.getDescription())
                                    .build())
                            .toList();

                    return CompletedPurchaseOrderDto.builder()
                            .orderId(first.getOrderId())
                            .status(first.getStatus())
                            .orderDate(first.getOrderDate())
                            .apptDate(first.getApptDate())
                            .apptTime(first.getApptTime())
                            .recordLastUpdated(first.getRecordLastUpdated())
                            .recordLastUpdatedUser(first.getRecordLastUpdatedUser())
                            .dueDate(first.getDueDate())
                            .customer(first.getCustomer())
                            .supplierPartyId(first.getSupplierPartyId())
                            .supplier(first.getSupplier())
                            .shipmentsStatusSummary(first.getShipmentsStatusSummary())
                            .shipmentsSummary(first.getShipmentsSummary())
                            .items(items)
                            .build();
                }).toList();

    }




}
