package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.utils.IntegrationTestRestUtil;

import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class MerchandiseReportRestApiUtil extends IntegrationTestRestUtil {

    private static final String DOWNLOAD_MERCHANDISE_REPORT_URL = "/v1/merchandise-reports/download";
    private static final String DOWNLOAD_FILTERED_MERCHANDISE_REPORT_URL = "/v1/merchandise-reports/download/filter";


    public MerchandiseReportRestApiUtil(Environment environment) {
        super(environment);
    }

    public DocumentResponse downloadMerchandiseReport() throws Exception {
        return getEntity(DOWNLOAD_MERCHANDISE_REPORT_URL, DocumentResponse.class);
    }

    public void downloadFilteredMerchandiseReport(String customFilter) throws Exception {
        String url = DOWNLOAD_FILTERED_MERCHANDISE_REPORT_URL;
        if (customFilter != null && !customFilter.isEmpty()) {
            url += "?customFilter=" + customFilter;
        }
        getEntity(url, DocumentResponse.class);
    }
}

