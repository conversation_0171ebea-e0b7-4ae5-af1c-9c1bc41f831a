package com.mercaso.ims.interfaces.rest.query;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import com.mercaso.ims.utils.interfacesutils.QueryVendorItemRestApiUtil;
import java.util.List;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class QueryVendorItemRestApiIT extends AbstractIT {

    @Autowired
    private QueryVendorItemRestApiUtil queryVendorItemRestApiUtil;

    @Test
    void shouldSuccessWhenSearchVendorItems() throws Exception {
        String vendorName = "vendor_" + RandomStringUtils.randomAlphabetic(5);
        VendorDo vendorDo = buildVendorDoData(vendorName);

        String sku = "sku_" + RandomStringUtils.randomAlphabetic(5);
        ItemDo itemDo = buildItemData(sku);

        // It is assumed here that there is a method to establish a VendorItem relationship, or the data already exists.
        buildVendorItemData("vendor_"+sku, vendorDo.getId(), itemDo.getId());

        List<VendorItemDto> result = queryVendorItemRestApiUtil.searchVendorItems(
            vendorDo.getId(), List.of(itemDo.getId())
        );

        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());
        Assertions.assertEquals(vendorDo.getId(), result.getFirst().getVendorId());
        Assertions.assertEquals(itemDo.getId(), result.getFirst().getItemId());
    }
}
