package com.mercaso.ims.interfaces;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.CreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.UpdateItemVendorRebateCommand;
import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.enums.ItemVendorRebateStatus;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import com.mercaso.ims.utils.itemvendorrebate.ItemVendorRebateCommandUtil;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class ItemVendorRebateRestApiIT extends AbstractIT {

    private static final Logger log = LoggerFactory.getLogger(ItemVendorRebateRestApiIT.class);

    @Test
    void shouldSuccessWhenCreateItemVendorRebate() throws Exception {
        // Arrange
        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorName = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        
        // Create vendor item first
        UUID vendorItemId = createVendorItem(itemDo.getId(), vendorDo.getId(), vendorItemNumber);

        CreateItemVendorRebateCommand command = ItemVendorRebateCommandUtil.buildCreateItemVendorRebateCommand(
                vendorItemId, vendorDo.getId(), itemDo.getId());

        // Act
        ItemVendorRebateDto result = itemVendorRebateRestApiUtil.createItemVendorRebateRequest(command);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(vendorItemId, result.getVendorItemId());
        assertEquals(vendorDo.getId(), result.getVendorId());
        assertEquals(itemDo.getId(), result.getItemId());
        assertEquals(command.getStartDate(), result.getStartDate());
        assertEquals(command.getEndDate(), result.getEndDate());
        assertEquals(0, command.getRebatePerUnit().compareTo(result.getRebatePerUnit()));
        assertEquals(ItemVendorRebateStatus.ACTIVE, result.getItemVendorRebateStatus());

        // Verify in database
        ItemVendorRebate savedRebate = itemVendorRebateRepository.findById(result.getId());
        assertNotNull(savedRebate);
        assertEquals(vendorItemId, savedRebate.getVendorItemId());

        log.info("Successfully created ItemVendorRebate with id: {}", result.getId());
    }

    @Test
    void shouldSuccessWhenUpdateItemVendorRebate() throws Exception {
        // Arrange - Create initial rebate
        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorName = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        UUID vendorItemId = createVendorItem(itemDo.getId(), vendorDo.getId(), vendorItemNumber);

        CreateItemVendorRebateCommand createCommand = ItemVendorRebateCommandUtil.buildCreateItemVendorRebateCommand(
                vendorItemId, vendorDo.getId(), itemDo.getId());
        ItemVendorRebateDto createdRebate = itemVendorRebateRestApiUtil.createItemVendorRebateRequest(createCommand);

        // Prepare update command
        UpdateItemVendorRebateCommand updateCommand = ItemVendorRebateCommandUtil.buildUpdateItemVendorRebateCommand(
                createdRebate.getId(), vendorItemId, vendorDo.getId(), itemDo.getId());

        // Act
        ItemVendorRebateDto result = itemVendorRebateRestApiUtil.updateItemVendorRebateRequest(updateCommand);

        // Assert
        assertNotNull(result);
        assertEquals(createdRebate.getId(), result.getId());
        assertEquals(vendorItemId, result.getVendorItemId());
        assertEquals(updateCommand.getStartDate(), result.getStartDate());
        assertEquals(updateCommand.getEndDate(), result.getEndDate());
        assertEquals(0, updateCommand.getRebatePerUnit().compareTo(result.getRebatePerUnit()));

        // Verify in database
        ItemVendorRebate updatedRebate = itemVendorRebateRepository.findById(result.getId());
        assertNotNull(updatedRebate);
        assertEquals(0, updateCommand.getRebatePerUnit().compareTo(updatedRebate.getRebatePerUnit()));

        log.info("Successfully updated ItemVendorRebate with id: {}", result.getId());
    }

    @Test
    void shouldSuccessWhenDeleteItemVendorRebate() throws Exception {
        // Arrange - Create rebate first
        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorName = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        UUID vendorItemId = createVendorItem(itemDo.getId(), vendorDo.getId(), vendorItemNumber);

        CreateItemVendorRebateCommand createCommand = ItemVendorRebateCommandUtil.buildCreateItemVendorRebateCommand(
                vendorItemId, vendorDo.getId(), itemDo.getId());
        ItemVendorRebateDto createdRebate = itemVendorRebateRestApiUtil.createItemVendorRebateRequest(createCommand);

        // Verify rebate exists
        ItemVendorRebate rebateBeforeDelete = itemVendorRebateRepository.findById(createdRebate.getId());
        assertNotNull(rebateBeforeDelete);

        // Act
        itemVendorRebateRestApiUtil.deleteItemVendorRebateRequest(createdRebate.getId());

        // Assert
        ItemVendorRebate rebateAfterDelete = itemVendorRebateRepository.findById(createdRebate.getId());
        assertNull(rebateAfterDelete);

        log.info("Successfully deleted ItemVendorRebate with id: {}", createdRebate.getId());
    }

    @Test
    void shouldSuccessWhenCreateItemVendorRebateWithContinuousEndDate() throws Exception {
        // Arrange - Create rebate with null end date (continuous)
        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorName = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        UUID vendorItemId = createVendorItem(itemDo.getId(), vendorDo.getId(), vendorItemNumber);

        CreateItemVendorRebateCommand command = ItemVendorRebateCommandUtil.buildCreateItemVendorRebateCommand(
                vendorItemId, vendorDo.getId(), itemDo.getId(), 
                LocalDate.now(), null, new BigDecimal("10.00"));

        // Act
        ItemVendorRebateDto result = itemVendorRebateRestApiUtil.createItemVendorRebateRequest(command);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(vendorItemId, result.getVendorItemId());
        assertNull(result.getEndDate()); // Should be null for continuous rebate
        assertEquals(0, new BigDecimal("10.00").compareTo(result.getRebatePerUnit()));

        log.info("Successfully created continuous ItemVendorRebate with id: {}", result.getId());
    }

    @Test
    void shouldSuccessWhenCreateItemVendorRebateWithSpecificDateRange() throws Exception {
        // Arrange - Create rebate with specific date range
        String sku = RandomStringUtils.randomAlphabetic(5);
        String vendorName = RandomStringUtils.randomAlphabetic(5);
        String vendorItemNumber = RandomStringUtils.randomAlphabetic(5);

        ItemDo itemDo = buildItemData(sku);
        VendorDo vendorDo = buildVendorDoData(vendorName);
        UUID vendorItemId = createVendorItem(itemDo.getId(), vendorDo.getId(), vendorItemNumber);

        LocalDate startDate = LocalDate.of(2024, 6, 1);
        LocalDate endDate = LocalDate.of(2024, 12, 31);
        BigDecimal rebateAmount = new BigDecimal("15.50");

        CreateItemVendorRebateCommand command = ItemVendorRebateCommandUtil.buildCreateItemVendorRebateCommand(
                vendorItemId, vendorDo.getId(), itemDo.getId(), startDate, endDate, rebateAmount);

        // Act
        ItemVendorRebateDto result = itemVendorRebateRestApiUtil.createItemVendorRebateRequest(command);

        // Assert
        assertNotNull(result);
        assertEquals(startDate, result.getStartDate());
        assertEquals(endDate, result.getEndDate());
        assertEquals(0, rebateAmount.compareTo(result.getRebatePerUnit()));

        log.info("Successfully created ItemVendorRebate with specific date range, id: {}", result.getId());
    }

    /**
     * Helper method to create a vendor item for testing
     */
    private UUID createVendorItem(UUID itemId, UUID vendorId, String vendorItemNumber) throws Exception {
        return vendorItemRestApiUtil.createVendorItemRequest(
                com.mercaso.ims.utils.vendor.VendorItemCommandUtil.buildCreateVendorItemCommand(
                        itemId, vendorId, vendorItemNumber)
        ).getVendorItemId();
    }
}
