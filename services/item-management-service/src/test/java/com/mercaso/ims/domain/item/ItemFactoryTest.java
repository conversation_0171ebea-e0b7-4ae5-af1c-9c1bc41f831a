package com.mercaso.ims.domain.item;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.utils.item.ItemCommandUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
class ItemFactoryTest {


    @Test
    void testCreateItem() {

        CreateItemCommand command = ItemCommandUtil.buildCreateItemCommand();

        Item item = ItemFactory.createItem(command);

        assertNotNull(item);
        assertEquals(command.getCategoryId(), item.getCategoryId());
        assertEquals(command.getBrandId(), item.getBrandId());
        assertEquals(command.getName(), item.getName());
        assertEquals(command.getDepartment(), item.getDepartment());
        assertEquals(command.getCategory(), item.getCategory());


    }

}