package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.ItemPriceGroupListDto;
import com.mercaso.ims.application.query.ItemPriceGroupQuery;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Component
public class SearchItemPriceGroupRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_ITEM_PRICE_GROUP_URL = "/v1/search/item-price-group";

    public SearchItemPriceGroupRestApiUtil(Environment environment) {
        super(environment);
    }

    public ItemPriceGroupListDto searchItemPriceGroup(Instant createdStartDate, Instant createdEndDate, String groupName, ItemPriceGroupQuery.SortType sortType)
        throws Exception {

        List<String> queryParams = new ArrayList<>();

        if (!ObjectUtils.isEmpty(createdStartDate)) {
            queryParams.add("createdAtBegin=" + createdStartDate);
        }
        if (!ObjectUtils.isEmpty(createdEndDate)) {
            queryParams.add("createdAtEnd=" + createdEndDate);
        }
        if (!StringUtils.isBlank(groupName)) {
            queryParams.add("groupName=" + groupName);
        }
        if (sortType != null) {
            queryParams.add("sort=" + sortType.name());
        }

        String queryString = queryParams.isEmpty() ? "" : "?" + String.join("&", queryParams);

        return getEntity(SEARCH_ITEM_PRICE_GROUP_URL + queryString, ItemPriceGroupListDto.class);

    }
}
