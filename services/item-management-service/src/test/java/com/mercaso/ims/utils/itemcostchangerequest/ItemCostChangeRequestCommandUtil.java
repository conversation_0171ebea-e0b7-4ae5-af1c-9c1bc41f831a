package com.mercaso.ims.utils.itemcostchangerequest;

import com.mercaso.ims.application.command.BatchUpdateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.CreateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.UpdateItemCostChangeRequestCommand;
import com.mercaso.ims.domain.itemcostchangerequest.enums.CostType;
import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

public class ItemCostChangeRequestCommandUtil {

    public static CreateItemCostChangeRequestCommand buildCreateItemCostChangeRequestCommand() {
        return buildCreateItemCostChangeRequestCommand(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID());
    }

    public static CreateItemCostChangeRequestCommand buildCreateItemCostChangeRequestCommand(UUID itemCostCollectionId,
        UUID vendorId,
        UUID itemId) {

        return new CreateItemCostChangeRequestCommand(
            itemCostCollectionId,
            vendorId,
            itemId,
            "skuNumber",
            "vendorSkuNumber",
            "vendorItemName",

            new BigDecimal(0),
            new BigDecimal(0),
            new BigDecimal(0),
            new BigDecimal(0),
            MatchedType.MISS_MATCHED,
            ItemCostChangeRequestStatus.PENDING, "upc", true, CostType.DIRECT_COST.getCostTypeName(), null);

    }

    public static UpdateItemCostChangeRequestCommand buildUpdateItemCostChangeRequestCommand(UUID id,
        BigDecimal previousCost,
        ItemCostChangeRequestStatus status) {

        return new UpdateItemCostChangeRequestCommand(
            id,
            previousCost,
            status);

    }


    public static BatchUpdateItemCostChangeRequestCommand buildBatchUpdateItemCostChangeRequestCommand(ItemCostChangeRequestStatus status) {
        UpdateItemCostChangeRequestCommand updateItemCostChangeRequestCommand = buildUpdateItemCostChangeRequestCommand(UUID.randomUUID(),
            new BigDecimal(10),
            status);
        return new BatchUpdateItemCostChangeRequestCommand(List.of(updateItemCostChangeRequestCommand));
    }

}
