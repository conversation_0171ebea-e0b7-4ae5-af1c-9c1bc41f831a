package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.command.CreateVendorItemCommand;
import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.dto.VendorItemAuditHistoryInfoDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class VendorItemRestApiUtil extends IntegrationTestRestUtil {

    private static final String VENDOR_ITEM_REST_API_URL = "/v1/vendor-item";

    public VendorItemRestApiUtil(Environment environment) {
        super(environment);
    }


    public void deleteVendorItemRequest(UUID id) {
        deleteEntity(VENDOR_ITEM_REST_API_URL + "/" + id);
    }

    public VendorItemDto updateVendorItemRequest(UUID id, UpdateVendorItemCommand command) throws Exception {
        return updateEntity(VENDOR_ITEM_REST_API_URL + "/" + id, command, VendorItemDto.class);
    }

    public VendorItemDto createVendorItemRequest(CreateVendorItemCommand command) throws Exception {
        return createEntity(VENDOR_ITEM_REST_API_URL, command, VendorItemDto.class);
    }

    public List<VendorItemAuditHistoryInfoDto> getVendorItemAuditHistories(UUID id) throws Exception {
        return getEntityList(VENDOR_ITEM_REST_API_URL+ "/" + id + "/audit-history", VendorItemAuditHistoryInfoDto.class);
    }

}
