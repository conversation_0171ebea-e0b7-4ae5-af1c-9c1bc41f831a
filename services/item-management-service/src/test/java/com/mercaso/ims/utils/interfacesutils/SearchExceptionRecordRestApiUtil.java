package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.ItemPriceExceptionRecordListDto;
import com.mercaso.ims.application.dto.VendorItemCostExceptionRecordListDto;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordStatus;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Component
public class SearchExceptionRecordRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_COST_EXCEPTION_REQUEST_URL = "/v1/search/exception-record/cost-exception";
    private static final String SEARCH_PRICE_EXCEPTION_REQUEST_URL = "/v1/search/exception-record/price-exception";

    public SearchExceptionRecordRestApiUtil(Environment environment) {
        super(environment);
    }

    public VendorItemCostExceptionRecordListDto searchCostExceptionRecord(
        ExceptionRecordStatus status,
        AvailabilityStatus itemStatus,
        String skuNumber,
        String itemTitle,
        UUID vendorId,
        Instant createdStartDate,
        Instant createdEndDate,
        String createdUserName) throws Exception {

        Map<String, Object> queryParams = buildQueryParams(status,
            itemStatus,
            skuNumber,
            itemTitle,
            vendorId,
            createdStartDate,
            createdEndDate,
            createdUserName);
        String queryString = buildQueryString(queryParams);

        return getEntity(SEARCH_COST_EXCEPTION_REQUEST_URL + queryString, VendorItemCostExceptionRecordListDto.class);
    }

    public ItemPriceExceptionRecordListDto searchPriceExceptionRecord(
        ExceptionRecordStatus status,
        AvailabilityStatus itemStatus,
        String skuNumber,
        String itemTitle,
        Instant createdStartDate,
        Instant createdEndDate,
        String createdUserName) throws Exception {

        Map<String, Object> queryParams = buildQueryParams(status,
            itemStatus,
            skuNumber,
            itemTitle,
            null,
            createdStartDate,
            createdEndDate,
            createdUserName);
        String queryString = buildQueryString(queryParams);

        return getEntity(SEARCH_PRICE_EXCEPTION_REQUEST_URL + queryString, ItemPriceExceptionRecordListDto.class);
    }

    private Map<String, Object> buildQueryParams(
        ExceptionRecordStatus status,
        AvailabilityStatus itemStatus,
        String skuNumber,
        String itemTitle,
        UUID vendorId,
        Instant createdStartDate,
        Instant createdEndDate,
        String createdUserName) {

        Map<String, Object> queryParams = new HashMap<>();

        if (!ObjectUtils.isEmpty(createdStartDate)) {
            queryParams.put("createdStartDate", createdStartDate);
        }
        if (!ObjectUtils.isEmpty(createdEndDate)) {
            queryParams.put("createdEndDate", createdEndDate);
        }
        if (!ObjectUtils.isEmpty(createdUserName)) {
            queryParams.put("createdUserName", createdUserName);
        }
        if (!ObjectUtils.isEmpty(skuNumber)) {
            queryParams.put("skuNumber", skuNumber);
        }
        if (!ObjectUtils.isEmpty(status)) {
            queryParams.put("status", status);
        }
        if (!ObjectUtils.isEmpty(itemStatus)) {
            queryParams.put("itemStatus", itemStatus);
        }
        if (!ObjectUtils.isEmpty(itemTitle)) {
            queryParams.put("itemTitle", itemTitle);
        }
        if (!ObjectUtils.isEmpty(vendorId)) {
            queryParams.put("vendorId", vendorId);
        }

        return queryParams;
    }

    private String buildQueryString(Map<String, Object> queryParams) {
        if (queryParams.isEmpty()) {
            return "";
        }
        return "?" + queryParams.entrySet().stream()
            .map(entry -> entry.getKey() + "=" + entry.getValue())
            .collect(Collectors.joining("&"));
    }

}
