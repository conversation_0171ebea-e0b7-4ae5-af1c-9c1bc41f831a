package com.mercaso.ims.domain.itemregprice;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.config.ImsAlertConfig;
import com.mercaso.ims.infrastructure.external.slack.SlackHook;
import com.mercaso.ims.utils.item.ItemUtil;
import com.mercaso.ims.utils.itemregprice.ItemRegPriceUtil;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ItemRegPriceSpecificationTest {

    private final BigDecimal maximumPrice = BigDecimal.valueOf(500);
    private final BigDecimal minimumPrice = BigDecimal.valueOf(5);
    private final BigDecimal maximumMargin = BigDecimal.valueOf(0.25);
    private final BigDecimal minimumMargin = BigDecimal.valueOf(0.03);


    private ImsAlertConfig imsAlertConfig = Mockito.mock(ImsAlertConfig.class);
    private ItemRepository itemRepository = Mockito.mock(ItemRepository.class);


    private VendorItemRepository vendorItemRepository = Mockito.mock(VendorItemRepository.class);
    private SlackHook slackHook = Mockito.mock(SlackHook.class);


    private ItemRegPriceSpecification specification = new ItemRegPriceSpecification(imsAlertConfig, itemRepository,
        vendorItemRepository, slackHook);

    @Test
    void testIsSatisfiedBy_ReasonablePriceAndMargin() {
        UUID itemId = UUID.randomUUID();

        ItemRegPrice price = ItemRegPriceUtil.buildItemRegPrice(itemId);
        price.setRegPrice(new BigDecimal("100"));

        Item item = ItemUtil.buildItem();
        when(itemRepository.findById(itemId)).thenReturn(item);

        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        vendorItem.setPackPlusCrvCost(new BigDecimal("80"));
        when(vendorItemRepository.findByVendorIDAndItemId(any(), any())).thenReturn(vendorItem);
        when(imsAlertConfig.getMaximumMargin()).thenReturn(maximumMargin);
        when(imsAlertConfig.getMinimumMargin()).thenReturn(minimumMargin);
        when(imsAlertConfig.getMaximumPrice()).thenReturn(maximumPrice);
        when(imsAlertConfig.getMinimumPrice()).thenReturn(minimumPrice);

        boolean result = specification.isSatisfiedBy(price);

        assertTrue(result);
        verify(vendorItemRepository, times(2)).findByVendorIDAndItemId(any(), any());
        verify(slackHook, times(0)).sendSlackMessage(any(), any(), any(), any(), any());

    }


    @Test
    void testIsSatisfiedBy_UnreasonablePrice() {
        UUID itemId = UUID.randomUUID();
        ItemRegPrice price = ItemRegPriceUtil.buildItemRegPrice(itemId);
        price.setRegPrice(new BigDecimal("10000"));

        Item item = ItemUtil.buildItem();
        when(itemRepository.findById(itemId)).thenReturn(item);
        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        vendorItem.setPackPlusCrvCost(new BigDecimal("80"));
        when(vendorItemRepository.findByVendorIDAndItemId(any(), any())).thenReturn(vendorItem);
        when(imsAlertConfig.getMaximumMargin()).thenReturn(maximumMargin);
        when(imsAlertConfig.getMinimumMargin()).thenReturn(minimumMargin);
        when(imsAlertConfig.getMaximumPrice()).thenReturn(maximumPrice);
        when(imsAlertConfig.getMinimumPrice()).thenReturn(minimumPrice);
        boolean result = specification.isReasonablePrice(price);

        assertTrue(result);
        verify(slackHook, times(1)).sendSlackMessage(any(), any(), any(), any(), any());

    }

    @Test
    void testIsSatisfiedBy_belowMiniMargin() {
        UUID itemId = UUID.randomUUID();
        ItemRegPrice price = ItemRegPriceUtil.buildItemRegPrice(itemId);
        price.setRegPrice(new BigDecimal("24.99"));

        Item item = ItemUtil.buildItem();
        when(itemRepository.findById(itemId)).thenReturn(item);

        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        vendorItem.setPackPlusCrvCost(new BigDecimal("100"));
        when(vendorItemRepository.findByVendorIDAndItemId(any(), any())).thenReturn(vendorItem);
        when(imsAlertConfig.getMaximumMargin()).thenReturn(maximumMargin);
        when(imsAlertConfig.getMinimumMargin()).thenReturn(minimumMargin);
        when(imsAlertConfig.getMaximumPrice()).thenReturn(maximumPrice);
        when(imsAlertConfig.getMinimumPrice()).thenReturn(minimumPrice);

        boolean result = specification.isReasonableMargin(price);

        assertTrue(result);
        verify(vendorItemRepository, times(1)).findByVendorIDAndItemId(any(), any());
        verify(slackHook, times(1)).sendSlackMessage(any(), any(), any(), any(), any());

    }

    @Test
    void testIsSatisfiedBy_aboveMaxMargin() {
        UUID itemId = UUID.randomUUID();
        ItemRegPrice price = ItemRegPriceUtil.buildItemRegPrice(itemId);
        price.setRegPrice(new BigDecimal("100"));

        Item item = ItemUtil.buildItem();
        when(itemRepository.findById(itemId)).thenReturn(item);

        VendorItem vendorItem = VendorItemUtil.buildVendorItem();
        vendorItem.setPackPlusCrvCost(new BigDecimal("1"));
        when(vendorItemRepository.findByVendorIDAndItemId(any(), any())).thenReturn(vendorItem);
        when(imsAlertConfig.getMaximumMargin()).thenReturn(maximumMargin);
        when(imsAlertConfig.getMinimumMargin()).thenReturn(minimumMargin);
        when(imsAlertConfig.getMaximumPrice()).thenReturn(maximumPrice);
        when(imsAlertConfig.getMinimumPrice()).thenReturn(minimumPrice);

        boolean result = specification.isReasonableMargin(price);

        assertTrue(result);
        verify(vendorItemRepository, times(1)).findByVendorIDAndItemId(any(), any());
        verify(slackHook, times(1)).sendSlackMessage(any(), any(), any(), any(), any());

    }
}
