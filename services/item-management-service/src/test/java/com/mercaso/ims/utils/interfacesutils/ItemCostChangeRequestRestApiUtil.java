package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.command.BatchUpdateItemCostChangeRequestCommand;
import com.mercaso.ims.application.command.BatchUpdateItemTargetCostChangeRequestCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemCostChangeRequestResultDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class ItemCostChangeRequestRestApiUtil extends IntegrationTestRestUtil {

    private static final String APPROVED_VENDOR_PO_INVOICE_REST_API_URL = "/v1/item-cost-change-request/batch/approve";

    private static final String REJECTED_VENDOR_PO_INVOICE_REST_API_URL = "/v1/item-cost-change-request/batch/reject";

    private static final String BATCH_CHANGE_TARGET_COST_REQUEST_REST_API_URL = "/v1/item-cost-change-request/batch/change-target-cost";


    public ItemCostChangeRequestRestApiUtil(Environment environment) {
        super(environment);
    }


    public BatchUpdateItemCostChangeRequestResultDto approvedVendorPoInvoiceItem(BatchUpdateItemCostChangeRequestCommand command)
        throws Exception {
        return updateEntity(APPROVED_VENDOR_PO_INVOICE_REST_API_URL,
            command,
            BatchUpdateItemCostChangeRequestResultDto.class);
    }

    public BatchUpdateItemCostChangeRequestResultDto rejectedVendorPoInvoiceItem(BatchUpdateItemCostChangeRequestCommand command)
        throws Exception {
        return updateEntity(REJECTED_VENDOR_PO_INVOICE_REST_API_URL,
            command,
            BatchUpdateItemCostChangeRequestResultDto.class);
    }

    public BatchUpdateItemCostChangeRequestResultDto batchChangeTargetCostRequest(BatchUpdateItemTargetCostChangeRequestCommand command)
        throws Exception {
        return updateEntity(BATCH_CHANGE_TARGET_COST_REQUEST_REST_API_URL,
            command,
            BatchUpdateItemCostChangeRequestResultDto.class);
    }

}