package com.mercaso.ims.utils.brand;

import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.enums.BrandStatus;
import java.time.Instant;
import java.util.UUID;

public class BrandUtil {

    public static Brand buildBrand(UUID id) {
        return Brand.builder()
            .id(id)
            .name("brand")
            .logo("logo")
            .description("description")
            .status(BrandStatus.ACTIVE)
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .createdBy("SYSTEM")
            .updatedBy("SYSTEM")
            .build();
    }
}
