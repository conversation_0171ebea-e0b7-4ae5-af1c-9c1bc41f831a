package com.mercaso.ims.utils.interfacesutils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.ims.application.command.UpdateItemRegPriceCommand;
import com.mercaso.ims.application.dto.BatchUpdateItemPriceResultDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class ItemPriceRestApiUtil extends IntegrationTestRestUtil {

    private static final String ITEM_PRICE_REST_API_URL = "/v1/item-price";

    public ItemPriceRestApiUtil(Environment environment) {
        super(environment);
    }


    public BatchUpdateItemPriceResultDto batchUpdateItemRegPrice(List<UpdateItemRegPriceCommand> commands)
        throws JsonProcessingException {
        return updateEntity(ITEM_PRICE_REST_API_URL + "/batch-update-reg-price", commands, BatchUpdateItemPriceResultDto.class);
    }


}
