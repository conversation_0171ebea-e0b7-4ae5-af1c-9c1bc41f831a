package com.mercaso.ims.utils.attribute;

import com.mercaso.ims.domain.attribute.Attribute;
import com.mercaso.ims.domain.attribute.enums.AttributeStatus;
import java.util.UUID;

public class AttributeUtil {

    public static Attribute buildAttribute(UUID id) {
        return Attribute.builder()
            .id(id)
            .name("name")
            .categoryId(UUID.randomUUID())
            .description("description")
            .status(AttributeStatus.ACTIVE)
            .build();
    }


}
