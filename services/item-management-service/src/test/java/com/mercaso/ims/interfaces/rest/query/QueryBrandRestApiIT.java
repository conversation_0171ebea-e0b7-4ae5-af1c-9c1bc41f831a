package com.mercaso.ims.interfaces.rest.query;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.BrandDto;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class QueryBrandRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenSearchVendors() throws Exception {

        List<BrandDto> brandDtos = queryBrandRestApiUtil.searchBrands("");
        Assertions.assertNotNull(brandDtos);

    }
}
