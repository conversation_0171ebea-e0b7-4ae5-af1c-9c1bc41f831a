package com.mercaso.ims.utils.itemsalestrend;

import com.mercaso.ims.application.dto.ItemSalesTrendDto;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrend;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;

import java.util.Date;
import java.util.UUID;

/**
 * Utility class for creating test data for ItemSalesTrend related tests
 */
public class ItemSalesTrendTestUtil {

    public static ItemSalesTrend buildItemSalesTrend(UUID itemId, ItemSalesTrendTimeGrain timeGrain, Integer salesQuantity) {
        return ItemSalesTrend.builder()
            .id(UUID.randomUUID())
            .itemId(itemId.toString())
            .skuNumber("TEST-SKU-" + UUID.randomUUID().toString().substring(0, 8))
            .salesQuantity(salesQuantity)
            .timeDim(new Date())
            .timeGrain(timeGrain)
            .build();
    }

    public static ItemSalesTrend buildItemSalesTrend(String skuNumber, ItemSalesTrendTimeGrain timeGrain) {
        return ItemSalesTrend.builder()
            .id(UUID.randomUUID())
            .itemId(UUID.randomUUID().toString())
            .skuNumber(skuNumber)
            .salesQuantity(100)
            .timeDim(new Date())
            .timeGrain(timeGrain)
            .build();
    }

    public static ItemSalesTrendDto buildItemSalesTrendDto(UUID itemId, ItemSalesTrendTimeGrain timeGrain, Integer salesQuantity) {
        return ItemSalesTrendDto.builder()
            .id(UUID.randomUUID())
            .itemId(itemId.toString())
            .skuNumber("TEST-SKU-" + UUID.randomUUID().toString().substring(0, 8))
            .salesQuantity(salesQuantity)
            .timeDim(new Date())
            .timeGrain(timeGrain)
            .build();
    }

    public static ItemSalesTrendDto buildItemSalesTrendDto(String skuNumber, ItemSalesTrendTimeGrain timeGrain) {
        return ItemSalesTrendDto.builder()
            .id(UUID.randomUUID())
            .itemId(UUID.randomUUID().toString())
            .skuNumber(skuNumber)
            .salesQuantity(100)
            .timeDim(new Date())
            .timeGrain(timeGrain)
            .build();
    }

    public static ItemSalesTrend buildItemSalesTrendWithAllFields() {
        return ItemSalesTrend.builder()
            .id(UUID.randomUUID())
            .itemId(UUID.randomUUID().toString())
            .skuNumber("FULL-TEST-SKU-001")
            .salesQuantity(250)
            .timeDim(new Date())
            .timeGrain(ItemSalesTrendTimeGrain.DAY)
            .build();
    }

    public static ItemSalesTrendDto buildItemSalesTrendDtoWithAllFields() {
        return ItemSalesTrendDto.builder()
            .id(UUID.randomUUID())
            .itemId(UUID.randomUUID().toString())
            .skuNumber("FULL-TEST-SKU-DTO-001")
            .salesQuantity(300)
            .timeDim(new Date())
            .timeGrain(ItemSalesTrendTimeGrain.WEEK)
            .build();
    }

    public static ItemSalesTrend buildItemSalesTrendForIntegrationTest(UUID itemId, ItemSalesTrendTimeGrain timeGrain, Integer salesQuantity, String skuNumber) {
        return ItemSalesTrend.builder()
            .id(null) // For new entities in integration tests
            .itemId(itemId.toString())
            .skuNumber(skuNumber)
            .salesQuantity(salesQuantity)
            .timeDim(new Date())
            .timeGrain(timeGrain)
            .build();
    }

    public static ItemSalesTrend buildItemSalesTrendWithSpecificDate(UUID itemId, ItemSalesTrendTimeGrain timeGrain, Integer salesQuantity, Date timeDim) {
        return ItemSalesTrend.builder()
            .id(UUID.randomUUID())
            .itemId(itemId.toString())
            .skuNumber("TEST-SKU-" + UUID.randomUUID().toString().substring(0, 8))
            .salesQuantity(salesQuantity)
            .timeDim(timeDim)
            .timeGrain(timeGrain)
            .build();
    }
}
