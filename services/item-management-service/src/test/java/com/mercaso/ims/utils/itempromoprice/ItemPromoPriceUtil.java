package com.mercaso.ims.utils.itempromoprice;

import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.domain.itempromoprice.enums.ItemPromoPriceStatus;
import java.math.BigDecimal;
import java.util.UUID;

public class ItemPromoPriceUtil {


    public static ItemPromoPrice buildItemPromoPrice(UUID itemId) {
        return ItemPromoPrice.builder()
            .itemId(itemId)
            .promoPrice(BigDecimal.TEN)
            .itemPromoPriceStatus(ItemPromoPriceStatus.ACTIVE)
            .build();
    }


}
