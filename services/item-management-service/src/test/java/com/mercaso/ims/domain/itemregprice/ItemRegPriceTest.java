package com.mercaso.ims.domain.itemregprice;

import com.mercaso.ims.application.dto.PriceDto;
import com.mercaso.ims.utils.itemregprice.ItemRegPriceUtil;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ItemRegPriceTest {


    @Test
    void testUpdatePrice() {
        ItemRegPrice itemRegPrice = ItemRegPriceUtil.buildItemRegPrice(UUID.randomUUID());

        ItemRegPrice result = itemRegPrice.updatePrice(new PriceDto(new BigDecimal(1),
            1000,
            Boolean.FALSE,
            1.1f,
            "oz"));

        Assertions.assertEquals(0, BigDecimal.valueOf(0.01).compareTo(result.getRegPriceIndividual()));
        Assertions.assertEquals(0, BigDecimal.valueOf(1).compareTo(result.getRegPrice()));

    }

}