package com.mercaso.ims.utils.vendor;

import com.mercaso.ims.application.command.CreateVendorItemCommand;
import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import java.util.UUID;

public class VendorItemCommandUtil {


    public static CreateVendorItemCommand buildCreateVendorItemCommand(UUID itemId, UUID vendorId, String vendorSkuNumber) {
        return CreateVendorItemCommand.builder()
            .vendorSkuNumber(vendorSkuNumber)
            .vendorId(vendorId)
            .vendorItemName("vendorItemName")
            .note("note")
            .aisle("aisle")
            .itemId(itemId)
            .build();
    }

    public static UpdateVendorItemCommand buildUpdateVendorItemCommand(UUID id, String vendorItemName, String vendorSkuNumber) {
        return UpdateVendorItemCommand.builder()
            .vendorItemId(id)
            .vendorSkuNumber(vendorSkuNumber)
            .vendorItemName(vendorItemName)
            .note("note")
            .aisle("aisle")
            .build();
    }

}
