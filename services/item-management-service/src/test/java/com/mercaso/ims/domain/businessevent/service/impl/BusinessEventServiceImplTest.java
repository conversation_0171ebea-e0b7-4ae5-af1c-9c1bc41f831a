package com.mercaso.ims.domain.businessevent.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractTest;
import com.mercaso.ims.domain.businessevent.BusinessEvent;
import com.mercaso.ims.domain.businessevent.BusinessEventRepository;
import com.mercaso.ims.infrastructure.event.BusinessEventDispatcher;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {BusinessEventServiceImpl.class})
class BusinessEventServiceImplTest extends AbstractTest {

    @MockBean
    private BusinessEventDispatcher businessEventDispatcher;

    @MockBean
    private BusinessEventRepository businessEventRepository;

    @Autowired
    private BusinessEventServiceImpl businessEventServiceImpl;

    @Test
    void testFindByIdInAndOrderByCreatedAt() {
        // Arrange
        ArrayList<BusinessEvent> businessEventList = new ArrayList<>();
        when(businessEventRepository.findByIdInAndOrderByCreatedAt(Mockito.<List<UUID>>any()))
            .thenReturn(businessEventList);
        ArrayList<UUID> businessEventIds = new ArrayList<>();

        // Act
        List<BusinessEvent> actualFindByIdInAndOrderByCreatedAtResult = businessEventServiceImpl
            .findByIdInAndOrderByCreatedAt(businessEventIds);

        // Assert
        verify(businessEventRepository).findByIdInAndOrderByCreatedAt(isA(List.class));
        assertTrue(actualFindByIdInAndOrderByCreatedAtResult.isEmpty());
        assertEquals(actualFindByIdInAndOrderByCreatedAtResult, businessEventIds);
        assertSame(businessEventList, actualFindByIdInAndOrderByCreatedAtResult);
    }

    @Test
    void testFindByIdInAndOrderByCreatedAt2() {
        // Arrange
        ArrayList<BusinessEvent> businessEventList = new ArrayList<>();
        when(businessEventRepository.findByIdInAndOrderByCreatedAt(Mockito.<List<UUID>>any()))
            .thenReturn(businessEventList);

        ArrayList<UUID> businessEventIds = new ArrayList<>();
        businessEventIds.add(UUID.randomUUID());

        // Act
        List<BusinessEvent> actualFindByIdInAndOrderByCreatedAtResult = businessEventServiceImpl
            .findByIdInAndOrderByCreatedAt(businessEventIds);

        // Assert
        verify(businessEventRepository).findByIdInAndOrderByCreatedAt(isA(List.class));
        assertTrue(actualFindByIdInAndOrderByCreatedAtResult.isEmpty());
        assertSame(businessEventList, actualFindByIdInAndOrderByCreatedAtResult);
    }

    @Test
    void testFindByIdInAndOrderByCreatedAt3() {
        // Arrange
        ArrayList<BusinessEvent> businessEventList = new ArrayList<>();
        when(businessEventRepository.findByIdInAndOrderByCreatedAt(Mockito.<List<UUID>>any()))
            .thenReturn(businessEventList);

        ArrayList<UUID> businessEventIds = new ArrayList<>();
        businessEventIds.add(UUID.randomUUID());
        businessEventIds.add(UUID.randomUUID());

        // Act
        List<BusinessEvent> actualFindByIdInAndOrderByCreatedAtResult = businessEventServiceImpl
            .findByIdInAndOrderByCreatedAt(businessEventIds);

        // Assert
        verify(businessEventRepository).findByIdInAndOrderByCreatedAt(isA(List.class));
        assertTrue(actualFindByIdInAndOrderByCreatedAtResult.isEmpty());
        assertSame(businessEventList, actualFindByIdInAndOrderByCreatedAtResult);
    }
}
