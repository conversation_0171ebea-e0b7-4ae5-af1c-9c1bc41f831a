package com.mercaso.ims.utils.category;

import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import com.mercaso.ims.infrastructure.repository.category.jpa.dataobject.CategoryDo;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.UUID;

public class CategoryUtil {

    public static Category buildCategory(String name) {
        return Category.builder()
            .id(UUID.randomUUID())
            .name(name)
            .icon("")
            .status(CategoryStatus.ACTIVE)
            .createdBy("System")
            .description("category")
            .build();
    }

    public static CategoryDo buildCategoryDo() {
        CategoryDo categoryDo = new CategoryDo();
        categoryDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        categoryDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        categoryDo.setCreatedUserName("janedoe");
        categoryDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        categoryDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        categoryDo.setDeletedUserName("janedoe");
        categoryDo.setDescription("The characteristics of someone or something");
        categoryDo.setIcon("Icon");
        categoryDo.setId(UUID.randomUUID());
        categoryDo.setName("Name");
        categoryDo.setStatus(CategoryStatus.DRAFT);
        categoryDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        categoryDo.setUpdatedBy("2020-03-01");
        categoryDo.setUpdatedUserName("janedoe");
        return categoryDo;
    }

    public static CategoryDto buildCategoryDto() {
        return CategoryDto.builder()
            .categoryId(UUID.randomUUID())
            .categoryName("Name")
            .ancestorCategoryId(UUID.randomUUID())
            .depth(1)
            .build();
    }
}
