package com.mercaso.ims.interfaces.rest.query;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.AttributeDto;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class QueryAttributeRestApiIT extends AbstractIT {

    @Test
    void shouldSuccessWhenSearchAttributes() throws Exception {

        List<AttributeDto> attributeDtos = queryAttributeRestApiUtil.searchAttributes("");
        Assertions.assertNotNull(attributeDtos);

    }
}
