package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.ItemAuditHistoryInfoDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class ItemAuditHistoryV2RestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_ITEM_AUDIT_HISTORY_V2_REQUEST_URL = "/v2/item-audit-history/";

    public ItemAuditHistoryV2RestApiUtil(Environment environment) {
        super(environment);
    }

    public List<ItemAuditHistoryInfoDto> itemAuditHistoryListRequest(UUID itemId) throws Exception {
        return getEntityList(SEARCH_ITEM_AUDIT_HISTORY_V2_REQUEST_URL + itemId, ItemAuditHistoryInfoDto.class);
    }

}
