package com.mercaso.ims.domain.item;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemTag.class})
class ItemTagTest {


    @Test
    void testSameValueAs() {

        // Arrange
        ItemTag itemTag = new ItemTag(mock(ItemTag.ItemTagBuilder.class));

        // Act and Assert
        assertTrue(itemTag.sameValueAs(new ItemTag(mock(ItemTag.ItemTagBuilder.class))));
    }


    @Test
    void testSameValueAs2() {

        // Arrange, Act and Assert
        assertFalse((new ItemTag(mock(ItemTag.ItemTagBuilder.class))).sameValueAs(null));
    }


    @Test
    void testSameValueAs3() {

        // Arrange
        ItemTag itemTag = new ItemTag(mock(ItemTag.ItemTagBuilder.class));
        itemTag.setItemId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemTag.sameValueAs(new ItemTag(mock(ItemTag.ItemTagBuilder.class))));
    }


    @Test
    void testSameValueAs4() {

        // Arrange
        ItemTag itemTag = new ItemTag(mock(ItemTag.ItemTagBuilder.class));

        ItemTag other = new ItemTag(mock(ItemTag.ItemTagBuilder.class));
        other.setItemId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemTag.sameValueAs(other));
    }


    @Test
    void testSameValueAs5() {

        // Arrange
        ItemTag itemTag = new ItemTag(mock(ItemTag.ItemTagBuilder.class));
        itemTag.setItemId(UUID.randomUUID());

        ItemTag other = new ItemTag(mock(ItemTag.ItemTagBuilder.class));
        other.setItemId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemTag.sameValueAs(other));
    }
}
