package com.mercaso.ims.utils.address;

import com.mercaso.ims.domain.address.Address;
import com.mercaso.ims.domain.address.enums.AddressPurpose;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.UUID;

public class AddressTestUtil {

    /**
     * Build an Address for testing purposes
     */
    public static Address buildAddress() {
        return buildAddress("Vendor", UUID.randomUUID(), AddressPurpose.BUSINESS);
    }

    /**
     * Build an Address with specific entity type and entity ID
     */
    public static Address buildAddress(String entityType, UUID entityId) {
        return buildAddress(entityType, entityId, AddressPurpose.BUSINESS);
    }

    /**
     * Build an Address with specific entity type, entity ID and purpose
     */
    public static Address buildAddress(String entityType, UUID entityId, AddressPurpose purpose) {
        return Address.builder()
            .entityType(entityType)
            .entityId(entityId)
            .streetAddress("123 " + RandomStringUtils.randomAlphabetic(5) + " St")
            .city("New York")
            .state("NY")
            .postalCode("10001")
            .country("USA")
            .directions("Near Central Park")
            .purpose(purpose)
            .additionalLines("Suite 100")
            .build();
    }

    /**
     * Build an Address with specific street address
     */
    public static Address buildAddress(String streetAddress) {
        return Address.builder()
            .entityType("Vendor")
            .entityId(UUID.randomUUID())
            .streetAddress(streetAddress)
            .city("New York")
            .state("NY")
            .postalCode("10001")
            .country("USA")
            .directions("Near Central Park")
            .purpose(AddressPurpose.BUSINESS)
            .additionalLines("Suite 100")
            .build();
    }

    /**
     * Build an Address with specific entity type and purpose
     */
    public static Address buildAddress(String entityType, AddressPurpose purpose) {
        return buildAddress(entityType, UUID.randomUUID(), purpose);
    }

}
