package com.mercaso.ims.utils.company;

import com.mercaso.ims.infrastructure.repository.company.jpa.dataobject.CompanyDo;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.UUID;

public class CompanyUtil {

    public static CompanyDo buildCompanyDo() {
        CompanyDo companyDo = new CompanyDo();
        companyDo.setCompanyId(1L);
        companyDo.setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        companyDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        companyDo.setCreatedUserName("janedoe");
        companyDo.setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        companyDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        companyDo.setDeletedUserName("janedoe");
        companyDo.setId(UUID.randomUUID());
        companyDo.setName("Name");
        companyDo.setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        companyDo.setUpdatedBy("2020-03-01");
        companyDo.setUpdatedUserName("janedoe");
        return companyDo;
    }
}
