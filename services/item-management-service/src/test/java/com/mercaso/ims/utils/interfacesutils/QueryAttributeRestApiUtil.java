package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.dto.AttributeDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class QueryAttributeRestApiUtil extends IntegrationTestRestUtil {

    private static final String SEARCH_ATTRIBUTES_URL = "/v1/query/attribute";

    public QueryAttributeRestApiUtil(Environment environment) {
        super(environment);
    }

    public List<AttributeDto> searchAttributes (String attributeName) throws Exception {
        String url = String.format(SEARCH_ATTRIBUTES_URL + "?attributeName=%s", attributeName);
        return getEntityList(url, AttributeDto.class);
    }
}
