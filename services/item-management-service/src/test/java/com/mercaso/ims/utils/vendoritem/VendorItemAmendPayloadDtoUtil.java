package com.mercaso.ims.utils.vendoritem;

import com.mercaso.ims.application.dto.payload.VendorItemAmendPayloadDto;
import com.mercaso.ims.utils.vendor.VendorItemDtoUtil;
import java.util.UUID;

public class VendorItemAmendPayloadDtoUtil {

    public static VendorItemAmendPayloadDto buildVendorItemAmendPayloadDto(UUID vendorItemId) {
        return VendorItemAmendPayloadDto.builder()
            .vendorItemId(vendorItemId)
            .previous(VendorItemDtoUtil.buildVendorItemDto())
            .current(VendorItemDtoUtil.buildVendorItemDto())
            .build();
    }
}
