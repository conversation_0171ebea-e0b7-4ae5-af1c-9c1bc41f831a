package com.mercaso.ims.utils.itemadjustmentrequestdetail;

import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail.jpa.dataobject.ItemAdjustmentRequestDetailDo;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.UUID;

public class ItemAdjustmentRequestDetailUtil {

    public static ItemAdjustmentRequestDetail buildItemAdjustmentRequestDetail(UUID itemAdjustmentRequestId) {
        return ItemAdjustmentRequestDetail.builder()
            .requestId(itemAdjustmentRequestId)
            .type(ItemAdjustmentType.CREATE)
            .status(ItemAdjustmentStatus.PENDING)
            .id(UUID.randomUUID())
            .sku("sku")
            .build();
    }

    public static ItemAdjustmentRequestDetail buildUpdateItemAdjustmentRequestDetail(UUID itemAdjustmentRequestId) {
        return ItemAdjustmentRequestDetail.builder()
            .requestId(itemAdjustmentRequestId)
            .type(ItemAdjustmentType.UPDATE)
            .status(ItemAdjustmentStatus.PENDING)
            .sku("sku")
            .regPricePackNoCrv(BigDecimal.TEN)
            .build();
    }

    public static ItemAdjustmentRequestDetail buildRemoveUpcAdjustmentRequestDetail(UUID itemAdjustmentRequestId) {
        return ItemAdjustmentRequestDetail.builder()
            .requestId(itemAdjustmentRequestId)
            .type(ItemAdjustmentType.CLEAN_UPC)
            .status(ItemAdjustmentStatus.PENDING)
            .sku("sku")
            .build();
    }

    public static ItemAdjustmentRequestDetailDo buildItemAdjustmentRequestDetailDo() {
        ItemAdjustmentRequestDetailDo itemAdjustmentRequestDetailDo = new ItemAdjustmentRequestDetailDo();
        itemAdjustmentRequestDetailDo.setAttributeName("Attribute Name");
        itemAdjustmentRequestDetailDo.setAttributeValue("42");
        itemAdjustmentRequestDetailDo.setBrand("Brand");
        itemAdjustmentRequestDetailDo.setCategory("Category");
        itemAdjustmentRequestDetailDo.setClassType("Class Type");
        itemAdjustmentRequestDetailDo.setCompanyId("42");
        itemAdjustmentRequestDetailDo
            .setCreatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemAdjustmentRequestDetailDo.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        itemAdjustmentRequestDetailDo.setCreatedUserName("janedoe");
        itemAdjustmentRequestDetailDo.setCrvFlag(true);
        itemAdjustmentRequestDetailDo
            .setDeletedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemAdjustmentRequestDetailDo.setDeletedBy("Jan 1, 2020 11:00am GMT+0100");
        itemAdjustmentRequestDetailDo.setDeletedUserName("janedoe");
        itemAdjustmentRequestDetailDo.setDepartment("Department");
        itemAdjustmentRequestDetailDo.setDisposition("Disposition");
        itemAdjustmentRequestDetailDo.setFailureReason("Just cause");
        itemAdjustmentRequestDetailDo.setId(UUID.randomUUID());
        itemAdjustmentRequestDetailDo.setImageUrl("https://example.org/example");
        itemAdjustmentRequestDetailDo.setInventory(1);
        itemAdjustmentRequestDetailDo.setItemSize("Item Size");
        itemAdjustmentRequestDetailDo.setItemStatus("Item Status");
        itemAdjustmentRequestDetailDo.setItemUnitMeasure("Item Unit Measure");
        itemAdjustmentRequestDetailDo.setLocationId("42");
        itemAdjustmentRequestDetailDo.setNewDescription("New Description");
        itemAdjustmentRequestDetailDo.setPackageSize(3);
        itemAdjustmentRequestDetailDo.setPrimaryPoVendor("Primary Vendor");
        itemAdjustmentRequestDetailDo.setPrimaryVendorItemAisle("Primary Vendor Item Aisle");
        itemAdjustmentRequestDetailDo.setPrimaryPoVendorItemCost(new BigDecimal("2.3"));
        itemAdjustmentRequestDetailDo.setPromoFlag(true);
        itemAdjustmentRequestDetailDo.setPromoPricePackNoCrv(new BigDecimal("2.3"));
        itemAdjustmentRequestDetailDo.setRegPricePackNoCrv(new BigDecimal("2.3"));
        itemAdjustmentRequestDetailDo.setRequestId(UUID.randomUUID());
        itemAdjustmentRequestDetailDo.setSku("Sku");
        itemAdjustmentRequestDetailDo.setStatus(ItemAdjustmentStatus.VALIDATION_FAILURE);
        itemAdjustmentRequestDetailDo.setSubCategory("Sub Category");
        itemAdjustmentRequestDetailDo.setTags("Tags");
        itemAdjustmentRequestDetailDo.setTitle("Dr");
        itemAdjustmentRequestDetailDo.setType(ItemAdjustmentType.CREATE);
        itemAdjustmentRequestDetailDo.setUpc("Upc");
        itemAdjustmentRequestDetailDo
            .setUpdatedAt(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC).toInstant());
        itemAdjustmentRequestDetailDo.setUpdatedBy("2020-03-01");
        itemAdjustmentRequestDetailDo.setUpdatedUserName("janedoe");
        itemAdjustmentRequestDetailDo.setVendor("Vendor");
        itemAdjustmentRequestDetailDo.setVendorAisle("Vendor Aisle");
        itemAdjustmentRequestDetailDo.setPoVendorItemCost(new BigDecimal("2.3"));
        itemAdjustmentRequestDetailDo.setVendorItemNumber("42");
        return itemAdjustmentRequestDetailDo;
    }
}
