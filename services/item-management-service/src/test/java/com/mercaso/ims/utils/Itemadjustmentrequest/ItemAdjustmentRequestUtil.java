package com.mercaso.ims.utils.Itemadjustmentrequest;

import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequest;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import java.util.UUID;

public class ItemAdjustmentRequestUtil {

    public static ItemAdjustmentRequest buildItemAdjustmentRequest(UUID id) {
        return ItemAdjustmentRequest.builder()
            .id(id)
            .requestFile("docName")
            .type(ItemAdjustmentRequestType.NEW_TEMPLATE_ADJUSTMENT)
            .status(ItemAdjustmentRequestStatus.UPLOADED)
            .build();
    }
}
