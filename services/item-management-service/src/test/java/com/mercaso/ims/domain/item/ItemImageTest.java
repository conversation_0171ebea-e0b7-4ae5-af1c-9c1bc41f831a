package com.mercaso.ims.domain.item;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(classes = {ItemImage.class})
class ItemImageTest {


    @Test
    void testSameValueAs() {

        // Arrange
        ItemImage itemImage = new ItemImage(mock(ItemImage.ItemImageBuilder.class));

        // Act and Assert
        assertTrue(itemImage.sameValueAs(new ItemImage(mock(ItemImage.ItemImageBuilder.class))));
    }


    @Test
    void testSameValueAs2() {

        // Arrange, Act and Assert
        assertFalse((new ItemImage(mock(ItemImage.ItemImageBuilder.class))).sameValueAs(null));
    }


    @Test
    void testSameValueAs3() {

        // Arrange
        ItemImage itemImage = new ItemImage(mock(ItemImage.ItemImageBuilder.class));
        itemImage.setItemId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemImage.sameValueAs(new ItemImage(mock(ItemImage.ItemImageBuilder.class))));
    }


    @Test
    void testSameValueAs4() {

        // Arrange
        ItemImage itemImage = new ItemImage(mock(ItemImage.ItemImageBuilder.class));

        ItemImage other = new ItemImage(mock(ItemImage.ItemImageBuilder.class));
        other.setItemId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemImage.sameValueAs(other));
    }


    @Test
    void testSameValueAs5() {

        // Arrange
        ItemImage itemImage = new ItemImage(mock(ItemImage.ItemImageBuilder.class));
        itemImage.setItemId(UUID.randomUUID());

        ItemImage other = new ItemImage(mock(ItemImage.ItemImageBuilder.class));
        other.setItemId(UUID.randomUUID());

        // Act and Assert
        assertFalse(itemImage.sameValueAs(other));
    }
}
