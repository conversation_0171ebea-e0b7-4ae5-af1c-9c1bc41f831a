package com.mercaso.ims.utils.interfacesutils;

import com.mercaso.ims.application.command.CreateCategoryCommand;
import com.mercaso.ims.application.command.UpdateCategoryCommand;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.CategoryTreeDto;
import com.mercaso.ims.utils.IntegrationTestRestUtil;
import java.util.List;
import java.util.UUID;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class CategoryV2RestApiUtil extends IntegrationTestRestUtil {

    private static final String CATEGORIES_REQUEST_URL = "/v2/categories";

    public CategoryV2RestApiUtil(Environment environment) {
        super(environment);
    }


    public List<CategoryTreeDto> getAllCategories() throws Exception {
        return getEntityList(CATEGORIES_REQUEST_URL, CategoryTreeDto.class);
    }

    public CategoryDto createCategory(CreateCategoryCommand command) throws Exception {
        return postEntity(CATEGORIES_REQUEST_URL, command, CategoryDto.class);
    }

    public void updateCategory(UUID categoryId, UpdateCategoryCommand command) throws Exception {
        updateEntity(CATEGORIES_REQUEST_URL + "/" + categoryId, command, Void.class);
    }

    public void deleteCategory(UUID categoryId) {
        deleteEntity(CATEGORIES_REQUEST_URL + "/" + categoryId);
    }
}
