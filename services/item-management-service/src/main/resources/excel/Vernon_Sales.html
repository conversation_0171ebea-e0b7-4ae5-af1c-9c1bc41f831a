
<!-- saved from url=(0075)http://www.vernonsales.com/ais101/CsSaStatusDetail.php?status=I&trno=924867 -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Vernon Sales - Your one stop supplier of food items                                                 </title>
<meta name="Keywords" content="vernon sales, 99 cents items, one dollar item, ">
<meta name="Description" content="Vernon Sales is wholesale distributor for 99 cent stores.  Locatd in Vernon, CA.,
">

<script language="JavaScript">
function changeImages() {
  if (document.images) {
    for (var i=0; i<changeImages.arguments.length; i+=2) {
      document[changeImages.arguments[i]].src = changeImages.arguments[i+1];
    }
  }
}
function NewWindow2(url) {
  window.open(url,'Jav','width=1012,height=690,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,location=no,directories=no,status=no');
}
</script>
<link rel="stylesheet" type="text/css" href="./<PERSON> Sales - Your one stop supplier of food items_files/style.css">
</head>

<body>
<div class="body">
  <center><table><tbody><tr><td>
  <div id="header1">
    <table>
      <tbody><tr><td>
        <a onmouseover="changeImages(&#39;t11&#39;, &#39;/images/Ais101/T11b.jpg&#39;); return true;" onmouseout="changeImages(&#39;t11&#39;, &#39;/images/Ais101/T11b.jpg&#39;); return true;"><img border="0" name="t11" src="./Vernon Sales - Your one stop supplier of food items_files/T11b.jpg"></a>      </td>
      <td>
        <form id="login" method="post" action="http://www.vernonsales.com/ais101/WebPass.php">
          Welcome,<br>MERCASO, INC<br><input class="loginsubmit" type="submit" name="action" value="Logout">        </form>
      </td>
    </tr></tbody></table>
  </div>
  <div id="header2">
    <a href="http://www.vernonsales.com/ais101/index.php" onmouseover="changeImages(&#39;t21&#39;, &#39;/images/Ais101/T21b.jpg&#39;); return true;" onmouseout="changeImages(&#39;t21&#39;, &#39;/images/Ais101/T21a.jpg&#39;); return true;"><img border="0" name="t21" src="./Vernon Sales - Your one stop supplier of food items_files/T21a.jpg"></a><a href="http://www.vernonsales.com/ais101/Search.php" onmouseover="changeImages(&#39;t22&#39;, &#39;/images/Ais101/T22b.jpg&#39;); return true;" onmouseout="changeImages(&#39;t22&#39;, &#39;/images/Ais101/T22a.jpg&#39;); return true;"><img border="0" name="t22" src="./Vernon Sales - Your one stop supplier of food items_files/T22a.jpg"></a><a href="http://www.vernonsales.com/ais101/CustServ.php" onmouseover="changeImages(&#39;t23&#39;, &#39;/images/Ais101/T23b.jpg&#39;); return true;" onmouseout="changeImages(&#39;t23&#39;, &#39;/images/Ais101/T23a.jpg&#39;); return true;"><img border="0" name="t23" src="./Vernon Sales - Your one stop supplier of food items_files/T23a.jpg"></a><a href="http://www.vernonsales.com/ais101/ShCart.php" onmouseover="changeImages(&#39;t24&#39;, &#39;/images/Ais101/T24b.jpg&#39;); return true;" onmouseout="changeImages(&#39;t24&#39;, &#39;/images/Ais101/T24a.jpg&#39;); return true;"><img border="0" name="t24" src="./Vernon Sales - Your one stop supplier of food items_files/T24a.jpg"></a><a href="http://www.vernonsales.com/ais101/Link1.php" onmouseover="changeImages(&#39;t25&#39;, &#39;/images/Ais101/T25a.jpg&#39;); return true;" onmouseout="changeImages(&#39;t25&#39;, &#39;/images/Ais101/T25a.jpg&#39;); return true;"><img border="0" name="t25" src="./Vernon Sales - Your one stop supplier of food items_files/T25a.jpg"></a><a href="http://www.vernonsales.com/ais101/Link2.php" onmouseover="changeImages(&#39;t26&#39;, &#39;/images/Ais101/T26a.jpg&#39;); return true;" onmouseout="changeImages(&#39;t26&#39;, &#39;/images/Ais101/T26a.jpg&#39;); return true;"><img border="0" name="t26" src="./Vernon Sales - Your one stop supplier of food items_files/T26a.jpg"></a>  </div>
  <div id="header3">
    <a onmouseover="changeImages(&#39;t31&#39;, &#39;/images/Ais101/T31b.jpg&#39;); return true;" onmouseout="changeImages(&#39;t31&#39;, &#39;/images/Ais101/T31a.jpg&#39;); return true;"><img border="0" name="t31" src="./Vernon Sales - Your one stop supplier of food items_files/T31a.jpg"></a>  </div>
  <div class="header4">
<table class="CsSaStatusDetail" border="1" cellpadding="1" cellspacing="1"><tbody><tr><td class="header1" colspan="9">Order No.: 924867</td></tr><tr></tr><tr><th class="lnno">Ln.</th><th class="itemno">Item No.</th><th class="desc1">Description</th><th class="ordqty">Ord. Qty</th><th class="shiqty">Ship Qty</th><th class="trum">UM</th><th class="qtum">#/UM</th><th class="price">U. Price</th><th class="lnamt">Ext. Amount</th></tr><tr><td align="right">1</td><td>14064</td><td>Tide Detergent 500g + 50g Jasmine &amp; Rose</td><td align="right">1</td><td align="right">1</td><td align="center">PK</td><td align="right">48</td><td align="right">1.09</td><td align="right">52.32</td></tr>
<tr><td align="right">2</td><td>75616</td><td>Tide Detergent 350g W-Downy</td><td align="right">19</td><td align="right">19</td><td align="center">PK</td><td align="right">36</td><td align="right">1.15</td><td align="right">786.60</td></tr>
<tr><td align="right">3</td><td>74322</td><td>Special Value All Purpose Cleaner 32oz</td><td align="right">1</td><td align="right">1</td><td align="center">PK</td><td align="right">12</td><td align="right">0.99</td><td align="right">11.88</td></tr>
</tbody></table><p><a href="http://www.vernonsales.com/ais101/CsSaStatus.php"></a></p><h3><a href="http://www.vernonsales.com/ais101/CsSaStatus.php">&nbsp; &nbsp; Back to Order Status page</a></h3><p></p><br><br><br><br><br><br>  </div>
  </td></tr></tbody></table></center>
  <div class="copyright">
        <p><br><a href="http://www.softrela.com/" target="_blank"><img border="0" src="./Vernon Sales - Your one stop supplier of food items_files/softrela.png"></a>
    </p>
  </div>
</div>


</body></html>