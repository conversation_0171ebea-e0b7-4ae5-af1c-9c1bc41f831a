<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!--    <turboFilter class="ch.qos.logback.classic.turbo.DuplicateMessageFilter">-->
    <!--        <AllowedRepetitions>0</AllowedRepetitions>-->
    <!--    </turboFilter>-->

    <turboFilter class="ch.qos.logback.classic.turbo.DynamicThresholdFilter">
        <Key>loggerName</Key>
        <DefaultThreshold>ALL</DefaultThreshold>
        <MDCKey>loggerName</MDCKey>
        <OnMatch>ACCEPT</OnMatch>
        <OnMismatch>NEUTRAL</OnMismatch>
    </turboFilter>

    <logger name="org.hibernate.engine.jdbc.spi.SqlExceptionHelper" level="ERROR" additivity="false">
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.JaninoEventEvaluator">
                <expression>
                    return message.contains("duplicate key value violates unique constraint
                    \"item_version_item_id_version_number_idx\"");
                </expression>
            </evaluator>
            <OnMatch>DENY</OnMatch>
            <OnMismatch>ACCEPT</OnMismatch>
        </filter>
    </logger>

    <appender class="ch.qos.logback.core.ConsoleAppender" name="STDOUT">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>${log.pattern}</Pattern>
            </layout>
        </encoder>
    </appender>
    <!--  Since skywalking cannot identify the color code  -->
    <appender class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender" name="SKYWALKING">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>${skywalking.log.pattern}</Pattern>
            </layout>
        </encoder>
    </appender>

    <root level="ERROR">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="SKYWALKING"/>
    </root>

    <logger level="INFO" name="com.mercaso"/>

    <logger level="ERROR" name="org.apache.kafka.clients.NetworkClient"/>

    <logger level="WARN" name="org.springframework.vault.core.lease"/>

    <property name="log.pattern"
        value="%red(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{10}) - [%tid] - [%X{user-id}] %cyan(%msg%n)"/>

    <property name="skywalking.log.pattern"
        value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{10} - [%tid] - [%X{user-id}] %msg%n"/>


    <springProfile name="local">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>

    <springProfile name="integration">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>

    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="SKYWALKING"/>
        </root>
    </springProfile>

    <springProfile name="sat">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="SKYWALKING"/>
        </root>
    </springProfile>
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="SKYWALKING"/>
        </root>
    </springProfile>
</configuration>