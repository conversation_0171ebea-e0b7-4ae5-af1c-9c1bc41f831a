
alter table item_adjustment_request_detail
    add vendor varchar(64);

alter table item_adjustment_request_detail
    add vendor_aisle varchar(64);

alter table item_adjustment_request_detail
    add company_id varchar(64);

alter table item_adjustment_request_detail
    add location_id varchar(64);

alter table item_adjustment_request_detail
    add id_tag varchar(225);

alter table item_adjustment_request_detail
    add new_description varchar(225);

alter table item_adjustment_request_detail
    add attribute_name varchar(225);
alter table item_adjustment_request_detail
    add attribute_value varchar(225);

alter table item_adjustment_request_detail
    add item_unit_measure varchar(64);
alter table item_adjustment_request_detail
    add promo_flag  BOOLEAN;

alter table item_adjustment_request_detail
    add crv_flag  BOOLEAN;

alter table item_adjustment_request_detail
    add promo_price_pack_no_crv  DECIMAL(10, 2);
alter table item_adjustment_request_detail
    add image_url  varchar(225);

alter table item_adjustment_request_detail
    rename column jc_web_price to vendor_item_cost;
alter table item_adjustment_request_detail
    rename column jc_sales_price to vendor_secondary_cost;
alter table item_adjustment_request_detail
    rename column downey_cost to primary_vendor_item_cost;
alter table item_adjustment_request_detail
    rename column aisle to primary_vendor_item_aisle;

alter table item_adjustment_request_detail
    rename column bottle_size to item_size;