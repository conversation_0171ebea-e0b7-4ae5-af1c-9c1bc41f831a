-- Create email table
CREATE TABLE email (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type VARCHAR(100) NOT NULL,
    entity_id UUID NOT NULL,
    email_type VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL,
    extension VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    created_user_name VARCHAR(100),
    updated_at TIMESTAMP,
    updated_by VARCHAR(100),
    updated_user_name VARCHAR(100),
    deleted_at TIMESTAMP,
    deleted_by VA<PERSON><PERSON><PERSON>(100),
    deleted_user_name VARCHAR(100)
);

-- Add comments to table
COMMENT ON TABLE email IS 'Email information for various entities';

-- Add comments to columns
COMMENT ON COLUMN email.entity_type IS 'Entity type (e.g., Vendor, Customer, etc.)';
COMMENT ON COLUMN email.entity_id IS 'Entity ID reference';
COMMENT ON COLUMN email.email_type IS 'Email type (<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>PORT, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)';
COMMENT ON COLUMN email.email IS 'Email address';
COMMENT ON COLUMN email.extension IS 'Email extension or additional identifier';
COMMENT ON COLUMN email.created_at IS 'Creation timestamp';
COMMENT ON COLUMN email.created_by IS 'User who created the record';
COMMENT ON COLUMN email.created_user_name IS 'Name of user who created the record';
COMMENT ON COLUMN email.updated_at IS 'Last update timestamp';
COMMENT ON COLUMN email.updated_by IS 'User who last updated the record';
COMMENT ON COLUMN email.updated_user_name IS 'Name of user who last updated the record';
COMMENT ON COLUMN email.deleted_at IS 'Soft delete timestamp';
COMMENT ON COLUMN email.deleted_by IS 'User who deleted the record';
COMMENT ON COLUMN email.deleted_user_name IS 'Name of user who deleted the record';

-- Create indexes for better query performance
CREATE INDEX idx_email_entity_type_entity_id ON email(entity_type, entity_id);
CREATE INDEX idx_email_email_type ON email(email_type);
CREATE INDEX idx_email_email ON email(email);
CREATE INDEX idx_email_deleted_at ON email(deleted_at);

-- Add constraints
ALTER TABLE email ADD CONSTRAINT chk_email_length CHECK (LENGTH(email) >= 5 AND LENGTH(email) <= 255);
ALTER TABLE email ADD CONSTRAINT chk_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');
