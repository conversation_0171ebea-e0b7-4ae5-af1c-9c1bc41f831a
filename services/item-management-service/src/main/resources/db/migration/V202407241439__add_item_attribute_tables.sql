CREATE TABLE item_attribute
(
    id             UUID         NOT NULL PRIMARY KEY,
    item_id        UUID,
    attribute_id   UUID         NOT NULL,
    value          TEXT,
    unit           VARCHAR(32),
    attribute_type VARCHAR(255),
    sort           INTEGER,
    created_at     TIMESTAMP    NOT NULL,
    created_by     <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at     TIMESTAMP,
    updated_by     VARCHAR(255),
    deleted_at     TIMESTAMP,
    deleted_by     VARCHAR(255)
);

create index item_attribute_item_id_idx on item_attribute (item_id);
create index item_attribute_attribute_id_idx on item_attribute (attribute_id);
