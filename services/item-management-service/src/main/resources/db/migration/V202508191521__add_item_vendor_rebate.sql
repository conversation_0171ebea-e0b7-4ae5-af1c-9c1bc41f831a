-- V1__create_item_vendor_rebate_table.sql
CREATE TABLE item_vendor_rebate (
                                    id UUID PRIMARY KEY,
                                    vendor_item_id UUID NOT NULL,
                                    vendor_id UUID NOT NULL,
                                    item_id UUID NOT NULL,
                                    start_date DATE NOT NULL,
                                    end_date DATE,
                                    rebate_per_unit DECIMAL(10, 2) NOT NULL,
                                    status VARCHAR(64),
                                    created_at         TIMESTAMP        NOT NULL DEFAULT NOW(),
                                    created_by         <PERSON><PERSON><PERSON><PERSON>(255),
                                    updated_at         TIMESTAMP        NOT NULL DEFAULT NOW(),
                                    updated_by         VARCHAR(255),
                                    deleted_at         TIMESTAMP,
                                    deleted_by         VARC<PERSON>R(255),
                                    created_user_name varchar(64),
                                    updated_user_name varchar(64),
                                    deleted_user_name varchar(64),
                                    CONSTRAINT uq_item_vendor_rebate UNIQUE (vendor_item_id, start_date, deleted_at)
);

CREATE INDEX idx_item_vendor_rebate_vendor ON item_vendor_rebate(vendor_id);
CREATE INDEX idx_item_vendor_rebate_item ON item_vendor_rebate(item_id);
CREATE INDEX idx_item_vendor_rebate_dates ON item_vendor_rebate(start_date, end_date);