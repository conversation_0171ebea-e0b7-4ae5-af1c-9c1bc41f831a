CREATE TABLE item
(
    id                  UUID         NOT NULL PRIMARY KEY,
    category_id         UUID         NOT NULL,
    brand_id            UUID,
    name                VA<PERSON>HA<PERSON>(255) NOT NULL,
    title               VARCHAR(255) NOT NULL,
    sku_number          VARCHAR(255) NOT NULL,
    description         TEXT,
    note                TEXT,
    photo               VARCHAR(255) NOT NULL,
    primary_vendor_id   UUID,
    detail              TEXT,
    package_type        VARCHAR(255),
    package_size        INT,
    shelf_life          VARCHAR(255),
    item_type           VA<PERSON>HAR(255),
    sales_status        VARCHAR(255),
    availability_status VARCHAR(255),
    handle              VARCHAR(255),
    company_id          BIGINT,
    location_id         BIGINT,
    created_at          TIMESTAMP    NOT NULL,
    created_by          <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at          TIMESTAMP,
    updated_by          VA<PERSON><PERSON>R(255),
    deleted_at          TIMESTAMP,
    deleted_by          <PERSON><PERSON><PERSON><PERSON>(255)
);

create index item_category_id_idx on item (category_id);
create index item_brand_id_idx on item (brand_id);
create unique index item_sku_number_idx on item (sku_number);
