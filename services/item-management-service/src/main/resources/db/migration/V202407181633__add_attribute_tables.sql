CREATE TABLE attribute
(
    id               UUID         NOT NULL PRIMARY KEY,
    category_id      UUID         NOT NULL,
    name             <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    description      VARCHAR(255) NOT NULL,
    attribute_format VARCHAR(50)  NOT NULL,
    status           VARCHAR(50)  NOT NULL,
    created_at       TIMESTAMP    NOT NULL,
    created_by       <PERSON><PERSON><PERSON><PERSON>(50)  NOT NULL,
    updated_at       TIMESTAMP,
    updated_by       VARCHAR(50),
    deleted_at       TIMESTAMP,
    deleted_by       VARCHAR(50)
);
create index attribute_category_id_idx on attribute (category_id);

CREATE TABLE attribute_enum_value
(
    id           UUID         NOT NULL PRIMARY KEY,
    attribute_id UUID         NOT NULL,
    value        VARCHAR(100) NOT NULL,
    sort_order   FLOAT        NOT NULL,
    created_at   TIMESTAMP    NOT NULL,
    created_by   <PERSON><PERSON><PERSON><PERSON>(50)  NOT NULL,
    updated_at   TIMESTAMP,
    updated_by   <PERSON><PERSON><PERSON><PERSON>(50),
    deleted_at   TIMESTAMP,
    deleted_by   VARC<PERSON>R(50)
);
create index attribute_enum_value_attribute_id_idx on attribute_enum_value (attribute_id);
