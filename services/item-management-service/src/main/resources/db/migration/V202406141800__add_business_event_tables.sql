CREATE TABLE business_event
(
    id             UUID         NOT NULL PRIMARY KEY,
    entity_id      UUID         NOT NULL,
    type           VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    payload        JSONB,
    correlation_id VARCHAR(50),
    created_at     TIMESTAMP    NOT NULL,
    created_by     <PERSON><PERSON><PERSON><PERSON>(50)  NOT NULL,
    updated_at     TIMESTAMP,
    updated_by     VARCHAR(50),
    deleted_at     TIMESTAMP,
    deleted_by     VARCHAR(50)
);

CREATE INDEX business_event_entity_id_idx ON business_event (entity_id);
CREATE INDEX business_event_type_idx ON business_event (type);
CREATE INDEX business_event_correlation_id_idx ON business_event (correlation_id);
