CREATE TABLE item_promo_price
(
    id                       UUID         NOT NULL PRIMARY KEY,
    item_id                  UUID,
    crv                      DECIMAL,
    promo_price              DECIMAL,
    promo_price_individual   DECIMAL,
    promo_price_plus_crv     DECIMAL,
    promo_begin_time         TIMESTAMP,
    promo_end_time           TIMESTAMP,
    promo_flag               BOOLEAN,
    promo_live_check         VARCHAR(255),
    promo_pricing_validation TEXT,
    status                   VARCHAR(255),
    created_at               TIMESTAMP    NOT NULL,
    created_by               VARCHAR(255) NOT NULL,
    updated_at               TIMESTAMP,
    updated_by               VARCHAR(255),
    deleted_at               TIMESTAMP,
    deleted_by               VARC<PERSON>R(255)
);

create index item_promo_price_item_id_idx on item_promo_price (item_id);