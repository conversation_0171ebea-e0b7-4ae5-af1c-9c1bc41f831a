-- Create phone_number table
CREATE TABLE phone_number (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type VARCHAR(100) NOT NULL,
    entity_id UUID NOT NULL,
    phone_type VARCHAR(20) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    extension VARCHAR(10),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    created_user_name VARCHAR(100),
    updated_at TIMESTAMP,
    updated_by VARCHAR(100),
    updated_user_name VARCHAR(100),
    deleted_at TIMESTAMP,
    deleted_by VARCHAR(100),
    deleted_user_name VARCHAR(100)
);

-- Add comments to table
COMMENT ON TABLE phone_number IS 'Phone number information for various entities';

-- Add comments to columns
COMMENT ON COLUMN phone_number.entity_type IS 'Entity type (e.g., Vendor, Customer, etc.)';
COMMENT ON COLUMN phone_number.entity_id IS 'Entity ID reference';
COMMENT ON COLUMN phone_number.phone_type IS 'Phone type (<PERSON><PERSON><PERSON><PERSON>, HOME, WORK, FAX, OTHER)';
COMMENT ON COLUMN phone_number.phone_number IS 'Phone number';
COMMENT ON COLUMN phone_number.extension IS 'Phone extension';
COMMENT ON COLUMN phone_number.created_at IS 'Creation timestamp';
COMMENT ON COLUMN phone_number.created_by IS 'User who created the record';
COMMENT ON COLUMN phone_number.created_user_name IS 'Name of user who created the record';
COMMENT ON COLUMN phone_number.updated_at IS 'Last update timestamp';
COMMENT ON COLUMN phone_number.updated_by IS 'User who last updated the record';
COMMENT ON COLUMN phone_number.updated_user_name IS 'Name of user who last updated the record';
COMMENT ON COLUMN phone_number.deleted_at IS 'Soft delete timestamp';
COMMENT ON COLUMN phone_number.deleted_by IS 'User who deleted the record';
COMMENT ON COLUMN phone_number.deleted_user_name IS 'Name of user who deleted the record';

-- Create indexes for better query performance
CREATE INDEX idx_phone_number_entity_type_entity_id ON phone_number(entity_type, entity_id);
CREATE INDEX idx_phone_number_phone_type ON phone_number(phone_type);
CREATE INDEX idx_phone_number_phone_number ON phone_number(phone_number);
CREATE INDEX idx_phone_number_deleted_at ON phone_number(deleted_at);

-- Add constraints
ALTER TABLE phone_number ADD CONSTRAINT chk_phone_number_length CHECK (LENGTH(phone_number) >= 7 AND LENGTH(phone_number) <= 20);