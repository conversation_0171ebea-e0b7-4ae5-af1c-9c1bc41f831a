CREATE TABLE category_hierarchy
(
    id                   UUID        NOT NULL PRIMARY KEY,
    category_id          UUID        NOT NULL,
    ancestor_category_id UUID        NOT NULL,
    depth                INT         NOT NULL,
    sort_order           INT,
    created_at           TIMESTAMP   NOT NULL,
    created_by           <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    updated_at           TIMESTAMP,
    updated_by           <PERSON><PERSON><PERSON><PERSON>(50),
    deleted_at           TIMESTAMP,
    deleted_by           VARC<PERSON>R(50),
    created_user_name    varchar(64),
    updated_user_name    varchar(64),
    deleted_user_name    varchar(64)
);

CREATE UNIQUE INDEX idx_category_id_ancestor_category_id_unique ON category_hierarchy(category_id, ancestor_category_id);

COMMENT ON COLUMN category_hierarchy.category_id IS 'The ID of the current category, the foreign key points to the category_id of the category table..';
COMMENT ON COLUMN category_hierarchy.ancestor_category_id IS 'Ancestor category ID, foreign key points to category_id of the category table.';
COMMENT ON COLUMN category_hierarchy.depth IS 'The hierarchical depth between the current category and the ancestor category (starting from 0, 0 represents itself, 1 represents the direct parent category, 2 represents the grandparent category, and so on).';


alter table category
    drop column parent_category_id;

alter table category
    drop column sort_order;