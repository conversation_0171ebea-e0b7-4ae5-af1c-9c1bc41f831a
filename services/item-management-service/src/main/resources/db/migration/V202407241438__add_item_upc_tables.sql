CREATE TABLE item_upc
(
    id         UUID         NOT NULL PRIMARY KEY,
    item_id    UUID,
    upc_number VA<PERSON>HAR(255) NOT NULL,
    upc_type VARCHAR(64) NOT NULL,
    created_at TIMESTAMP    NOT NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at TIMESTAMP,
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_at TIMESTAMP,
    deleted_by VA<PERSON><PERSON><PERSON>(255)
);


create index item_upc_item_id_idx on item_upc (item_id);