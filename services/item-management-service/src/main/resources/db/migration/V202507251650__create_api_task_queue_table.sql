CREATE TABLE api_task_queue (
    id                  UUID            NOT NULL PRIMARY KEY,
    task_type          VARCHAR(100)     NOT NULL,
    api_endpoint       VARCHAR(500)     NOT NULL,
    http_method        VARCHAR(10)      NOT NULL,
    request_payload    JSONB,
    response_payload   JSONB,
    status             VARCHAR(50)      NOT NULL DEFAULT 'PENDING',
    priority           INTEGER          NOT NULL DEFAULT 0,
    max_retry_count    INTEGER          NOT NULL DEFAULT 3,
    current_retry_count INTEGER         NOT NULL DEFAULT 0,
    scheduled_at       TIMESTAMP,
    started_at         TIMESTAMP,
    completed_at       TIMESTAMP,
    error_message      TEXT,
    created_at         TIMESTAMP        NOT NULL DEFAULT NOW(),
    created_by         <PERSON><PERSON><PERSON><PERSON>(255),
    updated_at         TIMESTAMP        NOT NULL DEFAULT NOW(),
    updated_by         <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_at         TIMESTAMP,
    deleted_by         <PERSON><PERSON><PERSON><PERSON>(255),
    created_user_name varchar(64),
    updated_user_name varchar(64),
    deleted_user_name varchar(64)
);

CREATE INDEX idx_api_task_queue_status ON api_task_queue (status);
CREATE INDEX idx_api_task_queue_task_type ON api_task_queue (task_type);
CREATE INDEX idx_api_task_queue_scheduled_at ON api_task_queue (scheduled_at);
CREATE INDEX idx_api_task_queue_priority_created_at ON api_task_queue (priority DESC, created_at ASC);
CREATE INDEX idx_api_task_queue_status_task_type ON api_task_queue (status, task_type);

ALTER TABLE api_task_queue ADD CONSTRAINT chk_api_task_queue_status
    CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'RETRY'));

ALTER TABLE api_task_queue ADD CONSTRAINT chk_api_task_queue_http_method 
    CHECK (http_method IN ('GET', 'POST', 'PUT', 'DELETE', 'PATCH'));

ALTER TABLE api_task_queue ADD CONSTRAINT chk_api_task_queue_retry_count 
    CHECK (current_retry_count >= 0 AND current_retry_count <= max_retry_count);

COMMENT ON TABLE api_task_queue IS 'General API task queue list, used to implement API call rate limiting and asynchronous processing';
COMMENT ON COLUMN api_task_queue.task_type IS 'Task type, used to distinguish different API call types and apply different rate limit strategies';
COMMENT ON COLUMN api_task_queue.api_endpoint IS 'API endpoint URL or identifier';
COMMENT ON COLUMN api_task_queue.request_payload IS 'Request parameters, stored in JSON format';
COMMENT ON COLUMN api_task_queue.response_payload IS 'Response result, stored in JSON format';
COMMENT ON COLUMN api_task_queue.priority IS 'Task priority, the higher the number, the higher the priority; tasks with the same priority are sorted by creation time.';
COMMENT ON COLUMN api_task_queue.scheduled_at IS 'Schedule execution time, used for delaying execution or retrying scheduling';
