CREATE TABLE dify_workflow_record
(
    id                UUID         NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    item_id           UUID  NOT NULL,
    sku_number        VARCHAR(100) NOT NULL,
    total_tokens      BIGINT,
    total_steps       INTEGER,
    elapsed_time      double precision,
    workflow_id       UUID,
    result            TEXT,
    status            VARCHAR(64),
    created_at        TIMESTAMP    NOT NULL,
    created_by        VARC<PERSON>R(50)  NOT NULL,
    updated_at        TIMESTAMP,
    updated_by        VARC<PERSON>R(50),
    deleted_at        TIMESTAMP,
    deleted_by        VA<PERSON><PERSON><PERSON>(50),
    created_user_name varchar(64),
    updated_user_name varchar(64),
    deleted_user_name varchar(64)
);

CREATE INDEX idx_dify_workflow_record_item_id_index ON dify_workflow_record (item_id);
CREATE INDEX idx_dify_workflow_record_sku_number_index ON dify_workflow_record (sku_number);
CREATE INDEX idx_dify_workflow_record_workflow_id_index ON dify_workflow_record (workflow_id);

COMMENT ON TABLE dify_workflow_record IS 'Dify Workflow Execution Record';

COMMENT ON COLUMN dify_workflow_record.total_tokens IS 'Workflow spent token';
COMMENT ON COLUMN dify_workflow_record.total_steps IS 'Workflow number of Execution Nodes';
COMMENT ON COLUMN dify_workflow_record.elapsed_time IS 'The time spent executing';
COMMENT ON COLUMN dify_workflow_record.workflow_id IS 'Workflow ID';
COMMENT ON COLUMN dify_workflow_record.result IS 'The returned result';