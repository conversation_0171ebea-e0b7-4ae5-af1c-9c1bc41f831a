CREATE TABLE location
(
    id           UUID         NOT NULL PRIMARY KEY,
    location_id  BIGINT,
    company_uuid UUID,
    name         <PERSON><PERSON><PERSON><PERSON>(255),
    created_at   TIMESTAMP    NOT NULL,
    created_by   VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at   TIMESTAMP,
    updated_by   VA<PERSON><PERSON><PERSON>(255),
    deleted_at   TIMESTAMP,
    deleted_by   VARCHAR(255)
);


create index location_company_uuid_idx on location (company_uuid);