comment on column item_promo_price.item_id is 'Product ID of the product promo price';

comment on column item_promo_price.crv is 'California Recycling Fee (CRV), an additional fee that applies to certain items.';

comment on column item_promo_price.promo_price is 'Promotional price: the discounted price of a product during an event.';

comment on column item_promo_price.promo_price_individual is 'Promotional price (single product), applies to the promotional price of a single product';

comment on column item_promo_price.promo_price_plus_crv is 'The promotional price is added with the California Recycling Rate (CRV) to show the final selling price.';

comment on column item_promo_price.promo_begin_time is 'The start time of the promotion, in timestamp format.';

comment on column item_promo_price.promo_end_time is 'The end time of the promotion, in timestamp format.';

comment on column item_promo_price.promo_flag is 'Promotion flag, indicating whether the product is on promotion. Possible values include true to enable, false to disable, etc.';

comment on column item_promo_price.promo_live_check is 'Promotion validity check flag, used to verify whether the promotion is running within the validity period.';

comment on column item_promo_price.promo_pricing_validation is 'Promotional price validation rules, used to verify whether the promotional price complies with business logic.';

comment on column item_promo_price.status is 'The Status field indicates the current status of the promotion record.';

comment on column item_promo_price.crv_flag is 'The CRV flag of the product, used to indicate whether the product has CRV.';