CREATE TABLE item_tag
(
    id         UUID         NOT NULL PRIMARY KEY,
    item_id    UUID,
    tag_name   <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP    NOT NULL,
    created_by VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at TIMESTAMP,
    updated_by VA<PERSON><PERSON>R(255),
    deleted_at TIMESTAMP,
    deleted_by VARCHAR(255)
);


create index item_tag_item_id_idx on item_tag (item_id);