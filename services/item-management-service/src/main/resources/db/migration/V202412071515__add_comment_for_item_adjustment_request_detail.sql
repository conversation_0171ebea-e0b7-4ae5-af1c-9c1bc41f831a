comment on column item_adjustment_request_detail.request_id is 'item adjustment request id';

comment on column item_adjustment_request_detail.type is 'item adjustment request type (eg: CREATE, UPDATE, DELETE, CLEAN_UPC, CREATE_VENDOR,)';

comment on column item_adjustment_request_detail.status is 'item adjustment request status (eg: VALIDATION_FAILURE, PENDING, IMS_UPDATED, IMS_UPDATED_FAILURE, PLYTIX_SYNCHRONIZED, PLYTIX_SYNCHRONIZED_FAILURE, SHOPIFY_SYNCHRONIZED, SHOPIFY_SYNCHRONIZED_FAILURE) (Plytix related deprecated)';

comment on column item_adjustment_request_detail.item_status is 'item status (eg: ACTIVE, DRAFT, DELETED, ARCHIVED)';

comment on column item_adjustment_request_detail.primary_vendor_item_aisle is 'The primary vendor item aisle of the product';

comment on column item_adjustment_request_detail.primary_vendor is 'The primary vendor of the product';

comment on column item_adjustment_request_detail.title is 'The title of a product is a marketing description for consumers, used to display the characteristics of the product on e-commerce platforms or in advertisements.';

comment on column item_adjustment_request_detail.package_size is 'Product packaging size';

comment on column item_adjustment_request_detail.item_size is 'Product item size';

comment on column item_adjustment_request_detail.department is 'The department of the product, Represents the first-level category of the product';

comment on column item_adjustment_request_detail.category is 'The category of the product, Represents the second-level category of the product';

comment on column item_adjustment_request_detail.sub_category is 'The sub-category of the product, Represents the third-level category of the product';

comment on column item_adjustment_request_detail.class_type is 'The class of the product, Represents the fourth-level category of the product';

comment on column item_adjustment_request_detail.brand is 'The brand of the product';

comment on column item_adjustment_request_detail.reg_price_pack_no_crv is 'Regular price pack no crv of the product';

comment on column item_adjustment_request_detail.primary_vendor_item_cost is 'The primary vendor item cost of the product';

comment on column item_adjustment_request_detail.vendor_item_cost is 'The vendor item cost of the product';

comment on column item_adjustment_request_detail.vendor_secondary_cost is 'The vendor secondary cost of the product';

comment on column item_adjustment_request_detail.upc is 'The UPC of the product (Deprecated)';

comment on column item_adjustment_request_detail.vendor_item_number is 'The vendor item number of the product';

comment on column item_adjustment_request_detail.disposition is 'The disposition of the product';

comment on column item_adjustment_request_detail.inventory is 'The inventory of the product';

comment on column item_adjustment_request_detail.failure_reason is 'The failure reason of the product. For specific types, see ItemAdjustmentFailureReason';

comment on column item_adjustment_request_detail.vendor is 'The vendor name of the product';

comment on column item_adjustment_request_detail.vendor_aisle is 'The vendor aisle of the product';

comment on column item_adjustment_request_detail.company_id is 'Created for later introduction of third-party sellers into the Mercaso platform. Different Company IDs represent different companies.';

comment on column item_adjustment_request_detail.location_id is 'Created for later introduction of third-party sellers into the Mercaso platform. Different Location IDs represent different locations.';

comment on column item_adjustment_request_detail.id_tag is 'The ID tag of the product, company_id and location_id are joined under the line';

comment on column item_adjustment_request_detail.new_description is 'New Description is a new field added in the recent UI refresh, with the main purpose of supporting the separation of Title and Subtitle. This change is intended to improve the readability and structured presentation of product information. This field is used in conjunction with other meta fields (such as packSize and itemSize) to generate clearer and more intuitive product titles and subtitles for users.';

comment on column item_adjustment_request_detail.attribute_name is 'The attribute name of the product';

comment on column item_adjustment_request_detail.attribute_value is 'The attribute value of the product';

comment on column item_adjustment_request_detail.item_unit_measure is 'The item unit measure of the product';

comment on column item_adjustment_request_detail.promo_flag is 'The promo flag of the product, used to indicate whether the product is a promotional product';

comment on column item_adjustment_request_detail.crv_flag is 'The crv flag of the product, used to indicate whether the product has CRV ';

comment on column item_adjustment_request_detail.promo_price_pack_no_crv is 'The promo price pack no crv of the product';

comment on column item_adjustment_request_detail.image_url is 'The main image URL of the product';

comment on column item_adjustment_request_detail.tags is 'The tags of the product, used to classify the product';

comment on column item_adjustment_request_detail.backup_vendor is 'The backup vendor name of the product';

comment on column item_adjustment_request_detail.case_upc is 'The case upc of the product';

comment on column item_adjustment_request_detail.each_upc is 'The each upc of the product';





