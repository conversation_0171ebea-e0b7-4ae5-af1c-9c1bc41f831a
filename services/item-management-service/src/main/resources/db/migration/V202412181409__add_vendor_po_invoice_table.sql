CREATE TABLE vendor_po_invoice
(
    id                UUID         NOT NULL PRIMARY KEY,
    source            VARCHAR(100) NOT NULL,
    vendor_id         UUID         NOT NULL,
    vendor_name       VARCHAR(100) NOT NULL,
    type              VARCHAR(50)  NOT NULL,
    payload           JSONB,
    created_at        TIMESTAMP    NOT NULL,
    created_by        VA<PERSON><PERSON>R(50)  NOT NULL,
    updated_at        TIMESTAMP,
    updated_by        VARCHAR(50),
    deleted_at        TIMESTAMP,
    deleted_by        VA<PERSON><PERSON>R(50),
    created_user_name varchar(64),
    updated_user_name varchar(64),
    deleted_user_name varchar(64)
);

create index vendor_po_invoice_vendor_name_idx on vendor_po_invoice (vendor_name);
create index vendor_po_invoice_vendor_id_idx on vendor_po_invoice (vendor_id);

COMMENT ON TABLE vendor_po_invoice IS 'This table stores purchase order invoices for vendors.';

COMMENT ON COLUMN vendor_po_invoice.id IS 'Primary key, unique identifier for the invoice.';
COMMENT ON COLUMN vendor_po_invoice.source IS 'Source of the invoice, e.g., automated script or manual input.';
COMMENT ON COLUMN vendor_po_invoice.vendor_id IS 'Foreign key referencing the vendor.';
COMMENT ON COLUMN vendor_po_invoice.vendor_name IS 'Name of the vendor associated with the invoice.';
COMMENT ON COLUMN vendor_po_invoice.type IS 'Type of the invoice, e.g., CSV file or json.';
COMMENT ON COLUMN vendor_po_invoice.payload IS 'JSONB field containing additional invoice data.';
COMMENT ON COLUMN vendor_po_invoice.created_at IS 'Timestamp when the invoice was created.';
COMMENT ON COLUMN vendor_po_invoice.created_by IS 'User or system that created the invoice.';
COMMENT ON COLUMN vendor_po_invoice.updated_at IS 'Timestamp when the invoice was last updated.';
COMMENT ON COLUMN vendor_po_invoice.updated_by IS 'User or system that last updated the invoice.';
COMMENT ON COLUMN vendor_po_invoice.deleted_at IS 'Timestamp when the invoice was soft-deleted.';
COMMENT ON COLUMN vendor_po_invoice.deleted_by IS 'User or system that soft-deleted the invoice.';

CREATE TABLE vendor_po_invoice_item
(
    id                   UUID        NOT NULL PRIMARY KEY,
    vendor_po_invoice_id UUID        NOT NULL,
    vendor_id            UUID        NOT NULL,
    matched_item_id      UUID,
    vendor_sku_number    VARCHAR(100),
    vendor_item_name     VARCHAR(100),
    UPC                  VARCHAR(255),
    unit_cost            DECIMAL,
    tax                  DECIMAL,
    crv                  DECIMAL,
    qty                  INT,
    price                DECIMAL     NOT NULL,
    created_at           TIMESTAMP   NOT NULL,
    created_by           VARCHAR(50) NOT NULL,
    updated_at           TIMESTAMP,
    updated_by           VARCHAR(50),
    deleted_at           TIMESTAMP,
    deleted_by           VARCHAR(50),
    created_user_name    varchar(64),
    updated_user_name    varchar(64),
    deleted_user_name    varchar(64)
);
create index vendor_po_invoice_item_invoice_id_idx on vendor_po_invoice_item (vendor_po_invoice_id);
create index vendor_po_invoice_item_vendor_id_idx on vendor_po_invoice_item (vendor_id);

COMMENT ON TABLE vendor_po_invoice_item IS 'This table stores line items associated with vendor invoices.';

COMMENT ON COLUMN vendor_po_invoice_item.id IS 'Primary key, unique identifier for the invoice item.';
COMMENT ON COLUMN vendor_po_invoice_item.vendor_po_invoice_id IS 'Foreign key referencing the vendor_po_invoice table.';
COMMENT ON COLUMN vendor_po_invoice_item.vendor_id IS 'Foreign key referencing the vendor.';
COMMENT ON COLUMN vendor_po_invoice_item.matched_item_id IS 'Reference to the matched item in the system.';
COMMENT ON COLUMN vendor_po_invoice_item.vendor_sku_number IS 'SKU number provided by the vendor.';
COMMENT ON COLUMN vendor_po_invoice_item.vendor_item_name IS 'Name of the item as specified by the vendor.';
COMMENT ON COLUMN vendor_po_invoice_item.UPC IS 'Universal Product Code for the item.';
COMMENT ON COLUMN vendor_po_invoice_item.unit_cost IS 'Cost per unit for the item.';
COMMENT ON COLUMN vendor_po_invoice_item.tax IS 'Tax amount applied to the item.';
COMMENT ON COLUMN vendor_po_invoice_item.crv IS 'California Redemption Value (CRV) for the item.';
COMMENT ON COLUMN vendor_po_invoice_item.qty IS 'Quantity of the item.';
COMMENT ON COLUMN vendor_po_invoice_item.price IS 'Total price for the item.';
COMMENT ON COLUMN vendor_po_invoice_item.created_at IS 'Timestamp when the invoice item was created.';
COMMENT ON COLUMN vendor_po_invoice_item.created_by IS 'User or system that created the invoice item.';
COMMENT ON COLUMN vendor_po_invoice_item.updated_at IS 'Timestamp when the invoice item was last updated.';
COMMENT ON COLUMN vendor_po_invoice_item.updated_by IS 'User or system that last updated the invoice item.';
COMMENT ON COLUMN vendor_po_invoice_item.deleted_at IS 'Timestamp when the invoice item was soft-deleted.';
COMMENT ON COLUMN vendor_po_invoice_item.deleted_by IS 'User or system that soft-deleted the invoice item.';