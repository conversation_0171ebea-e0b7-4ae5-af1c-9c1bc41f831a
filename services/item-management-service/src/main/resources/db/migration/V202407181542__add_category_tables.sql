CREATE TABLE category
(
    id                 UUID         NOT NULL PRIMARY KEY,
    name               <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    icon               VARCHAR(255) NOT NULL,
    description        VARCHAR(255) NOT NULL,
    parent_category_id UUID,
    status             VARCHAR(50)  NOT NULL,
    sort_order         FLOAT        NOT NULL,
    created_at         TIMESTAMP    NOT NULL,
    created_by         VA<PERSON><PERSON><PERSON>(50)  NOT NULL,
    updated_at         TIMESTAMP,
    updated_by         VARCHAR(50),
    deleted_at         TIMESTAMP,
    deleted_by         VARCHAR(50)
);

create index category_parent_category_id_idx on category (parent_category_id);

create index category_name_idx on category (name);
