comment on column item_adjustment_request.request_file is 'item adjustment request file name';

comment on column item_adjustment_request.type is 'item adjustment request type (eg: NEW_TEMPLATE_ADJUSTMENT, JC_ADJUSTMENT, DOWNEY_ADJUSTMENT)';

comment on column item_adjustment_request.status is 'item adjustment request status (eg: <PERSON><PERSON><PERSON>DE<PERSON>, F<PERSON>E_PROCESSED, CO<PERSON>LETED,FAILURE)';

comment on column item_adjustment_request.created_row_count is 'number of rows created in the item adjustment request file';

comment on column item_adjustment_request.modified_row_count is 'number of rows updated in the item adjustment request file';

comment on column item_adjustment_request.create_failed_row_count is 'number of rows failed to create in the item adjustment request file';

comment on column item_adjustment_request.create_success_row_count is 'number of rows success to create in the item adjustment request file';

comment on column item_adjustment_request.modify_failed_row_count is 'number of rows failed to update in the item adjustment request file';

comment on column item_adjustment_request.modify_success_row_count is 'number of rows success to update in the item adjustment request file';

comment on column item_adjustment_request.deleted_row_count is 'number of rows deleted in the item adjustment request file';

comment on column item_adjustment_request.delete_failed_row_count is 'number of rows failed to delete in the item adjustment request file';

comment on column item_adjustment_request.delete_success_row_count is 'number of rows success to delete in the item adjustment request file';

comment on column item_adjustment_request.detail_file is 'Deprecated';



