CREATE TABLE item_grade
(
    id                UUID         NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    item_id           varchar(64)  NOT NULL,
    sku_number        VARCHAR(100) NOT NULL,
    total_revenue     DECIMAL,
    pareto_grade      VARCHAR(50)  NOT NULL,
    created_at        TIMESTAMP    NOT NULL,
    created_by        VA<PERSON>HAR(50)  NOT NULL,
    updated_at        TIMESTAMP,
    updated_by        VARCHAR(50),
    deleted_at        TIMESTAMP,
    deleted_by        VARCHAR(50),
    created_user_name varchar(64),
    updated_user_name varchar(64),
    deleted_user_name varchar(64)
);

CREATE UNIQUE INDEX idx_item_id_unique ON item_grade (item_id);
CREATE UNIQUE INDEX idx_sku_number_unique ON item_grade (sku_number);

COMMENT ON TABLE item_grade IS 'This table stores Item grade.';

COMMENT ON COLUMN item_grade.id IS 'Primary key, unique identifier for the Item grade.';
COMMENT ON COLUMN item_grade.item_id IS 'UNIQUE item_id of the  Item grade.';
COMMENT ON COLUMN item_grade.total_revenue IS 'Total revenue of the Item.';
COMMENT ON COLUMN item_grade.pareto_grade IS 'Grade of the Item grade.';