alter table attribute
    add created_user_name varchar(64);
alter table attribute
    add updated_user_name varchar(64);
alter table attribute
    add deleted_user_name varchar(64);

alter table brand
    add created_user_name varchar(64);
alter table brand
    add updated_user_name varchar(64);
alter table brand
    add deleted_user_name varchar(64);

alter table category
    add created_user_name varchar(64);
alter table category
    add updated_user_name varchar(64);
alter table category
    add deleted_user_name varchar(64);


alter table item
    add created_user_name varchar(64);
alter table item
    add updated_user_name varchar(64);
alter table item
    add deleted_user_name varchar(64);


alter table item_adjustment_request
    add created_user_name varchar(64);
alter table item_adjustment_request
    add updated_user_name varchar(64);
alter table item_adjustment_request
    add deleted_user_name varchar(64);


alter table item_adjustment_request_detail
    add created_user_name varchar(64);
alter table item_adjustment_request_detail
    add updated_user_name varchar(64);
alter table item_adjustment_request_detail
    add deleted_user_name varchar(64);


alter table item_attribute
    add created_user_name varchar(64);
alter table item_attribute
    add updated_user_name varchar(64);
alter table item_attribute
    add deleted_user_name varchar(64);


alter table item_image
    add created_user_name varchar(64);
alter table item_image
    add updated_user_name varchar(64);
alter table item_image
    add deleted_user_name varchar(64);


alter table item_promo_price
    add created_user_name varchar(64);
alter table item_promo_price
    add updated_user_name varchar(64);
alter table item_promo_price
    add deleted_user_name varchar(64);


alter table item_tag
    add created_user_name varchar(64);
alter table item_tag
    add updated_user_name varchar(64);
alter table item_tag
    add deleted_user_name varchar(64);


alter table item_upc
    add created_user_name varchar(64);
alter table item_upc
    add updated_user_name varchar(64);
alter table item_upc
    add deleted_user_name varchar(64);


alter table plytix_other_data
    add created_user_name varchar(64);
alter table plytix_other_data
    add updated_user_name varchar(64);
alter table plytix_other_data
    add deleted_user_name varchar(64);


alter table vendor
    add created_user_name varchar(64);
alter table vendor
    add updated_user_name varchar(64);
alter table vendor
    add deleted_user_name varchar(64);


alter table vendor_item
    add created_user_name varchar(64);
alter table vendor_item
    add updated_user_name varchar(64);
alter table vendor_item
    add deleted_user_name varchar(64);