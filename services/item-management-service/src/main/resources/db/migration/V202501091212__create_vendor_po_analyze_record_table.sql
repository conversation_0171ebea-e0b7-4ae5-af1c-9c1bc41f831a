create table vendor_po_analyze_record
(
    id                UUID         NOT NULL PRIMARY KEY,
    vendor_id         UUID         NOT NULL,
    vendor_name       VARCHAR(100) NOT NULL,
    analysis_source    varchar,
    original_file_name varchar,
    analysis_result_payload           text,
    status VARCHAR(64),
    rcpt_id VARCHAR(64),
    rcpt_date VARCHAR(64),
    created_at        TIMESTAMP    NOT NULL,
    created_by        VARC<PERSON>R(50)  NOT NULL,
    updated_at        TIMESTAMP,
    updated_by        VARC<PERSON>R(50),
    deleted_at        TIMESTAMP,
    deleted_by        VARCHAR(50),
    created_user_name varchar(64),
    updated_user_name varchar(64),
    deleted_user_name varchar(64)
);

comment on column vendor_po_analyze_record.vendor_id is 'supplier table id';

comment on column vendor_po_analyze_record.vendor_name is 'supplier name';

comment on column vendor_po_analyze_record.analysis_source is 'supplier of parse file, e.g. aws, google, etc.';

comment on column vendor_po_analyze_record.status is 'analyze status (unapproved, approved)';

comment on column vendor_po_analyze_record.original_file_name is 'file name stored in S3';

comment on column vendor_po_analyze_record.rcpt_id is 'supplier invoice Unique ID';

comment on column vendor_po_analyze_record.rcpt_date is 'supplier invoice rcpt date';

