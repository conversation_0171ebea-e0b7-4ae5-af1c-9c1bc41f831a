CREATE TABLE vendor_item_availability_snapshot
(
    id            UUID         NOT NULL PRIMARY KEY,
    vendor_id     UUID         NOT NULL,
    snapshot_time TIMESTAMP    NOT NULL,
    snapshot_type VARCHAR(50)  NOT NULL, -- 'SHUTDOWN' or 'RESTORE'
    created_at    TIMESTAMP    NOT NULL,
    created_by    <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at    TIMESTAMP,
    updated_by    <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_at    TIMESTAMP,
    deleted_by    <PERSON><PERSON><PERSON><PERSON>(255)
);

CREATE TABLE vendor_item_availability_snapshot_detail
(
    id                    UUID         NOT NULL PRIMARY KEY,
    snapshot_id           UUID         NOT NULL,
    vendor_item_id        UUID         NOT NULL,
    vendor_sku_number     VARCHAR(255),
    item_id               UUID,
    previous_availability BOOLEAN,
    new_availability      BOOLEAN,
    created_at            TIMESTAMP    NOT NULL,
    created_by            <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at            TIMESTAMP,
    updated_by            <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_at            TIMESTAMP,
    deleted_by            VA<PERSON><PERSON><PERSON>(255),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (snapshot_id) REFERENCES vendor_item_availability_snapshot (id)
);


CREATE INDEX idx_vendor_item_availability_snapshot_vendor_id ON vendor_item_availability_snapshot (vendor_id);
CREATE INDEX idx_vendor_item_availability_snapshot_time ON vendor_item_availability_snapshot (snapshot_time);
CREATE INDEX idx_vendor_item_availability_snapshot_detail_snapshot_id ON vendor_item_availability_snapshot_detail (snapshot_id);
CREATE INDEX idx_vendor_item_availability_snapshot_detail_vendor_item_id ON vendor_item_availability_snapshot_detail (vendor_item_id);


COMMENT ON TABLE vendor_item_availability_snapshot IS 'vendor_item_availability_snapshot';
COMMENT ON COLUMN vendor_item_availability_snapshot.vendor_id IS 'vendor_id';
COMMENT ON COLUMN vendor_item_availability_snapshot.snapshot_time IS 'snapshot_time';

COMMENT ON TABLE vendor_item_availability_snapshot_detail IS 'vendor_item_availability_snapshot_detail';
COMMENT ON COLUMN vendor_item_availability_snapshot_detail.snapshot_id IS 'snapshot_id';
COMMENT ON COLUMN vendor_item_availability_snapshot_detail.vendor_item_id IS 'vendor_item_id';
COMMENT ON COLUMN vendor_item_availability_snapshot_detail.vendor_sku_number IS 'vendor_sku_number';
COMMENT ON COLUMN vendor_item_availability_snapshot_detail.item_id IS 'item_id';
COMMENT ON COLUMN vendor_item_availability_snapshot_detail.previous_availability IS 'previous_availability';
COMMENT ON COLUMN vendor_item_availability_snapshot_detail.new_availability IS 'new_availability';