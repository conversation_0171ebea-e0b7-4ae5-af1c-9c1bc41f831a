CREATE TABLE item_price_group
(
    id                UUID         NOT NULL PRIMARY KEY,
    group_name            <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    created_at        TIMESTAMP    NOT NULL,
    created_by        VA<PERSON><PERSON><PERSON>(50)  NOT NULL,
    updated_at        TIMESTAMP,
    updated_by        <PERSON><PERSON><PERSON><PERSON>(50),
    deleted_at        TIMESTAMP,
    deleted_by        <PERSON><PERSON><PERSON><PERSON>(50),
    created_user_name varchar(64),
    updated_user_name varchar(64),
    deleted_user_name varchar(64)
);

CREATE UNIQUE INDEX idx_group_name_unique ON item_price_group(group_name);

COMMENT ON TABLE item_price_group IS 'This table stores Item price group.';

COMMENT ON COLUMN item_price_group.id IS 'Primary key, unique identifier for the Item price group.';
COMMENT ON COLUMN item_price_group.group_name IS 'UNIQUE Name of the Item price group.';