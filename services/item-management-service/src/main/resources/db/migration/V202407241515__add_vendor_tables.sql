CREATE TABLE vendor
(
    id                  UUID         NOT NULL PRIMARY KEY,
    vendor_name         <PERSON><PERSON><PERSON><PERSON>(255),
    vendor_contact_name <PERSON><PERSON><PERSON><PERSON>(255),
    vendor_contact_tel  VARCHAR(255),
    vendor_company_name VA<PERSON>HA<PERSON>(255),
    status              VARCHAR(255),
    created_at          TIMESTAMP    NOT NULL,
    created_by          <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at          TIMESTAMP,
    updated_by          VA<PERSON><PERSON><PERSON>(255),
    deleted_at          TIMESTAMP,
    deleted_by          VARCHAR(255)
);


create index vendor_vendor_name_idx on vendor (vendor_name);
