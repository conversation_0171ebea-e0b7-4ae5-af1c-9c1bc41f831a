CREATE TABLE item_adjustment_request
(
    id                       UUID         NOT NULL PRIMARY KEY,
    request_file             VARCHAR(255) NOT NULL,
    type                     VARCHAR(50)  NOT NULL,
    status                   VARCHAR(50)  NOT NULL,
    created_row_count        INT,
    modified_row_count       INT,
    create_failed_row_count  INT,
    create_success_row_count INT,
    modify_failed_row_count  INT,
    modify_success_row_count INT,
    created_at               TIMESTAMP    NOT NULL,
    created_by               VARCHAR(50)  NOT NULL,
    updated_at               TIMESTAMP,
    updated_by               VARCHAR(50),
    deleted_at               TIMESTAMP,
    deleted_by               VARCHAR(50)
);


CREATE TABLE item_adjustment_request_detail
(
    id                    UUID        NOT NULL PRIMARY KEY,
    request_id            UUID,
    type                  VARCHAR(50) NOT NULL,
    status                VARCHAR(50) NOT NULL,
    sku                   VARCHAR(50) NOT NULL,
    item_status           VARCHAR(50),
    aisle                 VARCHAR(50),
    primary_vendor        VARCHAR(255),
    title                 VARCHAR(255),
    package_size          INT,
    bottle_size           FLOAT,
    department            VARCHAR(255),
    category              VARCHAR(255),
    sub_category          VARCHAR(255),
    class_type            VA<PERSON>HAR(255),
    brand                 VARCHAR(255),
    reg_price_pack_no_crv DECIMAL,
    downey_cost           DECIMAL,
    jc_web_price          DECIMAL,
    jc_sales_price        DECIMAL,
    upc                   VARCHAR(255),
    vendor_item_number    VARCHAR(255),
    disposition           VARCHAR(255),
    inventory             INT,
    created_at            TIMESTAMP   NOT NULL,
    created_by            VARCHAR(50) NOT NULL,
    updated_at            TIMESTAMP,
    updated_by            VARCHAR(50),
    deleted_at            TIMESTAMP,
    deleted_by            VARCHAR(50)
);
create index item_adjustment_request_detail_request_id_idx on item_adjustment_request_detail (request_id);

