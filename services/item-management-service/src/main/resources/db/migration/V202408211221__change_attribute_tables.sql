ALTER TABLE attribute
    ALTER COLUMN category_id DROP NOT NULL;

insert into attribute (id, name, description, attribute_format, status, category_id, created_at, created_by, updated_at)
values ('a8e3f151-2151-44f8-a2d8-3ce0b90b8cdc', 'item size', 'item size', 'NUMBER', 'ACTIVE', null, now(), 'system',
        now());
insert into attribute (id, name, description, attribute_format, status, category_id, created_at, created_by, updated_at)
values ('d6b888b8-8e53-4dcd-b5e3-63be5036e0c0', 'unit size', 'unit size', 'NUMBER', 'ACTIVE', null, now(), 'system',
        now());
insert into attribute (id, name, description, attribute_format, status, category_id, created_at, created_by, updated_at)
values ('c2d42be6-7819-4154-94f6-9aa1935ba2fc', 'Variant Weight', 'Variant Weight', 'NUMBER', 'ACTIVE', null, now(),
        'system', now());
insert into attribute (id, name, description, attribute_format, status, category_id, created_at, created_by, updated_at)
values ('adeeb5fb-452c-428f-a666-b2842f7dc11a', 'volume value', 'volume value', 'NUMBER', 'ACTIVE', null, now(),
        'system', now());

update item_attribute
set attribute_id = 'a8e3f151-2151-44f8-a2d8-3ce0b90b8cdc'
where attribute_id in (select id
                       from attribute
                       where name = 'item size');

update item_attribute
set attribute_id = 'd6b888b8-8e53-4dcd-b5e3-63be5036e0c0'
where attribute_id in (select id
                       from attribute
                       where name = 'unit size');

update item_attribute
set attribute_id = 'c2d42be6-7819-4154-94f6-9aa1935ba2fc'
where attribute_id in (select id
                       from attribute
                       where name = 'Variant Weight');

update item_attribute
set attribute_id = 'adeeb5fb-452c-428f-a666-b2842f7dc11a'
where attribute_id in (select id
                       from attribute
                       where name = 'volume value');


delete
from attribute
where name = 'item size'
  and category_id is not null;
delete
from attribute
where name = 'unit size'
  and category_id is not null;
delete
from attribute
where name = 'Variant Weight'
  and category_id is not null;
delete
from attribute
where name = 'volume value'
  and category_id is not null;