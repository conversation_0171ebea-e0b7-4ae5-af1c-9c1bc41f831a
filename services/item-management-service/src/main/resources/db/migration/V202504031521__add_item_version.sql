CREATE TABLE item_version
(
    id             UUID PRIMARY KEY,
    item_id        UUID NOT NULL,
    sku_number     VARCHAR(255) NOT NULL,
    version_number VARCHAR(50)   NOT NULL DEFAULT 'V0.01',
    item_data      JSONB NOT NULL,
    created_at        TIMESTAMP    NOT NULL,
    created_by        <PERSON><PERSON><PERSON><PERSON>(50)  NOT NULL,
    updated_at        TIMESTAMP,
    updated_by        <PERSON><PERSON><PERSON><PERSON>(50),
    deleted_at        TIMESTAMP,
    deleted_by        <PERSON><PERSON><PERSON><PERSON>(50),
    created_user_name varchar(64),
    updated_user_name varchar(64),
    deleted_user_name varchar(64)
);

create unique index item_version_item_id_version_number_idx on item_version (item_id, version_number);
create unique index item_version_sku_number_version_number_idx on item_version (sku_number, version_number);

COMMENT ON TABLE item_version IS 'This table stores item change version data.';

COMMENT ON COLUMN item_version.id IS 'Primary key, unique identifier for the exception record.';
COMMENT ON COLUMN item_version.item_id IS 'Item table primary key.';
COMMENT ON COLUMN item_version.sku_number IS 'Item SKU number.';
COMMENT ON COLUMN item_version.item_data IS 'Save item info data in JSON format.';