-- Create address table
CREATE TABLE address (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type VARCHAR(100) NOT NULL,
    entity_id UUID NOT NULL,
    street_address VARCHAR(500) NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    directions VARCHAR(1000),
    purpose VARCHAR(20) NOT NULL,
    additional_lines VARCHAR(1000),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    created_user_name VARCHAR(100),
    updated_at TIMESTAMP,
    updated_by VARCHAR(100),
    updated_user_name VARCHAR(100),
    deleted_at TIMESTAMP,
    deleted_by VARCHAR(100),
    deleted_user_name VARCHAR(100)
);

-- Add comments to table
COMMENT ON TABLE address IS 'Address information for various entities';

-- Add comments to columns
COMMENT ON COLUMN address.entity_type IS 'Entity type (e.g., Vendor, Customer, etc.)';
COMMENT ON COLUMN address.entity_id IS 'Entity ID reference';
COMMENT ON COLUMN address.street_address IS 'Street address';
COMMENT ON COLUMN address.city IS 'City name';
COMMENT ON COLUMN address.state IS 'State or province';
COMMENT ON COLUMN address.postal_code IS 'Postal or ZIP code';
COMMENT ON COLUMN address.country IS 'Country name';
COMMENT ON COLUMN address.directions IS 'Additional directions or landmarks';
COMMENT ON COLUMN address.purpose IS 'Address purpose (BILLING, SHIPPING, MAILING, BUSINESS, HOME, WAREHOUSE, OFFICE, OTHER)';
COMMENT ON COLUMN address.additional_lines IS 'Additional address lines';
COMMENT ON COLUMN address.created_at IS 'Creation timestamp';
COMMENT ON COLUMN address.created_by IS 'User who created the record';
COMMENT ON COLUMN address.created_user_name IS 'Name of user who created the record';
COMMENT ON COLUMN address.updated_at IS 'Last update timestamp';
COMMENT ON COLUMN address.updated_by IS 'User who last updated the record';
COMMENT ON COLUMN address.updated_user_name IS 'Name of user who last updated the record';
COMMENT ON COLUMN address.deleted_at IS 'Soft delete timestamp';
COMMENT ON COLUMN address.deleted_by IS 'User who deleted the record';
COMMENT ON COLUMN address.deleted_user_name IS 'Name of user who deleted the record';

-- Create indexes for better query performance
CREATE INDEX idx_address_entity_type_entity_id ON address(entity_type, entity_id);
CREATE INDEX idx_address_purpose ON address(purpose);
CREATE INDEX idx_address_postal_code ON address(postal_code);
CREATE INDEX idx_address_city ON address(city);
CREATE INDEX idx_address_state ON address(state);
CREATE INDEX idx_address_country ON address(country);
CREATE INDEX idx_address_deleted_at ON address(deleted_at);

-- Add constraints
ALTER TABLE address ADD CONSTRAINT chk_address_street_address_length CHECK (LENGTH(street_address) >= 5 AND LENGTH(street_address) <= 500);
ALTER TABLE address ADD CONSTRAINT chk_address_city_length CHECK (LENGTH(city) >= 2 AND LENGTH(city) <= 100);
ALTER TABLE address ADD CONSTRAINT chk_address_purpose_valid CHECK (purpose IN ('BILLING', 'SHIPPING', 'MAILING', 'BUSINESS', 'HOME', 'WAREHOUSE', 'OFFICE', 'OTHER', 'UNKNOWN'));
