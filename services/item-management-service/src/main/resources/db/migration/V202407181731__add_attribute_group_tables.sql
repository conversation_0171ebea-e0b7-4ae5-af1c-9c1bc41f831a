CREATE TABLE attribute_group
(
    id          UUID         NOT NULL PRIMARY KEY,
    category_id UUID         NOT NULL,
    name        <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    description VARCHAR(255) NOT NULL,
    status      VARCHAR(50)  NOT NULL,
    created_at  TIMESTAMP    NOT NULL,
    created_by  <PERSON><PERSON><PERSON><PERSON>(50)  NOT NULL,
    updated_at  TIMESTAMP,
    updated_by  VARC<PERSON>R(50),
    deleted_at  TIMESTAMP,
    deleted_by  VARCHAR(50)
);
create index attribute_group_category_id_idx on attribute_group (category_id);


CREATE TABLE attribute_group_detail
(
    id                 UUID        NOT NULL PRIMARY KEY,
    attribute_group_id UUID        NOT NULL,
    attribute_id       UUID        NOT NULL,
    required           boolean     NOT NULL,
    sort_order         FLOAT       NOT NULL,
    created_at         TIMESTAMP   NOT NULL,
    created_by         <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    updated_at         TIMESTAMP,
    updated_by         <PERSON><PERSON><PERSON><PERSON>(50),
    deleted_at         TIMESTAMP,
    deleted_by         <PERSON><PERSON><PERSON><PERSON>(50)
);
create index attribute_group_detail_attribute_group_id_idx on attribute_group_detail (attribute_group_id);
create index attribute_group_detail_attribute_id_idx on attribute_group_detail (attribute_id);

