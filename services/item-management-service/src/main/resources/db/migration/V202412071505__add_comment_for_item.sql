comment on column item.category_id is 'The leaf node of the category where the product is located';

comment on column item.brand_id is 'The brand ID of the product';

comment on column item.name is 'The name of a product is a simple, direct identifier that describes the basic information of the product. It is usually the core identifier of the product.';

comment on column item.title is 'The title of a product is a marketing description for consumers, used to display the characteristics of the product on e-commerce platforms or in advertisements.';

comment on column item.sku_number is 'Each product has a unique SKU number.';

comment on column item.description is 'Description of the product';

comment on column item.note is 'The product''s notes field is used to record additional information or comments related to the product.';

comment on column item.photo is 'The main picture of the product';

comment on column item.primary_vendor_id is 'The primary vendor ID of the product';

comment on column item.detail is 'A detailed description of the product, used to store rich information related to the product';

comment on column item.package_type is 'Product packaging type (eg: PACK, INDIVIDUAL, etc.)';

comment on column item.package_size is 'Product packaging size';

comment on column item.shelf_life is 'Shelf life of the product';

comment on column item.item_type is 'Used to identify the operation type or ownership type of the product, reflecting the difference in the operation model or responsibility division of the product (eg: SELF_OPERATED, THIRD_PARTY, HOSTED, etc.)';

comment on column item.sales_status is 'Sales status of the product (eg: LISTING, DELISTING, etc.)';

comment on column item.availability_status is 'Sales status of the product (eg: ACTIVE, DRAFT, DELETED, ARCHIVED, etc.)';

comment on column item.handle is 'The product''s handle is the URL of the website page automatically generated by Shopify and again used directly by the customer.';

comment on column item.company_id is 'Created for later introduction of third-party sellers into the Mercaso platform. Different Company IDs represent different companies.';

comment on column item.location_id is 'Created for later introduction of third-party sellers into the Mercaso platform. Different Location IDs represent different locations.';

comment on column item.department is 'The department of the product, Represents the first-level category of the product';

comment on column item.category is 'The category of the product, Represents the second-level category of the product';

comment on column item.sub_category is 'The sub-category of the product, Represents the third-level category of the product';

comment on column item.clazz is 'The class of the product, Represents the fourth-level category of the product';

comment on column item.new_description is 'New Description is a new field added in the recent UI refresh, with the main purpose of supporting the separation of Title and Subtitle. This change is intended to improve the readability and structured presentation of product information. This field is used in conjunction with other meta fields (such as packSize and itemSize) to generate clearer and more intuitive product titles and subtitles for users.';

comment on column item.backup_vendor_id is 'The designated backup supplier of the product';