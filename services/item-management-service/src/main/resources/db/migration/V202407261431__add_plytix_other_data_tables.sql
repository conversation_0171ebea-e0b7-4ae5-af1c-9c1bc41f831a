CREATE TABLE plytix_other_data
(
    id                          UUID         NOT NULL PRIMARY KEY,
    item_id                     UUID,
    sku                         VARCHAR(255),
    body_html                   VARCHAR(255),
    type                        VA<PERSON>HAR(255),
    variant_cost                DECIMAL,
    variant_fulfillment_service VARCHAR(255),
    variant_inventory_policy    VARCHAR(255),
    variant_inventory_tracker   VARCHAR(255),
    variant_requires_shipping   BOOLEAN,
    variant_taxable             BOOLEAN,
    published_scope             VARCHAR(255),
    published_at                VARCHAR(64),
    upc_reference               VARCHAR(255),
    awesome_price               DECIMAL,
    banner                      VARCHAR(255),
    banner_expiry                VARCHAR(255),
    banner_flag                  VARCHAR(255),
    clazz                       VARCHAR(255),
    cooler                      BOOLEAN,
    new_description             VARCHAR(255),
    files_migration             VARCHAR(255),
    id_tag                      VARCHAR(255),
    image_alt_text               VARCHAR(255),
    image_height                DOUBLE PRECISION,
    image_notes                 VARCHAR(255),
    image_width                 DOUBLE PRECISION,
    mfc_item                    <PERSON>OOLEAN,
    prop_65                     BOOLEAN,
    secondary_handle            VARCHAR(255),
    variant_inventory_item_id   VARCHAR(255),
    variant_jc_show_app_price   DOUBLE PRECISION,
    venture_partners_cost       DECIMAL,
    product_id                  VARCHAR(255),
    variation_of                VARCHAR(255),
    variants                    VARCHAR(255),
    assets                      VARCHAR(255),
    created_at                  TIMESTAMP    NOT NULL,
    created_by                  VARCHAR(255) NOT NULL,
    updated_at                  TIMESTAMP,
    updated_by                  VARCHAR(255),
    deleted_at                  TIMESTAMP,
    deleted_by                  VARCHAR(255)
);

create index plytix_other_data_item_id_idx on plytix_other_data (item_id);
