DROP INDEX IF EXISTS business_event_entity_id_idx;
ALTER TABLE business_event
    DROP COLUMN IF EXISTS entity_id;

-- business_event_entity
CREATE TABLE business_event_entity
(
    id                UUID         NOT NULL PRIMARY KEY,
    business_event_id UUID         NOT NULL,
    entity_id         UUID         NOT NULL,
    entity_type       VARCHAR(255) NOT NULL,
    created_at        TIMESTAMP    NOT NULL,
    created_by        VARCHAR(50)  NOT NULL,
    updated_at        TIMESTAMP,
    updated_by        VARCHAR(50),
    deleted_at        TIMESTAMP,
    deleted_by        VARCHAR(50)
);
CREATE INDEX business_event_entity_business_event_id_idx ON business_event_entity (business_event_id);
CREATE INDEX business_event_entity_entity_id_idx ON business_event_entity (entity_id);
CREATE INDEX business_event_entity_entity_type_idx ON business_event_entity (entity_type);
