CREATE TABLE exception_record
(
    id                UUID         NOT NULL PRIMARY KEY,
    business_event_id UUID         NOT NULL,
    entity_id         UUID         NOT NULL,
    entity_type       VARCHAR(100) NOT NULL,
    status            VARCHAR(50),
    exception_type    VARCHAR(100),
    description       VARCHAR(255),
    created_at        TIMESTAMP    NOT NULL,
    created_by        <PERSON><PERSON><PERSON>R(50)  NOT NULL,
    updated_at        TIMESTAMP,
    updated_by        VA<PERSON><PERSON>R(50),
    deleted_at        TIMESTAMP,
    deleted_by        VARCHAR(50),
    created_user_name varchar(64),
    updated_user_name varchar(64),
    deleted_user_name varchar(64)
);
create index exception_record_entity_id_idx on exception_record (entity_id);
create index exception_record_exception_type_idx on exception_record (exception_type);

COMMENT ON TABLE exception_record IS 'This table stores IMS business exception.';

COMMENT ON COLUMN exception_record.id IS 'Primary key, unique identifier for the exception record.';
COMMENT ON COLUMN exception_record.business_event_id IS 'Foreign key referencing the business event.';
COMMENT ON COLUMN exception_record.entity_id IS 'Reference to the entity that caused the exception.';
COMMENT ON COLUMN exception_record.status IS 'Status of the exception record.';
COMMENT ON COLUMN exception_record.exception_type IS 'Type of the exception.';
COMMENT ON COLUMN exception_record.description IS 'Description of the exception.';
COMMENT ON COLUMN exception_record.created_at IS 'Timestamp when the exception record was created.';
COMMENT ON COLUMN exception_record.created_by IS 'User or system that created the exception record.';

