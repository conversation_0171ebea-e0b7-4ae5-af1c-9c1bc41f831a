CREATE TABLE item_reg_price
(
    id                   UUID         NOT NULL PRIMARY KEY,
    item_id              UUID,
    crv                  DECIMAL,
    reg_price            DECIMAL,
    reg_price_individual DECIMAL,
    reg_price_plus_crv   DECIMAL,
    status               VARCHAR(255),
    created_at           TIMESTAMP    NOT NULL,
    created_by           <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at           TIMESTAMP,
    updated_by           VA<PERSON><PERSON><PERSON>(255),
    deleted_at           TIMESTAMP,
    deleted_by           VARCHAR(255)
);

create index item_reg_price_item_id_idx on item_reg_price (item_id);