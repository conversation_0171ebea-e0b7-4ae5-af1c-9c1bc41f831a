CREATE TABLE bulk_export_records
(
    id          UUID PRIMARY KEY,
    file_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    search_time TIMESTAMP,
    send_email_time TIMESTAMP,
    custom_filter JSONB,
    created_at  TIMESTAMP NOT NULL,
    created_by  <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    updated_at  TIMESTAMP,
    updated_by  <PERSON><PERSON><PERSON><PERSON>(50),
    deleted_at  TIMESTAMP,
    deleted_by  VA<PERSON><PERSON><PERSON>(50),
    created_user_name varchar(64),
    updated_user_name varchar(64),
    deleted_user_name varchar(64)
);

COMMENT ON TABLE bulk_export_records IS 'This table stores bulk export item data.';

COMMENT ON COLUMN bulk_export_records.id IS 'Primary key, unique identifier for the exception record.';
COMMENT ON COLUMN bulk_export_records.file_name IS 'Exported filename.';
COMMENT ON COLUMN bulk_export_records.search_time IS 'Search time when exporting item.';
COMMENT ON COLUMN bulk_export_records.send_email_time IS 'The time to send an email';
COMMENT ON COLUMN bulk_export_records.custom_filter IS 'Export the search conditions for Item.';