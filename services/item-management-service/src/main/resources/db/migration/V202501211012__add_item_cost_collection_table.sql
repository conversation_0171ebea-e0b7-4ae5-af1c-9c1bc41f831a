DROP TABLE IF EXISTS vendor_po_invoice;

CREATE TABLE item_cost_collection
(
    id                UUID         NOT NULL PRIMARY KEY,
    source            VARCHAR(100) NOT NULL,
    vendor_id         UUID         NOT NULL,
    vendor_name       VARCHAR(100) NOT NULL,
    collection_number VARCHAR(100) NOT NULL,
    file_name         VARCHAR(100) NOT NULL,
    type              VA<PERSON>HAR(50)  NOT NULL,
    created_at        TIMESTAMP    NOT NULL,
    created_by        <PERSON><PERSON><PERSON><PERSON>(50)  NOT NULL,
    updated_at        TIMESTAMP,
    updated_by        <PERSON><PERSON><PERSON><PERSON>(50),
    deleted_at        TIMESTAMP,
    deleted_by        VARC<PERSON>R(50),
    created_user_name varchar(64),
    updated_user_name varchar(64),
    deleted_user_name varchar(64)
);

create index item_cost_collection_vendor_name_idx on item_cost_collection (vendor_name);
create index item_cost_collection_vendor_id_idx on item_cost_collection (vendor_id);
create index item_cost_collection_collection_number_idx on item_cost_collection (collection_number);

COMMENT ON TABLE item_cost_collection IS 'This table stores Item cost collection for vendors.';

COMMENT ON COLUMN item_cost_collection.id IS 'Primary key, unique identifier for the Item cost collection.';
COMMENT ON COLUMN item_cost_collection.source IS 'Source of the Item cost collection, e.g., automated script or manual input.';
COMMENT ON COLUMN item_cost_collection.vendor_id IS 'Foreign key referencing the vendor.';
COMMENT ON COLUMN item_cost_collection.vendor_name IS 'Name of the vendor associated with the Item cost collection.';
COMMENT ON COLUMN item_cost_collection.type IS 'Type of the Item cost collection file, e.g., CSV file or json.';
COMMENT ON COLUMN item_cost_collection.collection_number IS 'Number of the Item cost collection file.';
COMMENT ON COLUMN item_cost_collection.file_name IS 'File name of the Item cost collection.';
COMMENT ON COLUMN item_cost_collection.created_at IS 'Timestamp when the collection was created.';
COMMENT ON COLUMN item_cost_collection.created_by IS 'User or system that created the collection.';
COMMENT ON COLUMN item_cost_collection.updated_at IS 'Timestamp when the collection was last updated.';
COMMENT ON COLUMN item_cost_collection.updated_by IS 'User or system that last updated the collection.';
COMMENT ON COLUMN item_cost_collection.deleted_at IS 'Timestamp when the collection was soft-deleted.';
COMMENT ON COLUMN item_cost_collection.deleted_by IS 'User or system that soft-deleted the collection.';

DROP TABLE IF EXISTS vendor_po_invoice_item;

CREATE TABLE item_cost_change_request
(
    id                      UUID        NOT NULL PRIMARY KEY,
    item_cost_collection_id UUID        NOT NULL,
    vendor_id               UUID        NOT NULL,
    item_id                 UUID,
    sku_number              VARCHAR(100),
    vendor_sku_number       VARCHAR(100),
    vendor_item_name        VARCHAR(100),
    status                  VARCHAR(50),
    match_type              VARCHAR(50),
    previous_cost           DECIMAL,
    target_cost             DECIMAL,
    tax                     DECIMAL,
    crv                     DECIMAL,
    created_at              TIMESTAMP   NOT NULL,
    created_by              VARCHAR(50) NOT NULL,
    updated_at              TIMESTAMP,
    updated_by              VARCHAR(50),
    deleted_at              TIMESTAMP,
    deleted_by              VARCHAR(50),
    created_user_name       varchar(64),
    updated_user_name       varchar(64),
    deleted_user_name       varchar(64)
);
create index item_cost_change_request_item_cost_collection_id_idx on item_cost_change_request (item_cost_collection_id);
create index item_cost_change_request_vendor_id_idx on item_cost_change_request (vendor_id);

COMMENT ON TABLE item_cost_change_request IS 'This table stores Item cost change request for vendors.';

COMMENT ON COLUMN item_cost_change_request.id IS 'Primary key, unique identifier for the invoice item.';
COMMENT ON COLUMN item_cost_change_request.item_cost_collection_id IS 'Foreign key referencing the vendor_po_invoice table.';
COMMENT ON COLUMN item_cost_change_request.vendor_id IS 'Foreign key referencing the vendor.';
COMMENT ON COLUMN item_cost_change_request.item_id IS 'Reference to the matched item in the system.';
COMMENT ON COLUMN item_cost_change_request.vendor_sku_number IS 'SKU number provided by the vendor.';
COMMENT ON COLUMN item_cost_change_request.vendor_item_name IS 'Name of the item as specified by the vendor.';
COMMENT ON COLUMN item_cost_change_request.previous_cost IS 'Previous Cost for the item.';
COMMENT ON COLUMN item_cost_change_request.target_cost IS 'Target Cost for the item.';
COMMENT ON COLUMN item_cost_change_request.tax IS 'Tax amount applied to the item.';
COMMENT ON COLUMN item_cost_change_request.crv IS 'California Redemption Value (CRV) for the item.';
COMMENT ON COLUMN item_cost_change_request.created_at IS 'Timestamp when the invoice item was created.';
COMMENT ON COLUMN item_cost_change_request.created_by IS 'User or system that created the invoice item.';
COMMENT ON COLUMN item_cost_change_request.updated_at IS 'Timestamp when the invoice item was last updated.';
COMMENT ON COLUMN item_cost_change_request.updated_by IS 'User or system that last updated the invoice item.';
COMMENT ON COLUMN item_cost_change_request.deleted_at IS 'Timestamp when the invoice item was soft-deleted.';
COMMENT ON COLUMN item_cost_change_request.deleted_by IS 'User or system that soft-deleted the invoice item.';
comment on column item_cost_change_request.match_type is 'Match type (MISSING_UPC, COST_SAME, MATCHED)';

alter table vendor_po_analyze_record
    drop column vendor_po_invoice_id;

alter table vendor_po_analyze_record
    add item_cost_collection_id UUID;

comment on column vendor_po_analyze_record.item_cost_collection_id is 'item cost collection id';

alter table vendor_po_analyze_record
    add analysis_expense_payload JSONB;
comment on column vendor_po_analyze_record.analysis_expense_payload is 'analysis expense payload';

