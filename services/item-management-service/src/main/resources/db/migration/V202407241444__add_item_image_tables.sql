CREATE TABLE item_image
(
    id         UUID         NOT NULL PRIMARY KEY,
    item_id    UUID,
    url        TEXT,
    image_type VARCHAR(255),
    status     VARCHAR(255),
    sort       INTEGER,
    created_at TIMESTAMP    NOT NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at TIMESTAMP,
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    deleted_at TIMESTAMP,
    deleted_by VA<PERSON><PERSON>R(255)
);

create index item_image_item_id_idx on item_image (item_id);