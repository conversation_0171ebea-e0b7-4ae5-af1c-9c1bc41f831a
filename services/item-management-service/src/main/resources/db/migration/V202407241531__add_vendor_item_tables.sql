CREATE TABLE vendor_item
(
    id                   UUID         NOT NULL PRIMARY KEY,
    vendor_id            UUID,
    item_id              UUID,
    vendor_sku_number    VARCHAR(255),
    vendor_item_name     VARCHAR(255),
    note                 TEXT,
    status_change_reason TEXT,
    aisle                VARCHAR(255),
    lowest_cost          DECIMAL,
    highest_cost         DECIMAL,
    cost                 DECIMAL,
    secondary_cost       DECIMAL,
    status               VARCHAR(255),
    created_at           TIMESTAMP    NOT NULL,
    created_by           VARC<PERSON><PERSON>(255) NOT NULL,
    updated_at           TIMESTAMP,
    updated_by           VARCHAR(255),
    deleted_at           TIMESTAMP,
    deleted_by           VARCHAR(255)
);


create index vendor_item_vendor_id_idx on vendor_item (vendor_id);
create index vendor_item_item_id_idx on vendor_item (item_id);
create index vendor_item_vendor_sku_number_idx on vendor_item (vendor_sku_number);
