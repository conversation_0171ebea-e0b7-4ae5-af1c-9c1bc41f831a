comment on column vendor_item.vendor_id is 'The unique identifier for the vendor associated with the vendor.';

comment on column vendor_item.item_id is 'The unique identifier for the item linked to the item.';

comment on column vendor_item.vendor_sku_number is 'The SKU (Stock Keeping Unit) number assigned to the item by the vendor.';

comment on column vendor_item.vendor_item_name is 'The name of the item as provided by the vendor.';

comment on column vendor_item.note is 'Additional notes or comments about the vendor item.';

comment on column vendor_item.status_change_reason is 'The reason for the status change of the vendor item, providing context for modifications.';

comment on column vendor_item.aisle is 'The aisle location where the item is stored or displayed, as specified by the vendor.';

comment on column vendor_item.lowest_cost is 'The lowest recorded cost for the item from the vendor.';

comment on column vendor_item.highest_cost is 'The highest recorded cost for the item from the vendor.';

comment on column vendor_item.pack_plus_crv_cost is 'The cost of a pack including the California Recycling Fee (CRV).';

comment on column vendor_item.secondary_pack_plus_crv_cost is 'The cost of a secondary pack including the California Recycling Fee (CRV).';

comment on column vendor_item.status is 'The current status of the vendor item, such as active or inactive.';

comment on column vendor_item.pack_no_crv_cost is 'The cost of a pack excluding the California Recycling Fee (CRV).';

comment on column vendor_item.individual_cost is 'The cost of a single unit of the item.';

comment on column vendor_item.secondary_pack_no_crv_cost is 'The cost of a secondary pack excluding the California Recycling Fee (CRV).';

comment on column vendor_item.secondary_individual_cost is 'The cost of a single unit in a secondary packaging format.';