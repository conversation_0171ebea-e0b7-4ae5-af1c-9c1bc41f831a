package com.mercaso.ims.infrastructure.apitaskprocess.finale;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskType;
import com.mercaso.ims.infrastructure.apitaskprocess.AbstractApiTaskProcessor;
import com.mercaso.ims.infrastructure.apitaskprocess.payload.TaskRequestPayload;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiClient;
import com.mercaso.ims.infrastructure.external.finale.dto.UpdateSupplierItemRequestDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Processor for FINALE_UPDATE_VENDOR_ITEM tasks
 * Handles updating vendor item information in Finale API
 */
@Slf4j
@Component
public class FinaleUpdateVendorItemProcessor extends AbstractApiTaskProcessor<Void> {

    private final FinaleExternalApiClient finaleExternalApiClient;

    public FinaleUpdateVendorItemProcessor(ObjectMapper objectMapper,
                                           FinaleExternalApiClient finaleExternalApiClient) {
        super(objectMapper);
        this.finaleExternalApiClient = finaleExternalApiClient;
    }

    @Override
    public String getTaskType() {
        return TaskType.FINALE_UPDATE_VENDOR_ITEM.getType();
    }

    @Override
    public boolean canProcess(String taskType) {
        try {
            TaskType type = TaskType.fromTaskType(taskType);
            return TaskType.FINALE_UPDATE_VENDOR_ITEM.equals(type);
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    @Override
    public boolean needsResponse() {
        return false; // Update operations typically don't need response
    }

    @Override
    public Void executeTask(ApiTaskQueue task) throws JsonProcessingException {
        // Parse the request payload to get the update parameters
        TaskRequestPayload requestPayload = parseRequestPayload(task, TaskRequestPayload.class);

        // Extract parameters
        String skuNumber = extractParameter(requestPayload, "skuNumber", "sku");
        String status = extractParameter(requestPayload, "status");
        String updateSupplierItemRequestDtoStr = extractParameter(requestPayload, "updateSupplierItemRequestDto");

        log.info("Updating vendor item for SKU: {}, updateSupplierItemRequestDto: {}",
                skuNumber, updateSupplierItemRequestDtoStr);

        UpdateSupplierItemRequestDto updateSupplierItemRequestDto =
                objectMapper.readValue(updateSupplierItemRequestDtoStr, UpdateSupplierItemRequestDto.class);

        try {
            // Call the finale API to update vendor item
            // Context management prevents infinite loop
            finaleExternalApiClient.updateVendorItem(skuNumber, status, updateSupplierItemRequestDto);

            log.info("Successfully updated vendor item for SKU: {}", skuNumber);
            return null;

        } catch (Exception e) {
            log.error("Failed to update vendor item for SKU {}: {}", skuNumber, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void validateTask(ApiTaskQueue task) {
        super.validateTask(task);

        try {
            TaskRequestPayload requestPayload = parseRequestPayload(task, TaskRequestPayload.class);

            // Validate required parameters
            String skuNumber = extractParameter(requestPayload, "skuNumber", "sku");
            if (skuNumber == null || skuNumber.trim().isEmpty()) {
                throw new IllegalArgumentException("SKU number is required for update vendor item task");
            }

            String updateSupplierItemRequestDtoStr = extractParameter(requestPayload, "updateSupplierItemRequestDto");
            if (updateSupplierItemRequestDtoStr == null || updateSupplierItemRequestDtoStr.trim().isEmpty()) {
                throw new IllegalArgumentException("updateSupplierItemRequestDto is required for update vendor item task");
            }

        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid request payload for update vendor item task: " + e.getMessage());
        }
    }
}
