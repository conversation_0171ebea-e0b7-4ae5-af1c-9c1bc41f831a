package com.mercaso.ims.infrastructure.repository.attributegroup.jpa;

import com.mercaso.ims.infrastructure.repository.attributegroup.jpa.dataobject.AttributeGroupDo;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.UUID;

public interface AttributeGroupJpaDao extends JpaRepository<AttributeGroupDo, UUID> {

    List<AttributeGroupDo> findByCategoryId(UUID categoryId);
}
