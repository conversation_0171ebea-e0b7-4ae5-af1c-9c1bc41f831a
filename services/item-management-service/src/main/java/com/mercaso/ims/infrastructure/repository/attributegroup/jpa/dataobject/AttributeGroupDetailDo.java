package com.mercaso.ims.infrastructure.repository.attributegroup.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import com.mercaso.ims.infrastructure.repository.attribute.jpa.dataobject.AttributeDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "attribute_group_detail")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "update attribute_group_detail set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class AttributeGroupDetailDo extends BaseDo {

    @Column(name = "attribute_group_id")
    private UUID attributeGroupId;

    @Column(name = "sort_order")
    private Float sortOrder;

    @JoinColumn(name = "attribute_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private AttributeDo attributeDo;

    @Column(name = "required")
    private boolean required;


}