package com.mercaso.ims.infrastructure.repository.vendoritem;

import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.VendorItemJpaDao;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemDo;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.mapper.VendorItemDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class VendorItemRepositoryImpl implements VendorItemRepository {

    private final VendorItemDoMapper vendorItemDoMapper;

    private final VendorItemJpaDao vendorItemJpaDao;


    @Override
    public VendorItem save(VendorItem vendorItem) {
        VendorItemDo vendorItemDo = vendorItemDoMapper.domainToDo(vendorItem);
        vendorItemDo = vendorItemJpaDao.saveAndFlush(vendorItemDo);
        return vendorItemDoMapper.doToDomain(vendorItemDo);
    }

    @Override
    public VendorItem findById(UUID id) {
        VendorItemDo vendorItemDo = vendorItemJpaDao.findById(id).orElse(null);
        if (null == vendorItemDo) {
            return null;
        }
        return vendorItemDoMapper.doToDomain(vendorItemDo);
    }

    @Override
    public VendorItem update(VendorItem vendorItem) {
        VendorItemDo vendorItemDo = vendorItemJpaDao.findById(vendorItem.getId()).orElse(null);
        if (Objects.isNull(vendorItemDo)) {
            throw new ImsBusinessException(ErrorCodeEnums.VENDOR_ITEM_NOT_FOUND.getCode());
        }
        VendorItemDo vendorItemDoTarget = vendorItemDoMapper.domainToDo(vendorItem);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(vendorItemDoTarget, vendorItemDo, ignoreProperties.toArray(new String[0]));
        VendorItemDo result = vendorItemJpaDao.save(vendorItemDo);
        return vendorItemDoMapper.doToDomain(result);
    }

    @Override
    public VendorItem deleteById(UUID id) {
        VendorItemDo vendorItemDo = vendorItemJpaDao.findById(id).orElse(null);
        if (null == vendorItemDo) {
            return null;
        }
        vendorItemDo.setDeletedAt(Instant.now());
        vendorItemDo.setDeletedBy(SecurityUtil.getLoginUserId() == null ? "1" : SecurityUtil.getLoginUserId());
        vendorItemDo.setDeletedUserName(SecurityUtil.getUserName() == null ? "system" : SecurityUtil.getUserName());
        return vendorItemDoMapper.doToDomain(vendorItemJpaDao.save(vendorItemDo));
    }

    @Override
    public List<VendorItem> findByItemID(UUID itemID) {
        return Optional.ofNullable(vendorItemJpaDao.findByItemId(itemID))
            .orElse(Collections.emptyList())
            .stream()
            .map(vendorItemDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<VendorItem> findByVendorID(UUID vendorID) {
        return Optional.ofNullable(vendorItemJpaDao.findByVendorId(vendorID))
            .orElse(Collections.emptyList())
            .stream()
            .map(vendorItemDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public VendorItem findByVendorIDAndItemIdAndVendorSkuNum(UUID vendorID, UUID itemID, String vendorSkuNum) {
        VendorItemDo vendorItemDo = vendorItemJpaDao.findByVendorIdAndItemIdAndVendorSkuNumber(vendorID,
            itemID,
            vendorSkuNum);
        if (null == vendorItemDo) {
            return null;
        }
        return vendorItemDoMapper.doToDomain(vendorItemDo);
    }

    @Override
    public VendorItem findByVendorIDAndItemId(UUID vendorID, UUID itemID) {
        VendorItemDo vendorItemDo = vendorItemJpaDao.findByVendorIdAndItemId(vendorID, itemID);
        if (null == vendorItemDo) {
            return null;
        }
        return vendorItemDoMapper.doToDomain(vendorItemDo);
    }

    @Override
    public List<VendorItem> findByItemIdWithin(List<UUID> itemID) {
        return Optional.ofNullable(vendorItemJpaDao.findByItemIdIn(itemID))
            .orElse(Collections.emptyList())
            .stream()
            .map(vendorItemDoMapper::doToDomain)
            .toList();
    }

    @Override
    public List<VendorItem> findByVendorIDAndVendorSkuNum(UUID vendorID, String vendorSkuNum) {
        List<VendorItemDo> vendorItemDos = vendorItemJpaDao.findByVendorIdAndVendorSkuNumber(vendorID,
            vendorSkuNum);
        if (null == vendorItemDos || vendorItemDos.isEmpty()) {
            return Collections.emptyList();
        }
        return vendorItemDos.stream()
            .map(vendorItemDoMapper::doToDomain)
            .toList();
    }

    @Override
    public List<VendorItem> findByVendorIdAndItemIdIn(UUID vendorID, List<UUID> itemID) {
        List<VendorItemDo> vendorItemDos = vendorItemJpaDao.findByVendorIdAndItemIdIn(vendorID, itemID);
        if (null == vendorItemDos || vendorItemDos.isEmpty()) {
            return Collections.emptyList();
        }
        return vendorItemDos.stream()
            .map(vendorItemDoMapper::doToDomain)
            .toList();
    }
}
