package com.mercaso.ims.infrastructure.repository.bulkexportrecords;
import com.mercaso.ims.domain.bulkexportrecords.BulkExportRecords;
import com.mercaso.ims.domain.bulkexportrecords.BulkExportRecordsRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa.BulkExportRecordsJpaDao;
import com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa.dataobject.BulkExportRecordsDo;
import com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa.mapper.BulkExportRecordsDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class BulkExportRecordsRepositoryImpl implements BulkExportRecordsRepository {

    private final BulkExportRecordsDoMapper bulkExportRecordsDoMapper;
    private final BulkExportRecordsJpaDao bulkExportRecordsJpaDao;


    @Override
    public BulkExportRecords save(BulkExportRecords domain) {

        BulkExportRecordsDo bulkExportRecordsDo = bulkExportRecordsDoMapper.domainToDo(domain);
        bulkExportRecordsDo = bulkExportRecordsJpaDao.save(bulkExportRecordsDo);
        return bulkExportRecordsDoMapper.doToDomain(bulkExportRecordsDo);
    }

    @Override
    public BulkExportRecords findById(UUID id) {
        BulkExportRecordsDo bulkExportRecordsDo = bulkExportRecordsJpaDao.findById(id).orElse(null);
        if (null == bulkExportRecordsDo) {
            return null;
        }
        return bulkExportRecordsDoMapper.doToDomain(bulkExportRecordsDo);
    }

    @Override
    public BulkExportRecords update(BulkExportRecords domain) {

        BulkExportRecordsDo bulkExportRecordsDo = bulkExportRecordsJpaDao.findById(domain.getId()).orElse(null);
        if (null == bulkExportRecordsDo) {
            throw new ImsBusinessException(ErrorCodeEnums.BULK_EXPORT_RECORDS_NOT_FOUND.getCode());
        }
        BulkExportRecordsDo bulkExportRecordsDoTarget = bulkExportRecordsDoMapper.domainToDo(domain);

        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
            "createdAt"));
        BeanUtils.copyProperties(bulkExportRecordsDoTarget, bulkExportRecordsDo, ignoreProperties.toArray(new String[0]));

        bulkExportRecordsDo = bulkExportRecordsJpaDao.save(bulkExportRecordsDo);
        return bulkExportRecordsDoMapper.doToDomain(bulkExportRecordsDo);
    }

    @Override
    public BulkExportRecords deleteById(UUID id) {
        BulkExportRecordsDo bulkExportRecordsDo = bulkExportRecordsJpaDao.findById(id).orElse(null);
        if (null == bulkExportRecordsDo) {
            return null;
        }
        bulkExportRecordsDo.setDeletedAt(Instant.now());
        bulkExportRecordsDo.setDeletedBy(SecurityUtil.getLoginUserId());
        bulkExportRecordsDo = bulkExportRecordsJpaDao.save(bulkExportRecordsDo);
        return bulkExportRecordsDoMapper.doToDomain(bulkExportRecordsDo);
    }
}
