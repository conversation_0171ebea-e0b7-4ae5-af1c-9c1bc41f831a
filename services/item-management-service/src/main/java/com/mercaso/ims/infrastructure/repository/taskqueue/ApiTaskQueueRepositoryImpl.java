package com.mercaso.ims.infrastructure.repository.taskqueue;

import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;
import com.mercaso.ims.domain.taskqueue.repository.ApiTaskQueueRepository;
import com.mercaso.ims.infrastructure.repository.taskqueue.jpa.ApiTaskQueueJpaDao;
import com.mercaso.ims.infrastructure.repository.taskqueue.jpa.dataobject.ApiTaskQueueDo;
import com.mercaso.ims.infrastructure.repository.taskqueue.mapper.ApiTaskQueueDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Implementation of API Task Queue repository using JPA
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApiTaskQueueRepositoryImpl implements ApiTaskQueueRepository {

    private final ApiTaskQueueJpaDao apiTaskQueueJpaDao;
    private final ApiTaskQueueDoMapper apiTaskQueueDoMapper;

    @Override
    @Transactional
    public ApiTaskQueue save(ApiTaskQueue task) {
        ApiTaskQueueDo taskDo = apiTaskQueueDoMapper.domainToDo(task);
        ApiTaskQueueDo savedTaskDo = apiTaskQueueJpaDao.save(taskDo);
        return apiTaskQueueDoMapper.doToDomain(savedTaskDo);
    }

    @Override
    public Optional<ApiTaskQueue> findById(UUID id) {
        return apiTaskQueueJpaDao.findById(id)
                .map(apiTaskQueueDoMapper::doToDomain);
    }

    @Override
    public List<ApiTaskQueue> findExecutableTasks(List<TaskStatus> statuses, int limit) {
        // Get more tasks than needed to ensure proper ordering after filtering
        int fetchLimit = Math.max(limit * 2, 100);
        Pageable pageable = PageRequest.of(0, fetchLimit);
        Page<ApiTaskQueueDo> apiTaskQueueDos = apiTaskQueueJpaDao.findByStatusInOrderByPriorityDescCreatedAtAsc(statuses, pageable);

        List<ApiTaskQueueDo> filteredTaskDos = apiTaskQueueDos.getContent();

        return filteredTaskDos.stream()
                .map(apiTaskQueueDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<ApiTaskQueue> findByStatus(TaskStatus status) {
        List<ApiTaskQueueDo> taskDos = apiTaskQueueJpaDao.findByStatusOrderByCreatedAtAsc(status);
        return taskDos.stream()
                .map(apiTaskQueueDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<ApiTaskQueue> findByTaskTypeAndStatus(String taskType, TaskStatus status) {
        List<ApiTaskQueueDo> taskDos = apiTaskQueueJpaDao.findByTaskTypeAndStatusOrderByPriorityDescCreatedAtAsc(taskType, status);
        return taskDos.stream()
                .map(apiTaskQueueDoMapper::doToDomain)
                .toList();
    }

    @Override
    public long countByStatus(TaskStatus status) {
        return apiTaskQueueJpaDao.countByStatus(status);
    }

    @Override
    public long countByTaskTypeAndStatus(String taskType, TaskStatus status) {
        return apiTaskQueueJpaDao.countByTaskTypeAndStatus(taskType, status);
    }

    @Override
    public List<ApiTaskQueue> findStuckProcessingTasks(Instant cutoffTime) {
        List<ApiTaskQueueDo> taskDos = apiTaskQueueJpaDao.findByStatusAndStartedAtBefore(TaskStatus.PROCESSING, cutoffTime);
        return taskDos.stream()
                .map(apiTaskQueueDoMapper::doToDomain)
                .toList();
    }

    @Override
    @Transactional
    public int cleanupCompletedTasks(Instant cutoffTime) {
        // Use database filtering instead of loading all completed tasks into memory
        List<ApiTaskQueueDo> apiTaskQueueDos = apiTaskQueueJpaDao.findByStatusAndCompletedAtBefore(
                TaskStatus.COMPLETED, cutoffTime);

        AtomicInteger updatedCount = new AtomicInteger();

        apiTaskQueueDos.forEach(taskDo -> {
            taskDo.setDeletedAt(Instant.now());
            // Use proper system identifier from SecurityUtil instead of hardcoded "1"
            taskDo.setDeletedBy(SecurityUtil.getSystemUserId());
            taskDo.setDeletedUserName(SecurityUtil.getSystemUserName());
            apiTaskQueueJpaDao.save(taskDo);
            updatedCount.getAndIncrement();
        });

        return updatedCount.get();
    }

    @Override
    @Transactional
    public void delete(ApiTaskQueue task) {
        if (task.getId() != null) {
            apiTaskQueueJpaDao.deleteById(task.getId());
        }
    }

    @Override
    @Transactional
    public void deleteById(UUID id) {
        apiTaskQueueJpaDao.deleteById(id);
    }

    @Override
    public List<String> findDistinctTaskTypesByStatus(List<TaskStatus> statuses) {
        List<ApiTaskQueueDo> apiTaskQueueDos = apiTaskQueueJpaDao.findByStatusIn(statuses);
        return apiTaskQueueDos.stream()
                .map(ApiTaskQueueDo::getTaskType)
                .distinct()
                .toList();
    }
}
