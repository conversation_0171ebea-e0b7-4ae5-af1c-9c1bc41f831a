package com.mercaso.ims.infrastructure.repository.itemregprice;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_REG_PRICE_NOT_FOUND;

import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.ItemRegPriceRepository;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemregprice.jpa.ItemRegPriceJpaDao;
import com.mercaso.ims.infrastructure.repository.itemregprice.jpa.dataobject.ItemRegPriceDo;
import com.mercaso.ims.infrastructure.repository.itemregprice.jpa.mapper.ItemRegPriceDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ItemRegPriceRepositoryImpl implements ItemRegPriceRepository {

    private final ItemRegPriceJpaDao itemRegPriceJpaDao;

    private final ItemRegPriceDoMapper itemRegPriceDoMapper;

    @Override
    public ItemRegPrice save(ItemRegPrice domain) {
        ItemRegPriceDo itemRegPriceDo = itemRegPriceDoMapper.domainToDo(domain);
        itemRegPriceDo = itemRegPriceJpaDao.save(itemRegPriceDo);
        return itemRegPriceDoMapper.doToDomain(itemRegPriceDo);
    }

    @Override
    public ItemRegPrice findById(UUID id) {
        ItemRegPriceDo itemRegPriceDo = itemRegPriceJpaDao.findById(id).orElse(null);
        return itemRegPriceDoMapper.doToDomain(itemRegPriceDo);
    }

    @Override
    public ItemRegPrice update(ItemRegPrice domain) {
        ItemRegPriceDo itemRegPriceDo = itemRegPriceJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(itemRegPriceDo)) {
            throw new ImsBusinessException(ITEM_REG_PRICE_NOT_FOUND.getCode());
        }
        ItemRegPriceDo itemRegPriceDoTarget = itemRegPriceDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(itemRegPriceDoTarget, itemRegPriceDo, ignoreProperties.toArray(new String[0]));
        ItemRegPriceDo result = itemRegPriceJpaDao.save(itemRegPriceDo);
        return itemRegPriceDoMapper.doToDomain(result);
    }

    @Override
    public ItemRegPrice deleteById(UUID id) {
        ItemRegPriceDo itemRegPriceDo = itemRegPriceJpaDao.findById(id).orElse(null);
        if (null == itemRegPriceDo) {
            return null;
        }
        itemRegPriceDo.setDeletedAt(Instant.now());
        itemRegPriceDo.setDeletedBy(SecurityUtil.getLoginUserId());
        return itemRegPriceDoMapper.doToDomain(itemRegPriceJpaDao.save(itemRegPriceDo));
    }

    @Override
    public ItemRegPrice findByItemId(UUID itemId) {
        ItemRegPriceDo itemRegPriceDo = itemRegPriceJpaDao.findByItemId(itemId);
        if (null == itemRegPriceDo) {
            return null;
        }
        return itemRegPriceDoMapper.doToDomain(itemRegPriceDo);

    }

    @Override
    public List<ItemRegPrice> findByItemIds(List<UUID> itemIds) {
        return Optional.ofNullable(itemRegPriceJpaDao.findByItemIdIn(itemIds))
            .orElse(Collections.emptyList())
            .stream()
            .map(itemRegPriceDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<ItemRegPrice> findByItemPriceGroupId(UUID itemPriceGroupId) {
        return Optional.ofNullable(itemRegPriceJpaDao.findByItemPriceGroupId(itemPriceGroupId))
            .orElse(Collections.emptyList())
            .stream()
            .map(itemRegPriceDoMapper::doToDomain)
            .toList().reversed();
    }
}
