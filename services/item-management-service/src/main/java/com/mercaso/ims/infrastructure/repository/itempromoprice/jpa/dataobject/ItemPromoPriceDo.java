package com.mercaso.ims.infrastructure.repository.itempromoprice.jpa.dataobject;

import com.mercaso.ims.domain.itempromoprice.enums.ItemPromoPriceStatus;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;

@Entity
@Table(name = "item_promo_price")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_promo_price set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemPromoPriceDo extends BaseDo {

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "crv")
    private BigDecimal crv;

    @Column(name = "promo_price")
    private BigDecimal promoPrice;

    @Column(name = "promo_price_individual")
    private BigDecimal promoPriceIndividual;

    @Column(name = "promo_price_plus_crv")
    private BigDecimal promoPricePlusCrv;

    @Column(name = "promo_begin_time")
    private Instant promoBeginTime;

    @Column(name = "promo_end_time")
    private Instant promoEndTime;

    @Column(name = "promo_flag")
    private Boolean promoFlag;

    @Column(name = "promo_live_check")
    private String promoLiveCheck;

    @Column(name = "promo_pricing_validation")
    private String promoPricingValidation;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private ItemPromoPriceStatus itemPromoPriceStatus;

    @Column(name = "crv_flag")
    private Boolean crvFlag;

}
