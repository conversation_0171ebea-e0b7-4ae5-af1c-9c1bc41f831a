package com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord.jpa.dataobject;

import com.mercaso.ims.domain.vendorpoanalyzerecord.enums.AnalysisSource;
import com.mercaso.ims.domain.vendorpoanalyzerecord.enums.AnalysisStatus;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "vendor_po_analyze_record")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update vendor_po_analyze_record set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class VendorPoAnalyzeRecordDo extends BaseDo {

    @Column(name = "vendor_id")
    private UUID vendorId;

    @Column(name = "vendor_name")
    private String vendorName;

    @Column(name = "analysis_source")
    @Enumerated(EnumType.STRING)
    private AnalysisSource analysisSource;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private AnalysisStatus status;

    @Column(name = "original_file_name")
    private String originalFileName;

    @Column(name = "rcpt_id")
    private String rcptId;

    @Column(name = "rcpt_date")
    private String rcptDate;

    @Column(name = "item_cost_collection_id")
    private UUID itemCostCollectionId;
    

    @Column(columnDefinition = "analysis_expense_payload")
    @Type(JsonType.class)
    private String analysisExpensePayload;

}
