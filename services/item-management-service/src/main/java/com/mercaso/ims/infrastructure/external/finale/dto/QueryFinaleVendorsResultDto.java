package com.mercaso.ims.infrastructure.external.finale.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryFinaleVendorsResultDto {

    private List<String> partyId;
    private List<String> groupName;

    public List<FinaleVendorDto> getAllFinaleVendors() {
        if (partyId == null || groupName == null || partyId.size() != groupName.size()) {
            throw new IllegalArgumentException("partyIds and groupNames must have the same size and not be null.");
        }

        List<FinaleVendorDto> result = new ArrayList<>();
        for (int i = 0; i < partyId.size(); i++) {
            result.add(new FinaleVendorDto(partyId.get(i), groupName.get(i)));
        }
        return result;
    }

    public FinaleVendorDto getFinaleVendor(String vendorName) {
        if (partyId == null || groupName == null || partyId.size() != groupName.size()) {
            throw new IllegalArgumentException("partyIds and groupNames must have the same size and not be null.");
        }
        for (int i = 0; i < groupName.size(); i++) {
            if (vendorName.equals(groupName.get(i)) && null != partyId.get(i)) {
                return new FinaleVendorDto(partyId.get(i), groupName.get(i));
            }
        }
        return null;
    }
}
