package com.mercaso.ims.infrastructure.repository.attributegroup;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ATTRIBUTE_GROUP_NOT_FOUND;

import com.mercaso.ims.domain.attributegroup.AttributeGroup;
import com.mercaso.ims.domain.attributegroup.AttributeGroupRepository;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.attributegroup.jpa.AttributeGroupJpaDao;
import com.mercaso.ims.infrastructure.repository.attributegroup.jpa.dataobject.AttributeGroupDo;
import com.mercaso.ims.infrastructure.repository.attributegroup.jpa.mapper.AttributeGroupDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class AttributeGroupRepositoryImpl implements AttributeGroupRepository {

    public final AttributeGroupJpaDao attributeGroupJpaDao;
    public final AttributeGroupDoMapper attributeGroupDoMapper;

    @Override
    public AttributeGroup save(AttributeGroup domain) {
        AttributeGroupDo attributeGroupDo = attributeGroupDoMapper.domainToDo(domain);
        attributeGroupDo = attributeGroupJpaDao.save(attributeGroupDo);
        return attributeGroupDoMapper.doToDomain(attributeGroupDo);
    }

    @Override
    public AttributeGroup findById(UUID id) {
        return attributeGroupDoMapper.doToDomain(attributeGroupJpaDao.findById(id).orElse(null));
    }

    @Override
    public AttributeGroup update(AttributeGroup domain) {
        AttributeGroupDo attributeGroupDo = attributeGroupJpaDao.findById(domain.getId()).orElse(null);
        if (null == attributeGroupDo) {
            throw new ImsBusinessException(ATTRIBUTE_GROUP_NOT_FOUND);
        }
        AttributeGroupDo target = attributeGroupDoMapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
            "createdAt"));
        BeanUtils.copyProperties(target, attributeGroupDo, ignoreProperties.toArray(new String[0]));
        attributeGroupDo = attributeGroupJpaDao.save(attributeGroupDo);
        return attributeGroupDoMapper.doToDomain(attributeGroupDo);
    }

    @Override
    public AttributeGroup deleteById(UUID id) {
        AttributeGroupDo attributeGroupDo = attributeGroupJpaDao.findById(id).orElse(null);
        if (null == attributeGroupDo) {
            return null;
        }
        attributeGroupDo.setDeletedAt(Instant.now());
        attributeGroupDo.setDeletedBy(SecurityUtil.getLoginUserId());
        return attributeGroupDoMapper.doToDomain(attributeGroupDo);
    }

    @Override
    public List<AttributeGroup> findByCategoryId(UUID categoryId) {
        return Optional.ofNullable(attributeGroupJpaDao.findByCategoryId(categoryId))
            .orElse(Collections.emptyList())
            .stream()
            .map(attributeGroupDoMapper::doToDomain)
            .toList().reversed();
    }
}
