package com.mercaso.ims.infrastructure.repository.vendoritem.jpa;

import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemAvailabilitySnapshotDetailDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface VendorItemAvailabilitySnapshotDetailJpaDao extends JpaRepository<VendorItemAvailabilitySnapshotDetailDo, UUID> {

    List<VendorItemAvailabilitySnapshotDetailDo> findBySnapshotId(UUID snapshotId);

    List<VendorItemAvailabilitySnapshotDetailDo> findByVendorItemIdOrderByCreatedAtDesc(UUID vendorItemId);
} 