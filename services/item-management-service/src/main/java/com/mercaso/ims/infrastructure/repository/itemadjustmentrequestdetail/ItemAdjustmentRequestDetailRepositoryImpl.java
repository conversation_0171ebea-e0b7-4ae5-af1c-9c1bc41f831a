package com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail;

import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail.jpa.ItemAdjustmentRequestDetailJpaDao;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail.jpa.dataobject.ItemAdjustmentRequestDetailDo;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail.jpa.mapper.ItemAdjustmentRequestDetailDoMapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class ItemAdjustmentRequestDetailRepositoryImpl implements ItemAdjustmentRequestDetailRepository {

    private final ItemAdjustmentRequestDetailJpaDao jpaDao;

    private final ItemAdjustmentRequestDetailDoMapper requestDetailDoMapper;

    @Override
    public ItemAdjustmentRequestDetail save(ItemAdjustmentRequestDetail domain) {
        ItemAdjustmentRequestDetailDo requestDetailDo = requestDetailDoMapper.domainToDo(domain);
        requestDetailDo = jpaDao.save(requestDetailDo);
        return requestDetailDoMapper.doToDomain(requestDetailDo);
    }

    @Override
    public ItemAdjustmentRequestDetail findById(UUID id) {
        return requestDetailDoMapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public ItemAdjustmentRequestDetail update(ItemAdjustmentRequestDetail domain) {

        ItemAdjustmentRequestDetailDo requestDetailDo = jpaDao.findById(domain.getId()).orElse(null);
        if (null == requestDetailDo) {
            throw new ImsBusinessException(ErrorCodeEnums.ITEM_ADJUSTMENT_NOT_FOUND.getCode());
        }
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
            "createdAt"));

        ItemAdjustmentRequestDetailDo requestDoTarget = requestDetailDoMapper.domainToDo(domain);
        BeanUtils.copyProperties(requestDoTarget, requestDetailDo, ignoreProperties.toArray(new String[0]));

        requestDetailDo = jpaDao.save(requestDoTarget);
        return requestDetailDoMapper.doToDomain(requestDetailDo);
    }

    @Override
    public ItemAdjustmentRequestDetail deleteById(UUID id) {
        return null;
    }

    @Override
    public List<ItemAdjustmentRequestDetail> saveList(List<ItemAdjustmentRequestDetail> details) {
        List<ItemAdjustmentRequestDetailDo> detailDos = details.stream().map(requestDetailDoMapper::domainToDo).toList();
        detailDos = jpaDao.saveAll(detailDos);
        return detailDos.stream().map(requestDetailDoMapper::doToDomain).toList();
    }

    @Override
    public List<ItemAdjustmentRequestDetail> findByItemAdjustmentStatus(ItemAdjustmentStatus status) {
        List<ItemAdjustmentRequestDetailDo> detailDos = jpaDao.findAllByStatus(status);
        return detailDos.stream().map(requestDetailDoMapper::doToDomain).toList();
    }

    @Override
    public List<ItemAdjustmentRequestDetail> findByItemAdjustmentRequestId(UUID requestId) {
        List<ItemAdjustmentRequestDetailDo> detailDos = jpaDao.findAllByRequestId(requestId);
        return detailDos.stream().map(requestDetailDoMapper::doToDomain).toList();
    }

    @Override
    public List<ItemAdjustmentRequestDetail> findAllBySku(String skuNumber) {
        return Optional.ofNullable(jpaDao.findAllBySku(skuNumber))
            .orElse(Collections.emptyList()).stream().map(requestDetailDoMapper::doToDomain).toList();
    }
}
