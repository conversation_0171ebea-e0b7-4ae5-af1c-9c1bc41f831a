package com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.query.ItemAdjustmentRequestQuery;
import com.mercaso.ims.application.query.ItemQuery;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestType;
import com.mercaso.ims.infrastructure.util.DateUtils;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedItemAdjustmentRequestJpaDaoImpl implements CustomizedItemAdjustmentRequestJpaDao {

    private static final String COUNT_QUERY = """
               SELECT COUNT(DISTINCT iar.id)
                FROM item_adjustment_request iar
            WHERE iar.deleted_at IS NULL
        """;
    private static final String ITEM_ADJUSTMENT_QUERY = "SELECT * FROM item_adjustment_request iar WHERE 1=1";
    private final JdbcTemplate jdbcTemplate;

    @Override
    public List<ItemAdjustmentRequestDto> getItemAdjustmentRequestDto(ItemAdjustmentRequestQuery query) {
        StringBuilder queryStr = new StringBuilder(ITEM_ADJUSTMENT_QUERY);

        queryStr.append(this.buildCreateTimeCondition(query.getCreatedStartDate(), query.getCreatedEndDate()));
        queryStr.append(this.buildCreateUserNameCondition(query.getCreatedUserName()));
        queryStr.append(this.buildSortSql(query.getSort()));
        queryStr.append(query.offsetLimitSql());
        return jdbcTemplate.query(queryStr.toString(), new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                int index = 1;
                if (!ObjectUtils.isEmpty(query.getCreatedStartDate())) {
                    ps.setTimestamp(index++, Timestamp.from(query.getCreatedStartDate()));
                }
                if (!ObjectUtils.isEmpty(query.getCreatedEndDate())) {
                    ps.setTimestamp(index++, Timestamp.from(query.getCreatedEndDate()));
                }
                if (!ObjectUtils.isEmpty(query.getCreatedUserName())) {
                    ps.setString(index, "%" + query.getCreatedUserName() + "%");
                }
            }
        }, new RowMapper<ItemAdjustmentRequestDto>() {
            @Override
            public ItemAdjustmentRequestDto mapRow(ResultSet rs, int rowNum) throws SQLException {
                ItemAdjustmentRequestDto itemAdjustmentRequestDto = new ItemAdjustmentRequestDto();
                itemAdjustmentRequestDto.setId(UUID.fromString(rs.getString("id")));
                itemAdjustmentRequestDto.setRequestFile(rs.getString("request_file").replace("documents/", ""));
                itemAdjustmentRequestDto.setType(ItemAdjustmentRequestType.fromString(rs.getString("type")));
                itemAdjustmentRequestDto.setStatus(ItemAdjustmentRequestStatus.fromString(rs.getString("status")));
                itemAdjustmentRequestDto.setCreateSuccessRowCount(rs.getInt("create_success_row_count"));
                itemAdjustmentRequestDto.setModifySuccessRowCount(rs.getInt("modify_success_row_count"));
                itemAdjustmentRequestDto.setDeleteSuccessRowCount(rs.getInt("delete_success_row_count"));
                itemAdjustmentRequestDto.setCreateFailedRowCount(rs.getInt("create_failed_row_count"));
                itemAdjustmentRequestDto.setModifyFailedRowCount(rs.getInt("modify_failed_row_count"));
                itemAdjustmentRequestDto.setDeleteFailedRowCount(rs.getInt("delete_failed_row_count"));
                itemAdjustmentRequestDto.setCreatedAt(DateUtils.fromTimestamp(rs.getTimestamp("created_at")));
                itemAdjustmentRequestDto.setCreatedBy(rs.getString("created_by"));
                itemAdjustmentRequestDto.setCreatedUserName(rs.getString("created_user_name"));
                itemAdjustmentRequestDto.setUpdatedAt(DateUtils.fromTimestamp(rs.getTimestamp("updated_at")));
                itemAdjustmentRequestDto.setUpdatedBy(rs.getString("updated_by"));
                itemAdjustmentRequestDto.setUpdatedUserName(rs.getString("updated_user_name"));
                itemAdjustmentRequestDto.setFailureReason(rs.getString("failure_reason"));
                return itemAdjustmentRequestDto;
            }
        });
    }

    @Override
    public long countQuery(ItemAdjustmentRequestQuery query) {

        StringBuilder queryStr = new StringBuilder(COUNT_QUERY);

        queryStr.append(this.buildCreateTimeCondition(query.getCreatedStartDate(), query.getCreatedEndDate()));
        queryStr.append(this.buildCreateUserNameCondition(query.getCreatedUserName()));

        List<Long> count = jdbcTemplate.query(queryStr.toString(), new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                int index = 1;
                if (!ObjectUtils.isEmpty(query.getCreatedStartDate())) {
                    ps.setTimestamp(index++, Timestamp.from(query.getCreatedStartDate()));
                }
                if (!ObjectUtils.isEmpty(query.getCreatedEndDate())) {
                    ps.setTimestamp(index++, Timestamp.from(query.getCreatedEndDate()));
                }
                if (!ObjectUtils.isEmpty(query.getCreatedUserName())) {
                    ps.setString(index, "%" + query.getCreatedUserName() + "%");
                }
            }
        }, (rs, rowNum) -> rs.getLong(1));
        return count.getFirst();
    }

    private String buildCreateTimeCondition(Instant createdStartDate, Instant createdEndDate) {
        StringBuilder stringBuilder = new StringBuilder();
        if (!ObjectUtils.isEmpty(createdStartDate)) {
            stringBuilder.append(" AND iar.created_at > ? ");
        }
        if (!ObjectUtils.isEmpty(createdEndDate)) {
            stringBuilder.append(" AND iar.created_at < ? ");
        }
        return stringBuilder.toString();
    }

    private String buildCreateUserNameCondition(String createUserName) {
        if (StringUtils.isNotBlank(createUserName)) {
            return "  AND iar.created_user_name ilike ? ";
        }
        return StringUtils.EMPTY;
    }

    private String buildSortSql(ItemQuery.SortType sort) {
        String sql = StringUtils.EMPTY;
        if (sort == null || sort == ItemQuery.SortType.UPDATED_AT_DESC) {
            return " ORDER BY iar.created_at DESC ";
        }
        return sql;
    }
}
