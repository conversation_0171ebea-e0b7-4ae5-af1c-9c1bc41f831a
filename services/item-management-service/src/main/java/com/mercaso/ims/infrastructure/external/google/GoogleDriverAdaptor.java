package com.mercaso.ims.infrastructure.external.google;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.AbstractInputStreamContent;
import com.google.api.client.http.ByteArrayContent;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.drive.Drive;
import com.google.api.services.drive.DriveScopes;
import com.google.api.services.drive.model.File;
import com.google.api.services.drive.model.Permission;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GoogleDriverAdaptor {

    public static final String APPLICATION_NAME = "IMS MERCHANDISING REPORT";
    public static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    @Value("${googleDriver.shared_drive_id}")
    private String sharedDriveId;
    @Value("${googleDriver.credentials}")
    private String credentials;

    public void uploadFileToGoogleDriver(String fileName, byte[] fileContent) {
        try {

            Drive driveService = getDriveService();

            File fileMetadata = new File();
            fileMetadata.setName(fileName);
            fileMetadata.setParents(Collections.singletonList(sharedDriveId));

            AbstractInputStreamContent mediaContent = new ByteArrayContent("application/octet-stream", fileContent);

            File file = driveService.files().create(fileMetadata, mediaContent)
                .setFields("id, parents")
                .setSupportsAllDrives(true)
                .execute();
            log.info("[uploadFileToGoogleDriver] File ID: " + file.getId());

            setFilePermission(driveService, file.getId(), "mercaso.com");

        } catch (Exception e) {

            log.error("[uploadFileToGoogleDriver] An error occurred: " + e);
        }
    }

    private GoogleCredentials getGoogleCredentials() throws IOException {
        return GoogleCredentials.fromStream(new ByteArrayInputStream(credentials.getBytes()))
            .createScoped(Collections.singleton(DriveScopes.DRIVE_FILE));
    }

    private Drive getDriveService() throws GeneralSecurityException, IOException {
        GoogleCredentials credentialsParm = getGoogleCredentials();
        return new Drive.Builder(
            GoogleNetHttpTransport.newTrustedTransport(),
            JSON_FACTORY,
            new HttpCredentialsAdapter(credentialsParm)
        ).setApplicationName(APPLICATION_NAME)
            .build();
    }


    public void setFilePermission(Drive driveService, String fileId, String domain) throws IOException {
        Permission permission = new Permission();
        permission.setType("domain");
        permission.setRole("reader");
        permission.setDomain(domain);

        driveService.permissions().create(fileId, permission)
            .setSupportsAllDrives(true)
            .execute();
    }
}
