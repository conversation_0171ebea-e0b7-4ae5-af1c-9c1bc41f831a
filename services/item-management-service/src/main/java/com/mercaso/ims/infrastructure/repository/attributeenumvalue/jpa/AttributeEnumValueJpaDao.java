package com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa;

import com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.dataobject.AttributeEnumValueDo;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.UUID;

public interface AttributeEnumValueJpaDao extends JpaRepository<AttributeEnumValueDo, UUID> {

    List<AttributeEnumValueDo> findByAttributeId(UUID attributeId);
}
