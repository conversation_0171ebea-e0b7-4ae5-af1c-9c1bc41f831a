package com.mercaso.ims.infrastructure.external.finale.dto;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonPropertyOrder({"facilityUrl", "productUrl", "quantityOnHandVar"})
public class InventoryItemVarianceDto {

    private String facilityUrl;
    private String productUrl;
    private Long quantityOnHandVar;

}