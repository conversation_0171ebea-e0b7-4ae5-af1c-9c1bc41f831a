package com.mercaso.ims.infrastructure.external.shopify.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShopifyProductDto {

    @JsonProperty("product")
    private ProductDto product;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ProductDto {
        @JsonProperty("id")
        private Long id;

        @JsonProperty("title")
        private String title;

        @JsonProperty("body_html")
        private String bodyHtml;

        @JsonProperty("vendor")
        private String vendor;

        @JsonProperty("product_type")
        private String productType;

        @JsonProperty("status")
        private String status;

        @JsonProperty("handle")
        private String handle;

        @JsonProperty("published_at")
        private String publishedAt;

        @JsonProperty("template_suffix")
        private String templateSuffix;

        @JsonProperty("published_scope")
        private String publishedScope;

        @JsonProperty("tags")
        private String tags;

        @JsonProperty("images")
        private List<Image> images;

        @JsonProperty("image")
        private Image image;

        @JsonProperty("options")
        private List<Options> options;

        @JsonProperty("variants")
        private List<Variant> variants;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Variant {
        @JsonProperty("id")
        private Long id;

        @JsonProperty("title")
        private String title;

        @JsonProperty("price")
        private BigDecimal price;

        @JsonProperty("sku")
        private String sku;

        @JsonProperty("position")
        private Integer position;

        @JsonProperty("inventory_policy")
        private String inventoryPolicy;

        @JsonProperty("compare_at_price")
        private String compareAtPrice;


        @JsonProperty("fulfillment_service")
        private String fulfillmentService;

        @JsonProperty("inventory_management")
        private String inventoryManagement;

        @JsonProperty("option1")
        private String option1;

        @JsonProperty("option2")
        private String option2;

        @JsonProperty("option3")
        private String option3;

        @JsonProperty("taxable")
        private Boolean taxable;

        @JsonProperty("barcode")
        @JsonInclude(JsonInclude.Include.ALWAYS)
        private String barcode;

        @JsonProperty("grams")
        private Integer grams;

        @JsonProperty("weight")
        private Double weight;

        @JsonProperty("weight_unit")
        private String weightUnit;

        @JsonProperty("requires_shipping")
        private Boolean requiresShipping;

        @JsonProperty("inventory_item_id")
        private Long inventoryItemId;

        @JsonProperty("inventory_quantity")
        private Long inventoryQuantity;

        @JsonProperty("image_id")
        private Long imageId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Options {
        @JsonProperty("id")
        private Long id;

        @JsonProperty("product_id")
        private Long productId;

        @JsonProperty("name")
        private String name;

        @JsonProperty("position")
        private Integer position;

        @JsonProperty("values")
        private List<String> values;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Image {

        @JsonProperty("id")
        private Long id;

        @JsonProperty("alt")
        private String alt;

        @JsonProperty("position")
        private Integer position;

        @JsonProperty("product_id")
        private Long productId;

        @JsonProperty("admin_graphql_api_id")
        private String adminGraphqlApiId;

        @JsonProperty("width")
        private Double width;

        @JsonProperty("height")
        private Double height;

        @JsonProperty("src")
        private String src;

        @JsonProperty("variant_ids")
        private List<Long> variantIds;
    }

}
