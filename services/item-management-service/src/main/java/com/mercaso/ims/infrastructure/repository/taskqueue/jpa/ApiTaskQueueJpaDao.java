package com.mercaso.ims.infrastructure.repository.taskqueue.jpa;

import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;
import com.mercaso.ims.infrastructure.repository.taskqueue.jpa.dataobject.ApiTaskQueueDo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * JPA repository for API Task Queue operations
 */
public interface ApiTaskQueueJpaDao extends JpaRepository<ApiTaskQueueDo, UUID> {

    Page<ApiTaskQueueDo> findByStatusInOrderByPriorityDescCreatedAtAsc(List<TaskStatus> statuses, Pageable pageable);

    /**
     * Find tasks by status
     */
    List<ApiTaskQueueDo> findByStatusOrderByCreatedAtAsc(TaskStatus status);

    /**
     * Find tasks by task type and status
     */
    List<ApiTaskQueueDo> findByTaskTypeAndStatusOrderByPriorityDescCreatedAtAsc(String taskType, TaskStatus status);

    /**
     * Count tasks by status
     */
    long countByStatus(TaskStatus status);

    /**
     * Count tasks by task type and status
     */
    long countByTaskTypeAndStatus(String taskType, TaskStatus status);

    /**
     * Find tasks that are stuck in PROCESSING status for too long
     */
    List<ApiTaskQueueDo> findByStatusAndStartedAtBefore(TaskStatus status, Instant cutoffTime);

    /**
     * Find distinct task types by status
     */
    List<ApiTaskQueueDo> findByStatusIn(List<TaskStatus> statuses);

    /**
     * Find completed tasks older than the specified cutoff time
     * This method filters directly in the database for efficient cleanup operations
     */
    List<ApiTaskQueueDo> findByStatusAndCompletedAtBefore(TaskStatus status, Instant cutoffTime);
}
