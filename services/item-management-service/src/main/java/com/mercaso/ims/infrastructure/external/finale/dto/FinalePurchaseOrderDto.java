package com.mercaso.ims.infrastructure.external.finale.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FinalePurchaseOrderDto {

    private String orderId;
    private String orderUrl;
    private String orderTypeId;
    private String orderHistoryListUrl;
    private LocalDateTime lastUpdatedDate;
    private LocalDateTime createdDate;
    private String settlementTermId;
    private String fulfillmentId;
    private String actionUrlEdit;
    private String actionUrlCancel;
    private String reserveAllUrl;
    private LocalDateTime orderDate;
    private LocalDateTime receiveDate;
    private BigDecimal orderItemListTotal;
    private String statusId;
    private String destinationFacilityUrl;
    private List<FinalePurchaseOrderItemDto> orderItemList;
    private List<Object> contentList;
    private List<Object> userFieldDataList;
    private List<String> shipmentUrlList;
    private List<String> invoiceUrlList;
    private List<String> connectionRelationUrlList;


}
