package com.mercaso.ims.infrastructure.external.finale.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateSupplierItemRequestDto {

    @JsonProperty("productUrl")
    private String productUrl;
    @JsonProperty("productId")
    private String productId;
    @JsonProperty("supplierList")
    private List<FinaleSupplierItemDto> supplierList;

}