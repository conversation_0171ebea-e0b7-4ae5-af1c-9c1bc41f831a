package com.mercaso.ims.infrastructure.external.slack;

import static org.apache.hc.core5.http.HttpHeaders.CONTENT_TYPE;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.infrastructure.client.HttpClient;
import com.mercaso.ims.infrastructure.config.SlackConfig;
import com.mercaso.ims.infrastructure.external.slack.dto.SlackAlertDto;
import com.mercaso.ims.infrastructure.external.slack.enums.SlackAlertLevel;
import com.mercaso.ims.infrastructure.external.slack.enums.SlackAlertType;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import io.swagger.v3.oas.models.PathItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class SlackHook {

    private static final String APPLICATION_JSON = "application/json";
    private final HttpClient client;
    private final SlackConfig slackConfig;
    private final Environment environment;
    private final FeatureFlagsManager featureFlagsManager;


    public void sendSlackMessage(SlackAlertType type,
        SlackAlertLevel level,
        String message,
        String sku,
        String reason) {

        log.info("Sending slack message for type: {} with message: {} and sku: {} and reason: {} ",
            type,
            message,
            sku,
            reason);

        try {
            SlackAlertDto slackAlertDto = SlackAlertDto.builder()
                .message(message)
                .type(type.getMessage())
                .level(level.getCode())
                .sku(sku)
                .reason(reason)
                .env(getEnv())
                .userName(getUserName())
                .build();
            String jsonBody = SerializationUtils.serialize(slackAlertDto);
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create(jsonBody, mediaType);
            Request request = new Request.Builder()
                .url(slackConfig.getSlackHook())
                .method(PathItem.HttpMethod.POST.name(), body)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();

            log.info("sending slack alert message, request body: {}", jsonBody);
            client.execute(request);
        } catch (Exception e) {
            log.warn("Error sending slack message: {} ", e.getMessage(), e);
        }

    }

    private String getEnv() {
        if (environment.getActiveProfiles().length > 0) {
            return environment.getActiveProfiles()[0];
        }
        return "local";
    }

    private String getUserName() {
        return SecurityUtil.getUserName() == null ? "System" : SecurityUtil.getUserName();
    }

}
