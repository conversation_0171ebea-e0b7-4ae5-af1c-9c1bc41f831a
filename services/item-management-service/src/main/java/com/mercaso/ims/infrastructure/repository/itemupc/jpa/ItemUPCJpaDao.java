package com.mercaso.ims.infrastructure.repository.itemupc.jpa;

import com.mercaso.ims.domain.item.enums.ItemUpcType;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ItemUPCJpaDao extends JpaRepository<ItemUPCDo, UUID> {

    List<ItemUPCDo> findAllByUpcNumberAndItemUpcType(String upcNumber, ItemUpcType itemUpcType);

    List<ItemUPCDo> findAllByItemId(UUID itemId);

}
