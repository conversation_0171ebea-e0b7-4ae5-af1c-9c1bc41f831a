package com.mercaso.ims.infrastructure.external.finale;

import com.mercaso.data.client.api.FinaleProductControllerApi;
import com.mercaso.data.client.dto.CustomPageFinaleAvailableStockDto;
import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.data.client.dto.FinaleAvailableStockFilter;
import com.mercaso.data.client.dto.PageableObject;
import com.mercaso.ims.infrastructure.exception.ServiceRetryableException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.hc.client5.http.ConnectTimeoutException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;

@Slf4j
@Component
@AllArgsConstructor
public class FinaleAdaptor {

    private final FinaleProductControllerApi finaleProductControllerApi;

    @Retryable(retryFor = {ServiceRetryableException.class},
        maxAttempts = 2,
        backoff = @Backoff(multiplier = 2))
    public List<FinaleAvailableStockDto> getAllProducts() {
        log.info("Fetching all products from Finale");
        long begin = System.currentTimeMillis();
        List<FinaleAvailableStockDto> allProducts = Lists.newArrayList();
        int pageNumber = 0;
        boolean hasMoreProducts = true;
        try {
            do {

                PageableObject pageable = new PageableObject();
                pageable.setPageSize(1000);
                pageable.setPageNumber(pageNumber);

                FinaleAvailableStockFilter productFilter = new FinaleAvailableStockFilter();
                productFilter.pageable(pageable);

                CustomPageFinaleAvailableStockDto pageProductDto = finaleProductControllerApi.getAvailableStock(productFilter);
                if (pageProductDto != null && !Objects.requireNonNull(pageProductDto.getData()).isEmpty()) {
                    allProducts.addAll(pageProductDto.getData());
                    pageNumber++;
                } else {
                    hasMoreProducts = false;
                }
            } while (hasMoreProducts);
        } catch (Exception e) {

            Throwable cause = e.getCause();
            if (cause instanceof RestClientException) {
                Throwable rootCause = cause.getCause();

                if (rootCause instanceof SocketTimeoutException || rootCause instanceof ConnectTimeoutException) {
                    throw new ServiceRetryableException("Exception calling get products %s", e);
                }
            }
            throw e;
        }
        log.info("Fetching all products from Finale, took {} ms", System.currentTimeMillis() - begin);
        return allProducts;
    }


    @Retryable(retryFor = {ServiceRetryableException.class},
        maxAttempts = 2,
        backoff = @Backoff(multiplier = 2))
    public FinaleAvailableStockDto getProductBySku(String sku) {
        FinaleAvailableStockDto products = null;
        int pageNumber = 0;
        try {

            List<String> skus = new ArrayList<>();
            skus.add(sku);

            PageableObject pageable = new PageableObject();
            pageable.setPageSize(10);
            pageable.setPageNumber(pageNumber);

            FinaleAvailableStockFilter productFilter = new FinaleAvailableStockFilter();
            productFilter.pageable(pageable);
            productFilter.setSkus(skus);

            CustomPageFinaleAvailableStockDto pageProductDto = finaleProductControllerApi.getAvailableStock(productFilter);
            if (pageProductDto != null && !Objects.requireNonNull(pageProductDto.getData()).isEmpty()) {
                products = pageProductDto.getData().get(0);

            }

        } catch (Exception e) {
            Throwable cause = e.getCause();
            if (cause instanceof RestClientException) {
                Throwable rootCause = cause.getCause();

                if (rootCause instanceof SocketTimeoutException || rootCause instanceof ConnectTimeoutException) {
                    throw new ServiceRetryableException("Exception calling get products %s", e);
                }
            }

            throw e;
        }
        return products;
    }
}
