package com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa;

import com.mercaso.ims.application.dto.BulkExportRecordsSearchDto;
import com.mercaso.ims.application.query.BulkExportRecordsQuery;

public interface CustomizedBulkExportRecordsJpaDao {

    /**
     * Retrieves bulk export records based on the provided query parameters.
     *
     * @param bulkExportRecordsQuery the query parameters for filtering bulk export records
     * @return BulkExportRecordsSearchDto containing the list of records and total count
     */
    BulkExportRecordsSearchDto getBulkExportRecords(BulkExportRecordsQuery bulkExportRecordsQuery);

}
