package com.mercaso.ims.infrastructure.external.finale.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryFinalePurchaseOrderResultDto {

    private DataWrapper data;

    @Data
    public static class DataWrapper {

        private OrderViewConnection orderViewConnection;
    }

    @Data
    public static class OrderViewConnection {

        private Summary summary;
        private List<Edge> edges;
        private PageInfo pageInfo;
        private String strategy;
    }

    @Data
    public static class Summary {

        private Metrics metrics;
    }

    @Data
    public static class Metrics {

        private List<Integer> count;
    }

    @Data
    public static class Edge {

        private PurchaseOrderPageInfo node;

    }

    @Data
    public static class PurchaseOrderPageInfo {

        private String orderUrl;
        private String fulfillment;
        private String orderDateFormatted;
        private OrderDestination orderDestination;
        private String orderId;
        private PartySupplierGroupName partySupplierGroupName;
        private String receiveDateFormatted;
        private String shipmentsFormatted;
        private String statusIdFormatted;
        private String totalFormatted;


        public String getSupplierId() {
            return partySupplierGroupName.getPartyId();
        }

        public boolean isReceived() {
            return StringUtils.isNotBlank(shipmentsFormatted)
                && shipmentsFormatted.startsWith("Received ");
        }

        public boolean isJitPurchaseOrder() {
            return StringUtils.isNotBlank(orderId)
                && orderId.startsWith("J");
        }
    }

    @Data
    public static class OrderDestination {

        private String name;
    }

    @Data
    public static class PartySupplierGroupName {

        private String partyId;
        private String name;
    }

    @Data
    public static class PageInfo {

        private boolean hasNextPage;
        private String endCursor;
    }

    public List<PurchaseOrderPageInfo> getPurchaseOrderInfoList() {
        List<PurchaseOrderPageInfo> purchaseOrderInfos = new ArrayList<>();
        if (data != null && data.getOrderViewConnection() != null && data.getOrderViewConnection().getEdges() != null) {
            purchaseOrderInfos = data.getOrderViewConnection().getEdges().stream().map(Edge::getNode).toList();
        }
        return purchaseOrderInfos;
    }


}
