package com.mercaso.ims.infrastructure.repository.businessevent.jpa.dataobject;

import com.mercaso.ims.domain.businessevent.enums.EventTypeEnums;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Type;


@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "business_event")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "update business_event set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class BusinessEventDo extends BaseDo {

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private EventTypeEnums type;

    @Column(columnDefinition = "jsonb")
    @Type(JsonType.class)
    private String payload;

    @Column(name = "correlation_id")
    private String correlationId;
}
