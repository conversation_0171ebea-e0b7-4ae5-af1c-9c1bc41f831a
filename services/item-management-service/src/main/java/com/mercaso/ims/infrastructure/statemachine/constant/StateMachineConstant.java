package com.mercaso.ims.infrastructure.statemachine.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StateMachineConstant {

    public static final String ITEM_ADJUSTMENT_REQUEST_DETAIL_STATEMACHINE_NAME = "itemAdjustmentRequestDetailStateMachine";
    public static final String ITEM_ADJUSTMENT_REQUEST_DETAIL_STATEMACHINE_FACTORY_NAME = "itemAdjustmentRequestDetailStateMachineFactory";
    public static final String ITEM_ADJUSTMENT_REQUEST_DETAIL_STATEMACHINE_PERSISTER_NAME = "itemAdjustmentRequestDetailStateMachinePersister";
    public static final String ITEM_ADJUSTMENT_REQUEST_STATEMACHINE_NAME = "itemAdjustmentRequestStateMachine";
    public static final String ITEM_ADJUSTMENT_REQUEST_STATEMACHINE_FACTORY_NAME = "itemAdjustmentRequestStateMachineFactory";
    public static final String ITEM_ADJUSTMENT_REQUEST_STATEMACHINE_PERSISTER_NAME = "itemAdjustmentRequestStateMachinePersister";

}
