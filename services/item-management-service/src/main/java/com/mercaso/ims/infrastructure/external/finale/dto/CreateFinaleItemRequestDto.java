package com.mercaso.ims.infrastructure.external.finale.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateFinaleItemRequestDto {

    @JsonProperty("productId")
    private String productId;

    @JsonProperty("statusId")
    private String statusId;


}