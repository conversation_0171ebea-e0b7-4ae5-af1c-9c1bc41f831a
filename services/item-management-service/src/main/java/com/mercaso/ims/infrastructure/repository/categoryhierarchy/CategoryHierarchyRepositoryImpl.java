package com.mercaso.ims.infrastructure.repository.categoryhierarchy;

import com.mercaso.ims.domain.categoryhierarchy.CategoryHierarchy;
import com.mercaso.ims.domain.categoryhierarchy.CategoryHierarchyRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.categoryhierarchy.jpa.CategoryHierarchyJpaDao;
import com.mercaso.ims.infrastructure.repository.categoryhierarchy.jpa.dataobject.CategoryHierarchyDo;
import com.mercaso.ims.infrastructure.repository.categoryhierarchy.jpa.mapper.CategoryHierarchyDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class CategoryHierarchyRepositoryImpl implements CategoryHierarchyRepository {

    private final CategoryHierarchyJpaDao categoryHierarchyJpaDao;
    private final CategoryHierarchyDoMapper categoryHierarchyDoMapper;


    @Override
    public CategoryHierarchy save(CategoryHierarchy domain) {
        CategoryHierarchyDo categoryHierarchyDo = categoryHierarchyDoMapper.domainToDo(domain);
        categoryHierarchyDo = categoryHierarchyJpaDao.save(categoryHierarchyDo);
        return categoryHierarchyDoMapper.doToDomain(categoryHierarchyDo);
    }

    @Override
    public CategoryHierarchy findById(UUID id) {
        return categoryHierarchyDoMapper.doToDomain(categoryHierarchyJpaDao.findById(id).orElse(null));
    }

    @Override
    public CategoryHierarchy update(CategoryHierarchy domain) {
        CategoryHierarchyDo categoryHierarchyDo = categoryHierarchyJpaDao.findById(domain.getId()).orElse(null);
        if (null == categoryHierarchyDo) {
            throw new ImsBusinessException(ErrorCodeEnums.CATEGORY_NOT_FOUND.getCode());
        }

        CategoryHierarchyDo categoryDoTarget = categoryHierarchyDoMapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
            "createdAt"));
        BeanUtils.copyProperties(categoryDoTarget, categoryHierarchyDo, ignoreProperties.toArray(new String[0]));
        categoryHierarchyDo = categoryHierarchyJpaDao.save(categoryDoTarget);
        return categoryHierarchyDoMapper.doToDomain(categoryHierarchyDo);
    }

    @Override
    public CategoryHierarchy deleteById(UUID id) {
        CategoryHierarchyDo categoryHierarchyDo = categoryHierarchyJpaDao.findById(id).orElse(null);
        if (null == categoryHierarchyDo) {
            return null;
        }
        categoryHierarchyDo.setDeletedAt(Instant.now());
        categoryHierarchyDo.setDeletedBy(SecurityUtil.getLoginUserId());
        categoryHierarchyDo = categoryHierarchyJpaDao.save(categoryHierarchyDo);
        return categoryHierarchyDoMapper.doToDomain(categoryHierarchyDo);
    }

    @Override
    public List<CategoryHierarchy> findByCategoryId(UUID categoryId) {
        List<CategoryHierarchyDo> categoryHierarchyDos = categoryHierarchyJpaDao.findByCategoryIdAndDeletedAtIsNullOrderByDepthDesc(categoryId);
        if (null == categoryHierarchyDos) {
            return List.of();
        }
        return categoryHierarchyDos.stream().map(categoryHierarchyDoMapper::doToDomain).toList();
    }

    @Override
    public List<CategoryHierarchy> findByCategoryIdIn(List<UUID> categoryIds) {
        List<CategoryHierarchyDo> categoryHierarchyDos = categoryHierarchyJpaDao.findByCategoryIdInAndDeletedAtIsNullOrderByDepthDesc(categoryIds);
        if (null == categoryHierarchyDos) {
            return List.of();
        }
        return categoryHierarchyDos.stream().map(categoryHierarchyDoMapper::doToDomain).toList();
    }

    @Override
    public List<CategoryHierarchy> findByAncestorCategoryId(UUID ancestorCategoryId) {
        List<CategoryHierarchyDo> categoryHierarchyDos = categoryHierarchyJpaDao.findByAncestorCategoryIdAndDeletedAtIsNullOrderByDepthDesc(ancestorCategoryId);
        if (null == categoryHierarchyDos) {
            return List.of();
        }
        return categoryHierarchyDos.stream().map(categoryHierarchyDoMapper::doToDomain).toList();
    }

    @Override
    public List<CategoryHierarchy> findByDepth(Integer depth) {
        List<CategoryHierarchyDo> categoryHierarchyDos = categoryHierarchyJpaDao.findByDepthAndDeletedAtIsNull(depth);
        if (null == categoryHierarchyDos) {
            return List.of();
        }
        return categoryHierarchyDos.stream().map(categoryHierarchyDoMapper::doToDomain).toList();
    }

    @Override
    public CategoryHierarchy findByCategoryIdAndAncestorCategoryId(UUID categoryId, UUID ancestorCategoryId) {
        CategoryHierarchyDo categoryHierarchyDo = categoryHierarchyJpaDao.findByCategoryIdAndAncestorCategoryIdAndDeletedAtIsNull(
            categoryId,
            ancestorCategoryId);
        if (null == categoryHierarchyDo) {
            return null;
        }
        return categoryHierarchyDoMapper.doToDomain(categoryHierarchyDo);
    }

    @Override
    public List<CategoryHierarchy> findByAncestorCategoryIdAndDepth(UUID ancestorCategoryId, Integer depth) {
        List<CategoryHierarchyDo> categoryHierarchyDos = categoryHierarchyJpaDao.findByAncestorCategoryIdAndDepthAndDeletedAtIsNull(ancestorCategoryId, depth);
        if (null == categoryHierarchyDos) {
            return List.of();
        }
        return categoryHierarchyDos.stream().map(categoryHierarchyDoMapper::doToDomain).toList();
    }

    @Override
    public List<CategoryHierarchy> findByCategoryIdAndDepth(UUID categoryId, Integer depth) {
        List<CategoryHierarchyDo> categoryHierarchyDos = categoryHierarchyJpaDao.findByCategoryIdAndDepthAndDeletedAtIsNull(categoryId, depth);
        if (null == categoryHierarchyDos) {
            return List.of();
        }
        return categoryHierarchyDos.stream().map(categoryHierarchyDoMapper::doToDomain).toList();
    }
}
