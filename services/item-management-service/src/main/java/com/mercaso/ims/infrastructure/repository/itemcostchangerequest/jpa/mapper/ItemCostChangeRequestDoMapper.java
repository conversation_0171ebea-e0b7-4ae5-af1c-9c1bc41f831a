package com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa.mapper;

import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequest;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa.dataobject.ItemCostChangeRequestDo;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ItemCostChangeRequestDoMapper extends BaseDoMapper<ItemCostChangeRequestDo, ItemCostChangeRequest> {

}
