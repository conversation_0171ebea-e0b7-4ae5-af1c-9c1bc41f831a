package com.mercaso.ims.infrastructure.excel.listener;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.CreateItemVendorRebateRequestData;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.mercaso.ims.infrastructure.util.FormatUtils.cleanInput;

@Slf4j
public class CreateItemVendorRebateRequestDataListener extends ItemAdjustmentRequestDataListener<CreateItemVendorRebateRequestData> {

    public CreateItemVendorRebateRequestDataListener(UUID itemAdjustmentRequestId,
                                                     ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService,
                                                     ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService,
                                                     ItemRepository itemRepository,
                                                     VendorRepository vendorRepository,
                                                     VendorItemRepository vendorItemRepository,
                                                     CategoryApplicationService categoryApplicationService,
                                                     FeatureFlagsManager featureFlagsManager, BrandRepository brandRepository) {
        super(itemAdjustmentRequestId,
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager, brandRepository);
    }

    @Override
    CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(CreateItemVendorRebateRequestData createItemVendorRebateRequestData) {
        return CreateItemAdjustmentRequestDetailCommand.builder()
            .requestId(itemAdjustmentRequestId)
            .type(getItemAdjustmentType())
            .sku(createItemVendorRebateRequestData.getSku())
            .vendor(createItemVendorRebateRequestData.getVendor())
            .rebateStartDate(createItemVendorRebateRequestData.getStartDate())
            .rebateEndDate(createItemVendorRebateRequestData.getEndDate())
            .rebatePerSellingUnit(createItemVendorRebateRequestData.getRebatePerSellingUnit())
            .build();
    }


    @Override
    ItemAdjustmentType getItemAdjustmentType() {
        return ItemAdjustmentType.CREATE_REBATE;
    }

    @Override
    List<ItemAdjustmentFailureReason> validateInput(CreateItemVendorRebateRequestData createItemVendorRebateRequestData) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();
        String skuNumber = cleanInput(createItemVendorRebateRequestData.getSku());

        Item item = itemRepository.findBySku(skuNumber);
        if (item == null) {
            failureReasons.add(ItemAdjustmentFailureReason.ITEM_NOT_FOUND);
            return failureReasons;
        }
        if (StringUtils.isBlank(createItemVendorRebateRequestData.getVendor())) {
            failureReasons.add(ItemAdjustmentFailureReason.VENDOR_NAME_IS_REQUIRED);
            return failureReasons;
        }

        String vendorName = createItemVendorRebateRequestData.getVendor();
        ItemAdjustmentFailureReason validateVendorResult = validateVendor(vendorName);
        if (validateVendorResult != null) {
            failureReasons.add(validateVendorResult);
            return failureReasons;
        }

        Vendor vendor = vendorRepository.findByVendorName(vendorName);

        if (Boolean.TRUE.equals(vendor.getExternalPicking())) {
            failureReasons.add(ItemAdjustmentFailureReason.SUPPLIER_MUST_BE_DIRECT);
        }

        ItemAdjustmentFailureReason validateVendorItemResult = validateVendorItem(item.getId(), vendor.getId());
        if (validateVendorItemResult != null) {
            failureReasons.add(validateVendorItemResult);
        }

        if (StringUtils.isBlank(createItemVendorRebateRequestData.getStartDate())) {
            failureReasons.add(ItemAdjustmentFailureReason.REBATE_START_DATE_IS_REQUIRED);
        }

        if (StringUtils.isBlank(createItemVendorRebateRequestData.getRebatePerSellingUnit())) {
            failureReasons.add(ItemAdjustmentFailureReason.REBATE_PER_SELLING_UNIT_IS_REQUIRED);
        }

        String rebatePerSellingUnit = createItemVendorRebateRequestData.getRebatePerSellingUnit();
        if (!NumberUtils.isCreatable(rebatePerSellingUnit)) {
            failureReasons.add(ItemAdjustmentFailureReason.REBATE_PER_SELLING_UNIT_FORMAT_ERROR);
        }

        return failureReasons;

    }
}
