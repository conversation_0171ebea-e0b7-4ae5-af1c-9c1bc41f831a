package com.mercaso.ims.infrastructure.repository.itempromoprice.jpa;

import com.mercaso.ims.infrastructure.repository.itempromoprice.jpa.dataobject.ItemPromoPriceDo;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ItemPromoPriceJpaDao extends JpaRepository<ItemPromoPriceDo, UUID> {

    List<ItemPromoPriceDo> findByItemId(UUID itemId);

    List<ItemPromoPriceDo> findByItemIdIn(List<UUID> itemIds);

    List<ItemPromoPriceDo> findByPromoBeginTimeBetween(Instant start, Instant end);

    List<ItemPromoPriceDo> findByPromoEndTimeBetween(Instant start, Instant end);
}
