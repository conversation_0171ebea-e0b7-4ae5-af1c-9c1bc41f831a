package com.mercaso.ims.infrastructure.repository.item.jpa;

import com.mercaso.ims.application.dto.CategoryItemCountsDto;
import com.mercaso.ims.application.dto.ItemAttributeDto;
import com.mercaso.ims.application.dto.ItemCategoryDto;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.query.ItemQuery;
import java.util.List;
import java.util.UUID;

public interface CustomizedItemJpaDao {

    List<UUID> getItemIdListV2(ItemQuery itemQuery);

    List<ItemSerachDto> getItemDtoList(ItemQuery itemQuery);

    long countQueryV2(ItemQuery query);

    List<ItemCategoryDto> findItemCategoryByIdIn(List<UUID> ids);

    List<ItemCategoryDto> findItemCategoryBySkuIn(List<String> skus);

    List<CategoryItemCountsDto> countItemsByCategoryIdAndStatus(List<UUID> categoryIds);

    List<ItemAttributeDto> findItemAttributeDtoByItemId(UUID itemId);

}
