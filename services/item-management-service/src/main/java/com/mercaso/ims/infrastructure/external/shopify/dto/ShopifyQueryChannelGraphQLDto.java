package com.mercaso.ims.infrastructure.external.shopify.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class ShopifyQueryChannelGraphQLDto {

    private Long shopifyProductId;


    public String buildQueryChannelQraphQL() {
        String query = "{"
            + "    \"query\": \"query publications($id: ID!) { product(id: $id) { resourcePublicationsV2(first: 10) { edges { node { isPublished publication {id name }} } } } }\","
            + "    \"variables\": {"
            + "        \"id\": \"gid://shopify/Product/" + shopifyProductId + "\""
            + "    }"
            + "}";

        log.info("[buildQueryChannelQraphQL] query: {}", query);

        return query;
    }

}
