package com.mercaso.ims.infrastructure.external.aws_ocr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mercaso.ims.application.dto.BaseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnalyzeLineItemDto extends BaseDto {

    private String vendorSkuNumber;

    private String upcNumber;

    private String newDescription;

    private String quantity;

    private String cost;

    private String totalCost;


}
