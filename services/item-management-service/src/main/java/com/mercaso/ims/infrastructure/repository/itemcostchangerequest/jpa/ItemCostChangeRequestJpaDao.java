package com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa;

import com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa.dataobject.ItemCostChangeRequestDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ItemCostChangeRequestJpaDao extends JpaRepository<ItemCostChangeRequestDo, UUID> {

    List<ItemCostChangeRequestDo> findByItemCostCollectionId(UUID itemCostCollectionId);

    List<ItemCostChangeRequestDo> findByItemCostCollectionIdIn(List<UUID> itemCostCollectionIds);
}
