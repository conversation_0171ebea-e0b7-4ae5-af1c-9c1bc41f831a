package com.mercaso.ims.infrastructure.repository.brand.jpa;

import com.mercaso.ims.infrastructure.repository.brand.jpa.dataobject.BrandDo;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.UUID;

public interface BrandJpaDao extends JpaRepository<BrandDo, UUID> {
    BrandDo findByName(String name);

    List<BrandDo> findByIdIn(List<UUID> ids);

    List<BrandDo> findByNameContainsIgnoreCase(String name);
    
    List<BrandDo> findByNameIgnoreCase(String name);
}