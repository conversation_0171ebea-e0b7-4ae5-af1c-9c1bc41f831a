package com.mercaso.ims.infrastructure.repository.item.jpa.mapper;

import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.itemattribute.jpa.dataobject.ItemAttributeDo;
import com.mercaso.ims.infrastructure.repository.itemattribute.jpa.mapper.ItemAttributeDoMapper;
import com.mercaso.ims.infrastructure.repository.itemimage.jpa.dataobject.ItemImageDo;
import com.mercaso.ims.infrastructure.repository.itemimage.jpa.mapper.ItemImageDoMapper;
import com.mercaso.ims.infrastructure.repository.itemtag.jpa.dataobject.ItemTagDo;
import com.mercaso.ims.infrastructure.repository.itemtag.jpa.mapper.ItemTagDoMapper;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.mapper.ItemUPCDoMapper;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring",
    uses = {ItemAttributeDoMapper.class,
        ItemTagDoMapper.class,
        ItemImageDoMapper.class,
        ItemUPCDoMapper.class})
public interface ItemDoMapper extends BaseDoMapper<ItemDo, Item> {

    @Override
    @Mapping(target = "itemImages", source = "itemImages")
    @Mapping(target = "itemAttributes", source = "itemAttributes")
    @Mapping(target = "itemTags", source = "itemTags")
    @Mapping(target = "itemUPCs", source = "itemUPCs")
    @Mapping(target = "itemGrade", ignore = true)
    Item doToDomain(ItemDo itemDo);

    @Override
    @Mapping(target = "itemImages", source = "itemImages")
    @Mapping(target = "itemAttributes", source = "itemAttributes")
    @Mapping(target = "itemTags", source = "itemTags")
    @Mapping(target = "itemUPCs", source = "itemUPCs")
    ItemDo domainToDo(Item item);

    @AfterMapping
    default void setItemDoAfter(@MappingTarget ItemDo itemDo, Item item) {
        if (item.getItemImages() != null) {
            List<ItemImageDo> itemImages = item.getItemImages().stream()
                .map(ItemImageDoMapper.INSTANCE::domainToDo).toList();
            itemDo.getItemImages().clear();
            itemDo.getItemImages().addAll(itemImages);
        }

        if (item.getItemAttributes() != null) {
            List<ItemAttributeDo> itemAttributes = item.getItemAttributes().stream()
                .map(ItemAttributeDoMapper.INSTANCE::domainToDo).toList();
            itemDo.getItemAttributes().clear();
            itemDo.getItemAttributes().addAll(itemAttributes);
        }

        if (item.getItemTags() != null) {
            List<ItemTagDo> itemTags = item.getItemTags().stream()
                .map(ItemTagDoMapper.INSTANCE::domainToDo).toList();
            itemDo.getItemTags().clear();
            itemDo.getItemTags().addAll(itemTags);
        }

        if (item.getItemUPCs() != null) {
            List<ItemUPCDo> itemUPCs = item.getItemUPCs().stream()
                .map(ItemUPCDoMapper.INSTANCE::domainToDo).toList();
            itemDo.getItemUPCs().clear();
            itemDo.getItemUPCs().addAll(itemUPCs);
        }

    }
}
