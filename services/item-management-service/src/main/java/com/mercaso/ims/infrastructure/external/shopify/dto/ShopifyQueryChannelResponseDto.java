package com.mercaso.ims.infrastructure.external.shopify.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import java.util.List;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShopifyQueryChannelResponseDto {
    @JsonProperty("data")
    private DataNode data;

    @Data
    public static class DataNode {
        @JsonProperty("publications")
        private Publications publications;
    }

    @Data
    public static class Publications {
        @JsonProperty("edges")
        private List<Edge> edges;
    }

    @Data
    public static class Edge {
        @JsonProperty("node")
        private Node node;
    }

    @Data
    public static class Node {
        @JsonProperty("id")
        private String id;
        @JsonProperty("name")
        private String name;
    }

}