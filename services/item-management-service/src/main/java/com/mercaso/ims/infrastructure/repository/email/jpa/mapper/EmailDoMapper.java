package com.mercaso.ims.infrastructure.repository.email.jpa.mapper;

import com.mercaso.ims.domain.BaseValueObjectDoMapper;
import com.mercaso.ims.domain.email.Email;
import com.mercaso.ims.infrastructure.repository.email.jpa.dataobject.EmailDo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface EmailDoMapper extends BaseValueObjectDoMapper<EmailDo, Email> {

    EmailDoMapper INSTANCE = Mappers.getMapper(EmailDoMapper.class);

    @Override
    Email doToDomain(EmailDo emailDo);

    @Override
    EmailDo domainToDo(Email email);
}
