package com.mercaso.ims.infrastructure.external.shopify;


import static com.mercaso.ims.domain.item.enums.AvailabilityStatus.DRAFT;

import com.google.api.client.util.Lists;
import com.mercaso.ims.application.dto.ItemAttributeDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemImageDto;
import com.mercaso.ims.application.dto.ItemUPCDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.domain.category.CategoryConstant;
import com.mercaso.ims.domain.item.enums.ImageType;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import com.mercaso.ims.infrastructure.external.shopify.dto.InventoryItemDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.InventoryItemInfoDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.MetafieldDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.MetafieldInfoDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyGraphQLQueryResponseDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyGraphQLQueryResponseDto.ImageEdgeDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyGraphQLQueryResponseDto.ProductEdgeDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyGraphQLQueryResponseDto.VariantEdgeDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto.Image;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto.Options;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto.ProductDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.ShopifyProductDto.Variant;
import com.mercaso.ims.infrastructure.external.shopify.dto.VariantImageDto;
import com.mercaso.ims.infrastructure.external.shopify.dto.VolumeMetafieldDto;
import com.mercaso.ims.infrastructure.external.shopify.enums.VariantType;
import com.mercaso.ims.infrastructure.external.shopify.enums.VolumeUnit;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import com.mercaso.ims.infrastructure.util.VolumeUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ShopifyRequestDtoFactory {

    private static final String NUMBER_DECIMAL = "number_decimal";
    private static final String NUMBER_INTEGER = "number_integer";
    private static final String SINGLE_LINE_TEXT_FIELD = "single_line_text_field";
    private static final String VOLUME = "volume";
    private static final String SHOPIFY_CUSTOM_NAMESPACE = "custom";
    private static final String SHOPIFY_ATTRIBUTES_NAMESPACE = "attributes";
    private static final String SHOPIFY_PRICING_NAMESPACE = "pricing";

    private ShopifyRequestDtoFactory() {
        throw new IllegalStateException("Factory class");
    }

    public static ShopifyProductDto buildShopifyProductDto(ItemDto itemDto,
        ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto) {

        ShopifyProductDto shopifyProductDto = new ShopifyProductDto();

        List<String> variantTypes = setVariantTypes(itemDto);

        List<Variant> variants = new ArrayList<>();
        Long productId = null;
        Long variantId = null;
        String tags = Optional.ofNullable(itemDto.getItemTags()).orElse(Collections.emptyList()).stream()
            .map(i -> i.getTagName().replace(",", ";"))
            .filter(tagName -> tagName != null && !tagName.isEmpty())
            .collect(Collectors.joining(","));

        if (null == shopifyGraphQLQueryResponseDto || CollectionUtils.isEmpty(shopifyGraphQLQueryResponseDto.getData()
            .getProducts()
            .getEdges())) {
            for (String variantType : variantTypes) {
                variants.add(buildVariant(variantType, itemDto, variantId));
            }

        } else {
            List<ProductEdgeDto> edges = shopifyGraphQLQueryResponseDto.getData().getProducts().getEdges();

            ShopifyGraphQLQueryResponseDto.ProductDto node = edges.stream()
                .map(ProductEdgeDto::getNode)
                .filter(product -> product.getVariants().getEdges().stream()
                    .map(VariantEdgeDto::getNode)
                    .anyMatch(variant -> itemDto.getSkuNumber().equals(variant.getSku()))).findFirst().orElse(null);

            if (null == node) {
                log.error("Product not found for item with skuNumber: {}", itemDto.getSkuNumber());
                return shopifyProductDto;
            }

            productId = extractId(node.getId());

            for (VariantEdgeDto variantEdgeDto : node.getVariants().getEdges()) {
                variantId = extractId(variantEdgeDto.getNode().getId());
                String title = variantEdgeDto.getNode().getTitle();
                variants.add(buildVariant(title, itemDto, variantId));
            }
        }

        ProductDto productDto = buildProductDto(itemDto,
            productId,
            tags,
            variantTypes,
            variants);

        shopifyProductDto.setProduct(productDto);

        return shopifyProductDto;
    }

    private static List<String> setVariantTypes(ItemDto itemDto) {
        List<String> variantTypes = Lists.newArrayList();

        variantTypes.add(VariantType.INDIVIDUAL.getType());
        variantTypes.add(VariantType.PACK_NO_CRV.getType());

        if (CategoryConstant.BEVERAGE.equalsIgnoreCase(itemDto.getDepartment())) {
            variantTypes.add(VariantType.PACK_PLUS_CRV.getType());
        } else {
            variantTypes.add(VariantType.PACK.getType());
        }
        return variantTypes;
    }

    private static ProductDto buildProductDto(ItemDto itemDto,
        Long productId, String tags, List<String> variantTypes, List<Variant> variants) {
        return ProductDto.builder()
            .id(productId)
            .title(itemDto.getTitle())
            .bodyHtml(itemDto.getBodyHtml())
            .status(Objects.isNull(itemDto.getAvailabilityStatus()) ? DRAFT.name().toLowerCase()
                : itemDto.getAvailabilityStatus().toLowerCase())
            .productType(itemDto.getType())
            .handle(itemDto.getHandle())
            .tags(StringUtils.isBlank(tags) ? null : tags)
            .vendor(getVendorName(itemDto))
            .publishedAt(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
            .publishedScope("web")
            .options(Collections.singletonList(Options.builder()
                .name("Title")
                .position(1)
                .values(variantTypes)
                .build()))
            .variants(variants)
            .build();
    }

    private static String getVendorName(ItemDto itemDto) {
        if (Objects.nonNull(itemDto.getPrimaryVendorItem())) {
            return itemDto.getPrimaryVendorItem().getVendorName();
        }
        if (Objects.nonNull(itemDto.getBackupVendorItem())) {
            return itemDto.getBackupVendorItem().getVendorName();
        }
        return null;
    }

    private static Variant buildVariant(String variantType, ItemDto itemDto, Long variantId) {
        BigDecimal price = null;
        String sku = null;
        String barcode = null;
        Integer position = null;
        String option1 = null;

        if (itemDto.getItemRegPrice() != null) {
            switch (variantType.toLowerCase()) {
                case "individual":
                    price = itemDto.getIndividualPrice();
                    position = 1;
                    option1 = "Individual";
                    break;
                case "pack no crv":
                    price = itemDto.getPrice();
                    position = 2;
                    option1 = "Pack No CRV";
                    break;
                case "pack", "pack plus crv":
                    price = itemDto.getPricePlusCrv();
                    sku = itemDto.getSkuNumber();
                    position = 3;
                    barcode = getBarcodeNumber(itemDto);
                    option1 = CategoryConstant.BEVERAGE.equalsIgnoreCase(itemDto.getDepartment()) ? "Pack Plus CRV" : "Pack";
                    break;
                default:
                    log.warn("Invalid variant type: {}", variantType);
                    break;
            }
        }

        return Variant.builder()
            .id(variantId)
            .title(variantType)
            .position(position)
            .price(price)
            .inventoryPolicy("deny")
            .inventoryManagement("shopify")
            .option1(option1)
            .sku(sku)
            .barcode(barcode)
            .taxable(false)
            .fulfillmentService("manual")
            .build();
    }

    public static VariantImageDto buildVariantImageDto(ItemDto itemDto,
        ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto) {
        List<ProductEdgeDto> edges = shopifyGraphQLQueryResponseDto.getData().getProducts().getEdges();
        if (CollectionUtils.isEmpty(edges)) {
            log.error("[buildVariantImageDto] Product not found for item with skuNumber: {}", itemDto.getSkuNumber());
            return null;
        }
        ShopifyGraphQLQueryResponseDto.ProductDto node = edges.getFirst().getNode();
        Long productId = extractId(node.getId());

        List<Long> variantIds = node.getVariants().getEdges().stream()
            .map(variantEdgeDto -> extractId(variantEdgeDto.getNode().getId())).toList();

        String photoUrl = getShopifyPhotoUrl(itemDto);

        Image image = Image.builder()
            .src(photoUrl)
            .width(800D)
            .height(800D)
            .productId(productId)
            .variantIds(variantIds)
            .build();

        List<ImageEdgeDto> imageEdgeDtos = node.getImages().getEdges();
        if (CollectionUtils.isNotEmpty(imageEdgeDtos)) {
            Long imageId = imageEdgeDtos.stream()
                .map(imageEdgeDto -> extractId(imageEdgeDto.getNode().getId()))
                .toList().getFirst();

            if (null != imageId) {
                image.setId(imageId);
            }
        }

        return VariantImageDto.builder()
            .image(image).build();
    }

    public static VariantImageDto buildVariantImageDto(ItemDto itemDto, ShopifyProductDto shopifyProductDto) {
        if (shopifyProductDto == null || shopifyProductDto.getProduct() == null) {
            log.error("[buildVariantImageDto] ShopifyProductDto or product is null for item with skuNumber: {}", itemDto.getSkuNumber());
            return null;
        }

        ProductDto product = shopifyProductDto.getProduct();
        Long productId = product.getId();

        List<Long> variantIds = Optional.ofNullable(product.getVariants())
            .orElse(Collections.emptyList())
            .stream()
            .map(Variant::getId)
            .filter(Objects::nonNull)
            .toList();

        String photoUrl = getShopifyPhotoUrl(itemDto);

        Image image = Image.builder()
            .src(photoUrl)
            .width(800D)
            .height(800D)
            .productId(productId)
            .variantIds(variantIds)
            .build();

        if (CollectionUtils.isNotEmpty(product.getImages())) {
            Long imageId = product.getImages().getFirst().getId();
            if (null != imageId) {
                image.setId(imageId);
            }
        }

        return VariantImageDto.builder()
            .image(image).build();
    }

    public static VariantImageDto buildVariantImageDto(ItemDto itemDto,
        ShopifyGraphQLQueryResponseDto shopifyGraphQLQueryResponseDto,
        ShopifyProductDto shopifyProductDto) {
        
        if (shopifyGraphQLQueryResponseDto != null && 
            shopifyGraphQLQueryResponseDto.getData() != null &&
            CollectionUtils.isNotEmpty(shopifyGraphQLQueryResponseDto.getData().getProducts().getEdges())) {
            return buildVariantImageDto(itemDto, shopifyGraphQLQueryResponseDto);
        }
        
        if (shopifyProductDto != null) {
            return buildVariantImageDto(itemDto, shopifyProductDto);
        }
        
        log.error("[buildVariantImageDto] Both shopifyGraphQLQueryResponseDto and shopifyProductDto are null or empty for item with skuNumber: {}", itemDto.getSkuNumber());
        return null;
    }

    public static Long extractId(String fullId) {
        String[] idParts = fullId.split("/");
        return (idParts.length > 0) ? Long.parseLong(idParts[idParts.length - 1]) : null;
    }


    public static List<MetafieldDto> buildMetaFieldDto(ItemDto itemDto) {
        List<MetafieldDto> metafieldDtos = new ArrayList<>();

        log.info("Building metafields for item with itemDto: {}", itemDto);

        // Process item attributes
        addMetafieldIfNotBlank(SHOPIFY_CUSTOM_NAMESPACE, metafieldDtos,
            "item_size",
            itemDto.getItemSize() == null ? null : itemDto.getItemSize().toString(),
            NUMBER_DECIMAL);
        addMetafieldIfNotBlank(SHOPIFY_CUSTOM_NAMESPACE,
            metafieldDtos,
            "unit_of_measure",
            itemDto.getItemSizeUnitMeasure(),
            SINGLE_LINE_TEXT_FIELD);

        addMetafieldIfNotBlank(SHOPIFY_ATTRIBUTES_NAMESPACE, metafieldDtos, "unit_size", buildUnitSizeValue(itemDto), VOLUME);

        // Process other fields
        addMetafieldIfNotNull(SHOPIFY_CUSTOM_NAMESPACE, metafieldDtos, "pack_size", itemDto.getPackageSize(), NUMBER_INTEGER);
        addMetafieldIfNotNull(SHOPIFY_CUSTOM_NAMESPACE,
            metafieldDtos,
            "product_title",
            itemDto.getNewDescription(),
            SINGLE_LINE_TEXT_FIELD);
        addMetafieldIfNotNull(SHOPIFY_ATTRIBUTES_NAMESPACE,
            metafieldDtos,
            "brand",
            itemDto.getBrand() != null ? itemDto.getBrand().getBrandName() : null,
            SINGLE_LINE_TEXT_FIELD);

        // Process item price
        if (itemDto.getItemRegPrice() != null) {
            log.info("ItemRegPrice: {}", itemDto.getItemRegPrice());
            if (null != itemDto.getItemRegPrice().getRegPrice()) {
                addMetafield(SHOPIFY_PRICING_NAMESPACE,
                    metafieldDtos,
                    "pack_no_crv",
                    itemDto.getItemRegPrice().getRegPrice().setScale(2, RoundingMode.HALF_UP).toString(),
                    NUMBER_DECIMAL);
            }
            if (null != itemDto.getItemRegPrice().getRegPriceIndividual()) {
                addMetafield(SHOPIFY_PRICING_NAMESPACE,
                    metafieldDtos,
                    "each",
                    itemDto.getItemRegPrice().getRegPriceIndividual().setScale(2, RoundingMode.HALF_UP).toString(),
                    NUMBER_DECIMAL);
            }
        }

        // Process location and company IDs
        addMetafieldIfNotNull(SHOPIFY_CUSTOM_NAMESPACE, metafieldDtos, "location_id", itemDto.getLocationId(), NUMBER_INTEGER);
        addMetafieldIfNotNull(SHOPIFY_CUSTOM_NAMESPACE, metafieldDtos, "company_id", itemDto.getCompanyId(), NUMBER_INTEGER);

        // Process id_tag
        if (itemDto.getCompanyId() != null && itemDto.getLocationId() != null) {
            addMetafield(SHOPIFY_CUSTOM_NAMESPACE,
                metafieldDtos,
                "id_tag",
                itemDto.getCompanyId() + "_" + itemDto.getLocationId(),
                SINGLE_LINE_TEXT_FIELD);
        }

        addMetafieldIfNotNull(SHOPIFY_CUSTOM_NAMESPACE,
            metafieldDtos,
            "item_weight_pounds",
            itemDto.getCaseWeight(),
            NUMBER_DECIMAL);

        // Check feature flag for category hierarchy fields
        addMetafieldIfNotNull(SHOPIFY_CUSTOM_NAMESPACE,
            metafieldDtos,
            "department",
            itemDto.getDepartment(),
            SINGLE_LINE_TEXT_FIELD);
        addMetafieldIfNotNull(SHOPIFY_CUSTOM_NAMESPACE, metafieldDtos, "category", itemDto.getCategory(), SINGLE_LINE_TEXT_FIELD);
        addMetafieldIfNotNull(SHOPIFY_CUSTOM_NAMESPACE,
            metafieldDtos,
            "sub_category",
            itemDto.getSubCategory(),
            SINGLE_LINE_TEXT_FIELD);
        addMetafieldIfNotNull(SHOPIFY_CUSTOM_NAMESPACE, metafieldDtos, "class", itemDto.getClazz(), SINGLE_LINE_TEXT_FIELD);

        UUID departmentId = itemDto.getCategoryGroup() != null ? itemDto.getCategoryGroup().getDepartmentId() : null;
        addMetafieldIfNotNull(SHOPIFY_CUSTOM_NAMESPACE,
            metafieldDtos,
            "department_id",
            departmentId,
            SINGLE_LINE_TEXT_FIELD);

        if (StringUtils.isNotBlank(itemDto.getEachUpc())) {
            String eachupc = itemDto.getEachUpc().split(",")[0];
            addMetafieldIfNotNull(SHOPIFY_CUSTOM_NAMESPACE, metafieldDtos, "eachupc", eachupc, SINGLE_LINE_TEXT_FIELD);
        }

        return metafieldDtos;
    }

    public static List<MetafieldDto> buildDeleteMetaFieldDto(ItemDto itemDto) {
        List<MetafieldDto> metafieldDtos = new ArrayList<>();

        log.info("Building metafields for item with itemDto: {}", itemDto);

        // Process item attributes
        if (null == itemDto.getItemSize()) {
            addMetafield(SHOPIFY_CUSTOM_NAMESPACE, metafieldDtos,
                "item_size",
                null,
                NUMBER_DECIMAL);
        }

        if (null == itemDto.getItemSizeUnitMeasure()) {
            addMetafield(SHOPIFY_CUSTOM_NAMESPACE,
                metafieldDtos,
                "unit_of_measure",
                itemDto.getItemSizeUnitMeasure(),
                SINGLE_LINE_TEXT_FIELD);
        }

        if (null == buildUnitSizeValue(itemDto)) {
            addMetafield(SHOPIFY_ATTRIBUTES_NAMESPACE, metafieldDtos, "unit_size", null, VOLUME);
        }

        if (null == itemDto.getCaseWeight()) {
            addMetafield(SHOPIFY_CUSTOM_NAMESPACE,
                metafieldDtos,
                "item_weight_pounds",
                null,
                NUMBER_DECIMAL);
        }

        if (StringUtils.isBlank(itemDto.getEachUpc())) {
            addMetafield(SHOPIFY_CUSTOM_NAMESPACE,
                    metafieldDtos,
                    "eachupc",
                    null,
                    SINGLE_LINE_TEXT_FIELD);
        }

        return metafieldDtos;
    }


    public static InventoryItemDto buildInventoryItemDto(ItemDto itemDto, ShopifyProductDto shopifyProductDto) {
        if (isInvalidInput(itemDto, shopifyProductDto)) {
            return null;
        }

        Variant variantPack = findPackVariant(shopifyProductDto.getProduct().getVariants(), itemDto.getSkuNumber());
        if (variantPack == null) {
            return null;
        }

        BigDecimal minimumCost = calculateMinimumCost(itemDto.getVendorItemDtos());

        return InventoryItemDto.builder()
            .inventoryItem(InventoryItemInfoDto.builder()
                .id(variantPack.getInventoryItemId())
                .cost(minimumCost.toString())
                .build())
            .build();
    }

    public static String buildRequestChannel() {
        String query = "{"
            + "    \"query\": \"query { publications(first: 10)  {edges {node {id name}}} }\""
            + "}";

        log.info("[buildRequestChannelQraphQL] query: {}", query);

        return query;
    }

    private static boolean isInvalidInput(ItemDto itemDto, ShopifyProductDto shopifyProductDto) {
        if (CollectionUtils.isEmpty(itemDto.getVendorItemDtos())) {
            log.warn("[buildInventoryItemDto] VendorItemDtos not found for item with skuNumber: {}", itemDto.getSkuNumber());
            return true;
        }
        if (shopifyProductDto == null || shopifyProductDto.getProduct() == null
            || shopifyProductDto.getProduct().getVariants() == null) {
            log.warn("[buildInventoryItemDto] Product or variants not found for item with skuNumber: {}", itemDto.getSkuNumber());
            return true;
        }
        return false;
    }

    private static Variant findPackVariant(List<Variant> variants, String skuNumber) {
        return variants.stream()
            .filter(variant -> variant.getTitle().equals(VariantType.PACK.getType()) || variant.getTitle()
                .equals(VariantType.PACK_PLUS_CRV.getType()))
            .findFirst()
            .orElseGet(() -> {
                log.warn("[buildInventoryItemDto] Variant not found for item with skuNumber: {}", skuNumber);
                return null;
            });
    }

    private static BigDecimal calculateMinimumCost(List<VendorItemDto> vendorItemDtos) {
        return vendorItemDtos.stream()
            .flatMap(dto -> Stream.of(dto.getCost(), dto.getSecondaryCost()))
            .filter(cost -> cost != null && cost.compareTo(BigDecimal.ZERO) > 0)
            .min(BigDecimal::compareTo)
            .orElse(BigDecimal.valueOf(10000));
    }


    private static void addMetafieldIfNotBlank(String namespace,
        List<MetafieldDto> metafieldDtos,
        String key,
        String value,
        String type) {
        if (StringUtils.isNotBlank(value)) {
            addMetafield(namespace, metafieldDtos, key, value, type);
        }
    }

    private static void addMetafieldIfNotNull(String namespace,
        List<MetafieldDto> metafieldDtos,
        String key,
        Object value,
        String type) {
        if (value != null) {
            addMetafield(namespace, metafieldDtos, key, value.toString(), type);
        }
    }

    private static void addMetafield(String namespace, List<MetafieldDto> metafieldDtos, String key, String value, String type) {
        metafieldDtos.add(MetafieldDto.builder()
            .metafield(MetafieldInfoDto.builder()
                .namespace(namespace)
                .key(key)
                .value(value)
                .type(type)
                .build())
            .build());
    }

    private static String getBarcodeNumber(ItemDto itemDto) {

        return Optional.ofNullable(itemDto.getItemUPCs())
            .orElse(Collections.emptyList())
            .stream()
            .filter(upc -> upc.getUpcNumber() != null && !upc.getUpcNumber().isEmpty())
            .collect(Collectors.toMap(
                ItemUPCDto::getItemUpcType,
                ItemUPCDto::getUpcNumber,
                (existing, replacement) -> existing,
                LinkedHashMap::new
            ))
            .entrySet()
            .stream()
            .sorted(Comparator.comparingInt(entry -> {
                if (entry.getKey() == ItemUpcType.EACH_UPC) {
                    return 1;
                }
                if (entry.getKey() == ItemUpcType.CASE_UPC) {
                    return 2;
                }
                return 3;
            }))
            .map(Map.Entry::getValue)
            .collect(Collectors.joining(" "));
    }

    private static String buildUnitSizeValue(ItemDto itemDto) {
        ItemAttributeDto bottleSizeAttribute = itemDto.getBottleSizeAttributeV2();
        if (null == bottleSizeAttribute || StringUtils.isBlank(bottleSizeAttribute.getValue())) {
            return null;
        }

        VolumeUnit unit = VolumeUnit.valueFromImsValue(bottleSizeAttribute.getUnit());
        String bottleSize = bottleSizeAttribute.getValue();
        if (null == unit) {
            log.warn("[buildMetaFieldDto] Invalid volume unit: {}", bottleSizeAttribute.getUnit());
            unit = VolumeUnit.FLUID_OUNCES;
            if (VolumeUtil.isValidBottleUnit(bottleSizeAttribute.getUnit())) {
                bottleSize = String.valueOf(VolumeUtil.convertToOunces(Double.parseDouble(bottleSize),
                    bottleSizeAttribute.getUnit()));
            }
        }

        VolumeMetafieldDto volumeMetafieldDto = VolumeMetafieldDto.builder()
            .unit(unit.getValue())
            .value(bottleSize)
            .build();
        return SerializationUtils.serialize(volumeMetafieldDto);
    }


    private static String getShopifyPhotoUrl (ItemDto itemDto) {
        if (Boolean.TRUE.equals(itemDto.getPromoFlag())) {
            log.info("[buildVariantImageDto] Promo flag is true for item with skuNumber: {}", itemDto.getSkuNumber());
            ItemImageDto prmoImage = itemDto.getItemImages().stream()
                    .filter(i -> i.getImageType().equals(ImageType.PROMO_IMAGE))
                    .findFirst()
                    .orElse(null);
            log.info("[buildVariantImageDto] Promo image found: {}", prmoImage);

            if (null != prmoImage) {
                return prmoImage.getUrl();
            }
        }

        return itemDto.getPhotoUrl();
    }
}
