package com.mercaso.ims.infrastructure.repository.itemversion.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.Type;


@Entity
@ToString(callSuper = true)
@Table(name = "item_version")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_version set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemVersionDo extends BaseDo {

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "sku_number")
    private String skuNumber;

    @Column(name = "version_number")
    private Integer versionNumber;

    @Column(columnDefinition = "item_data")
    @Type(JsonType.class)
    private String itemData;
}
