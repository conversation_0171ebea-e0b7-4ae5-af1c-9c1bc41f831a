package com.mercaso.ims.infrastructure.repository.phonenumber.jpa.mapper;

import com.mercaso.ims.domain.BaseValueObjectDoMapper;
import com.mercaso.ims.domain.phone.PhoneNumber;
import com.mercaso.ims.infrastructure.repository.phonenumber.jpa.dataobject.PhoneNumberDo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface PhoneNumberDoMapper extends BaseValueObjectDoMapper<PhoneNumberDo, PhoneNumber> {

    PhoneNumberDoMapper INSTANCE = Mappers.getMapper(PhoneNumberDoMapper.class);

    @Override
    PhoneNumber doToDomain(PhoneNumberDo phoneNumberDo);

    @Override
    PhoneNumberDo domainToDo(PhoneNumber phoneNumber);
}
