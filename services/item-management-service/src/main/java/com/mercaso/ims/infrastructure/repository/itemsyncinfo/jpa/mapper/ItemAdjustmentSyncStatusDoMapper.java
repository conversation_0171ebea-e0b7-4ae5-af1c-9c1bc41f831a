package com.mercaso.ims.infrastructure.repository.itemsyncinfo.jpa.mapper;

import com.mercaso.ims.domain.BaseValueObjectDoMapper;
import com.mercaso.ims.domain.itemsyncinfo.ItemAdjustmentSyncStatus;
import com.mercaso.ims.infrastructure.repository.itemsyncinfo.jpa.dataobject.ItemAdjustmentSyncStatusDo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ItemAdjustmentSyncStatusDoMapper extends BaseValueObjectDoMapper<ItemAdjustmentSyncStatusDo, ItemAdjustmentSyncStatus> {

    ItemAdjustmentSyncStatusDoMapper INSTANCE = Mappers.getMapper(ItemAdjustmentSyncStatusDoMapper.class);

    @Override
    ItemAdjustmentSyncStatus doToDomain(ItemAdjustmentSyncStatusDo itemSyncInfoDo);

    @Override
    ItemAdjustmentSyncStatusDo domainToDo(ItemAdjustmentSyncStatus itemSyncInfo);
}
