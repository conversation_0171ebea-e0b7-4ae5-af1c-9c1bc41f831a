package com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject;

import com.mercaso.ims.domain.vendor.enums.VendorStatus;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import java.time.LocalTime;

@Entity
@Table(name = "vendor")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update vendor set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class VendorDo extends BaseDo {

    @Column(name = "vendor_name")
    private String vendorName;

    @Column(name = "vendor_contact_name")
    private String vendorContactName;

    @Column(name = "vendor_contact_tel")
    private String vendorContactTel;

    @Column(name = "vendor_company_name")
    private String vendorCompanyName;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private VendorStatus vendorStatus;

    @Column(name = "external_picking")
    private Boolean externalPicking;
    
    @Column(name = "finale_id")
    private String finaleId;

    @Column(name = "shutdown_window_enabled")
    private Boolean shutdownWindowEnabled;

    @Column(name = "shutdown_window_start")
    private LocalTime shutdownWindowStart;

    @Column(name = "shutdown_window_end")
    private LocalTime shutdownWindowEnd;

    @Column(name = "shutdown_window_days")
    private String shutdownWindowDays;
}
