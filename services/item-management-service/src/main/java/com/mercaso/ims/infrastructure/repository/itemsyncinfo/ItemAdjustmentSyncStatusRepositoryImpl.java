package com.mercaso.ims.infrastructure.repository.itemsyncinfo;

import com.mercaso.ims.domain.itemsyncinfo.ItemAdjustmentSyncStatus;
import com.mercaso.ims.domain.itemsyncinfo.ItemAdjustmentSyncStatusRepository;
import com.mercaso.ims.domain.itemsyncinfo.enums.SyncShopifyStatus;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemsyncinfo.jpa.ItemAdjustmentSyncStatusJpaDao;
import com.mercaso.ims.infrastructure.repository.itemsyncinfo.jpa.dataobject.ItemAdjustmentSyncStatusDo;
import com.mercaso.ims.infrastructure.repository.itemsyncinfo.jpa.mapper.ItemAdjustmentSyncStatusDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ItemAdjustmentSyncStatusRepositoryImpl implements ItemAdjustmentSyncStatusRepository {

    private final ItemAdjustmentSyncStatusDoMapper itemAdjustmentSyncStatusDoMapper;

    private final ItemAdjustmentSyncStatusJpaDao itemSyncInfoJpaDao;

    @Override
    public ItemAdjustmentSyncStatus save(ItemAdjustmentSyncStatus domain) {
        ItemAdjustmentSyncStatusDo itemAdjustmentSyncStatusDo = itemAdjustmentSyncStatusDoMapper.domainToDo(domain);
        itemAdjustmentSyncStatusDo = itemSyncInfoJpaDao.save(itemAdjustmentSyncStatusDo);
        return itemAdjustmentSyncStatusDoMapper.doToDomain(itemAdjustmentSyncStatusDo);
    }

    @Override
    public ItemAdjustmentSyncStatus findById(UUID id) {
        ItemAdjustmentSyncStatusDo itemSyncInfoDo = itemSyncInfoJpaDao.findById(id).orElse(null);
        return itemAdjustmentSyncStatusDoMapper.doToDomain(itemSyncInfoDo);
    }

    @Override
    public ItemAdjustmentSyncStatus update(ItemAdjustmentSyncStatus domain) {
        ItemAdjustmentSyncStatusDo itemSyncInfoDo = itemSyncInfoJpaDao.findByBusinessEventId(domain.getBusinessEventId());
        if (Objects.isNull(itemSyncInfoDo)) {
            throw new ImsBusinessException(ErrorCodeEnums.ITEM_SYNC_INFO_NOT_FOUND.getCode());
        }
        ItemAdjustmentSyncStatusDo itemSyncInfoDoTarget = itemAdjustmentSyncStatusDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(itemSyncInfoDoTarget, itemSyncInfoDo, ignoreProperties.toArray(new String[0]));
        ItemAdjustmentSyncStatusDo result = itemSyncInfoJpaDao.save(itemSyncInfoDo);
        return itemAdjustmentSyncStatusDoMapper.doToDomain(result);
    }

    @Override
    public List<ItemAdjustmentSyncStatus> findBySyncShopifyStatus(SyncShopifyStatus syncShopifyStatus) {
        return Optional.ofNullable(itemSyncInfoJpaDao.findTop500BySyncShopifyStatusOrderByCreatedAt(
                syncShopifyStatus))
            .orElse(Collections.emptyList())
            .stream()
            .map(itemAdjustmentSyncStatusDoMapper::doToDomain)
            .toList();
    }

    @Override
    public ItemAdjustmentSyncStatus deleteById(UUID id) {
        ItemAdjustmentSyncStatusDo itemSyncInfoDo = itemSyncInfoJpaDao.findById(id).orElse(null);
        if (null == itemSyncInfoDo) {
            return null;
        }
        itemSyncInfoDo.setDeletedAt(Instant.now());
        itemSyncInfoDo.setDeletedBy(SecurityUtil.getLoginUserId());
        return itemAdjustmentSyncStatusDoMapper.doToDomain(itemSyncInfoJpaDao.save(itemSyncInfoDo));
    }

    @Override
    public ItemAdjustmentSyncStatus findByBusinessEventId(UUID businessEventId) {
        ItemAdjustmentSyncStatusDo itemSyncInfoDo = itemSyncInfoJpaDao.findByBusinessEventId(businessEventId);
        if (null == itemSyncInfoDo) {
            return null;
        }
        return itemAdjustmentSyncStatusDoMapper.doToDomain(itemSyncInfoDo);
    }
}

