package com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa;

import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.dataobject.ItemVendorRebateDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * JPA Repository for ItemVendorRebate
 */
@Repository
public interface ItemVendorRebateJpaDao extends JpaRepository<ItemVendorRebateDo, UUID> {

    /**
     * Find rebates by vendor ID
     */
    List<ItemVendorRebateDo> findByVendorId(UUID vendorId);

    /**
     * Find rebates by item ID
     */
    List<ItemVendorRebateDo> findByItemId(UUID itemId);

    /**
     * Find rebates by vendor item ID
     */
    List<ItemVendorRebateDo> findByVendorItemId(UUID vendorItemId);
}
