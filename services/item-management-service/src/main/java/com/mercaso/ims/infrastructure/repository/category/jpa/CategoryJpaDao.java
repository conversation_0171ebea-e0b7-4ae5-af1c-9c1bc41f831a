package com.mercaso.ims.infrastructure.repository.category.jpa;

import com.mercaso.ims.domain.category.enums.CategoryStatus;
import com.mercaso.ims.infrastructure.repository.category.jpa.dataobject.CategoryDo;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.UUID;

public interface CategoryJpaDao extends JpaRepository<CategoryDo, UUID> {

    List<CategoryDo> findByName(String name);

    List<CategoryDo> findAllByNameIn(List<String> names);

    List<CategoryDo> findAllByIdIn(List<UUID> ids);

    List<CategoryDo> findAllByNameAndStatus(String name, CategoryStatus categoryStatus);
}
