package com.mercaso.ims.infrastructure.repository.vendoritem.jpa;

import com.mercaso.ims.domain.vendoritem.enums.SnapshotType;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemAvailabilitySnapshotDo;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface VendorItemAvailabilitySnapshotJpaDao extends JpaRepository<VendorItemAvailabilitySnapshotDo, UUID> {

    List<VendorItemAvailabilitySnapshotDo> findByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(UUID vendorId, SnapshotType snapshotType);

    List<VendorItemAvailabilitySnapshotDo> findByVendorIdAndSnapshotTypeAndSnapshotTimeAfterOrderBySnapshotTimeDesc(UUID vendorId, SnapshotType snapshotType, Instant startTime);

    Optional<VendorItemAvailabilitySnapshotDo> findTop1ByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(UUID vendorId, SnapshotType snapshotType);
} 