package com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject;

import com.mercaso.ims.domain.vendoritem.enums.SnapshotType;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Table(name = "vendor_item_availability_snapshot")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update vendor_item_availability_snapshot set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class VendorItemAvailabilitySnapshotDo extends BaseDo {

    @Column(name = "vendor_id")
    private UUID vendorId;

    @Column(name = "snapshot_time")
    private Instant snapshotTime;

    @Column(name = "snapshot_type")
    @Enumerated(EnumType.STRING)
    private SnapshotType snapshotType;
} 