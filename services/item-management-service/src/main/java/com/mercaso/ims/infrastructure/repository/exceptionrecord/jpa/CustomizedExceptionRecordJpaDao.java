package com.mercaso.ims.infrastructure.repository.exceptionrecord.jpa;

import com.mercaso.ims.application.dto.ItemPriceExceptionRecordDto;
import com.mercaso.ims.application.dto.VendorItemCostExceptionRecordDto;
import com.mercaso.ims.application.query.ItemPriceExceptionRecordQuery;
import com.mercaso.ims.application.query.VendorItemCostExceptionRecordQuery;
import java.util.List;

public interface CustomizedExceptionRecordJpaDao {

    List<ItemPriceExceptionRecordDto> fetchItemPriceExceptionRecordDtoList(ItemPriceExceptionRecordQuery query);

    List<VendorItemCostExceptionRecordDto> fetchVendorItemCostExceptionRecordDtoList(VendorItemCostExceptionRecordQuery query);

    long itemPriceExceptionRecordCountQuery(ItemPriceExceptionRecordQuery query);

    long vendorItemCostExceptionRecordCountQuery(VendorItemCostExceptionRecordQuery query);


}
