package com.mercaso.ims.infrastructure.repository.itemattribute.jpa;

import com.mercaso.ims.infrastructure.repository.itemattribute.jpa.dataobject.ItemAttributeDo;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.UUID;

public interface ItemAttributeJpaDao extends JpaRepository<ItemAttributeDo, UUID> {

    List<ItemAttributeDo> findByItemId(UUID itemId);
}
