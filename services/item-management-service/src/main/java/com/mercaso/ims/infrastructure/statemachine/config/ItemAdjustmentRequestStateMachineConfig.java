package com.mercaso.ims.infrastructure.statemachine.config;

import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequest;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestTransitionEvents;
import com.mercaso.ims.infrastructure.statemachine.StatemachineFactory;
import com.mercaso.ims.infrastructure.statemachine.constant.StateMachineConstant;
import com.mercaso.ims.infrastructure.statemachine.factory.ImsStateMachineFactory;
import java.util.EnumSet;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;


@Configuration
@EnableStateMachineFactory(contextEvents = false, name = StateMachineConstant.ITEM_ADJUSTMENT_REQUEST_STATEMACHINE_NAME)
public class ItemAdjustmentRequestStateMachineConfig extends
    EnumStateMachineConfigurerAdapter<ItemAdjustmentRequestStatus, ItemAdjustmentRequestTransitionEvents> {


    @Override
    public void configure(StateMachineStateConfigurer<ItemAdjustmentRequestStatus, ItemAdjustmentRequestTransitionEvents> states)
        throws Exception {
        states.withStates()
            .initial(ItemAdjustmentRequestStatus.UPLOADED)
            .states(EnumSet.allOf(ItemAdjustmentRequestStatus.class));
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<ItemAdjustmentRequestStatus, ItemAdjustmentRequestTransitionEvents> transitions)
        throws Exception {
        transitions
            .withExternal()
            .source(ItemAdjustmentRequestStatus.UPLOADED)
            .target(ItemAdjustmentRequestStatus.FILE_PROCESSED)
            .event(ItemAdjustmentRequestTransitionEvents.FILE_PROCESSED)
            .and()
            .withExternal()
            .source(ItemAdjustmentRequestStatus.UPLOADED)
            .target(ItemAdjustmentRequestStatus.FAILURE)
            .event(ItemAdjustmentRequestTransitionEvents.PROCESSED_FAILURE)
            .and()
            .withExternal()
            .source(ItemAdjustmentRequestStatus.FILE_PROCESSED)
            .target(ItemAdjustmentRequestStatus.COMPLETED)
            .event(ItemAdjustmentRequestTransitionEvents.COMPLETED);
    }

    @Bean(name = StateMachineConstant.ITEM_ADJUSTMENT_REQUEST_STATEMACHINE_FACTORY_NAME)
    @StatemachineFactory(domainClass = ItemAdjustmentRequest.class)
    public ImsStateMachineFactory<ItemAdjustmentRequestStatus, ItemAdjustmentRequestTransitionEvents, ItemAdjustmentRequest> itemAdjustmentRequestStateMachineAdapter(
        @Qualifier(value = StateMachineConstant.ITEM_ADJUSTMENT_REQUEST_STATEMACHINE_NAME)
        StateMachineFactory<ItemAdjustmentRequestStatus, ItemAdjustmentRequestTransitionEvents> stateMachineFactory,
        @Qualifier(value = StateMachineConstant.ITEM_ADJUSTMENT_REQUEST_STATEMACHINE_PERSISTER_NAME)
        StateMachinePersister<ItemAdjustmentRequestStatus, ItemAdjustmentRequestTransitionEvents, ItemAdjustmentRequest> persister) {
        return new ImsStateMachineFactory<>(stateMachineFactory, persister);
    }

    @Bean(name = StateMachineConstant.ITEM_ADJUSTMENT_REQUEST_STATEMACHINE_PERSISTER_NAME)
    public StateMachinePersister<ItemAdjustmentRequestStatus, ItemAdjustmentRequestTransitionEvents, ItemAdjustmentRequest> itemAdjustmentRequestStateMachinePersister(
        StateMachinePersist<ItemAdjustmentRequestStatus, ItemAdjustmentRequestTransitionEvents, ItemAdjustmentRequest> persist) {
        return new DefaultStateMachinePersister<>(persist);
    }

}
