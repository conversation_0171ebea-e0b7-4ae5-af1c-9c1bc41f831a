package com.mercaso.ims.infrastructure.external.finale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompletedPurchaseOrderDto {
    
    private String orderId;
    private String status;
    private String orderDate;
    private String apptDate;
    private String apptTime;
    private String recordLastUpdated;
    private String recordLastUpdatedUser;
    private String dueDate;
    private String customer;
    private String supplierPartyId;
    private String supplier;
    private String shipmentsStatusSummary;
    private String shipmentsSummary;
    private String type;
    
    private List<CompletedPurchaseOrderItemDto> items;

    public boolean isReceived() {
        return StringUtils.isNotBlank(shipmentsSummary)
                && shipmentsSummary.startsWith("Received ");
    }

    public boolean isJitPurchaseOrder() {
        return StringUtils.isNotBlank(orderId)
                && orderId.startsWith("J");
    }

    public boolean isPurchaseType() {
        return StringUtils.isNotBlank(type)
                && type.equalsIgnoreCase("Purchase");
    }
}