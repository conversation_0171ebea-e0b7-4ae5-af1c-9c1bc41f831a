package com.mercaso.ims.infrastructure.repository.businessevent.jpa;

import com.mercaso.ims.infrastructure.repository.businessevent.jpa.dataobject.BusinessEventDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface BusinessEventJpaDao extends JpaRepository<BusinessEventDo, UUID> {

    List<BusinessEventDo> findByIdInOrderByCreatedAtDesc(List<UUID> ids);
}
