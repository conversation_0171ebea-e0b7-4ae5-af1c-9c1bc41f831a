package com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa;

import com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.dataobject.ItemCostCollectionDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ItemCostCollectionJpaDao extends JpaRepository<ItemCostCollectionDo, UUID> {

    List<ItemCostCollectionDo> findByVendorId(UUID vendorId);

}
