package com.mercaso.ims.infrastructure.external.finale.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonPropertyOrder({"groupName",
    "roleTypeIdList",
    "statusId"})
public class CreateVendorRequestDto {

    @JsonProperty("groupName")
    private String groupName;
    @JsonProperty("roleTypeIdList")
    private List<String> roleTypeIdList;
    @JsonProperty("statusId")
    private String statusId;


}