package com.mercaso.ims.infrastructure.repository.itemtag.jpa.mapper;

import com.mercaso.ims.domain.BaseValueObjectDoMapper;
import com.mercaso.ims.domain.item.ItemTag;
import com.mercaso.ims.infrastructure.repository.itemtag.jpa.dataobject.ItemTagDo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ItemTagDoMapper extends BaseValueObjectDoMapper<ItemTagDo, ItemTag> {

    ItemTagDoMapper INSTANCE = Mappers.getMapper(ItemTagDoMapper.class);

    @Override
    ItemTag doToDomain(ItemTagDo itemTagDo);

    @Override
    ItemTagDo domainToDo(ItemTag itemTag);
}
