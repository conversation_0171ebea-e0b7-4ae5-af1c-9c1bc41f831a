package com.mercaso.ims.infrastructure.repository.company.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;

@Entity
@Table(name = "company")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update company set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class CompanyDo extends BaseDo {

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "name")
    private String name;
}
