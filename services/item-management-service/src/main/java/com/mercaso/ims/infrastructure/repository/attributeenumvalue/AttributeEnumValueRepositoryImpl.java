package com.mercaso.ims.infrastructure.repository.attributeenumvalue;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ATTRIBUTE_ENUM_VALUE_NOT_FOUND;

import com.mercaso.ims.domain.attributeenumvalue.AttributeEnumValue;
import com.mercaso.ims.domain.attributeenumvalue.AttributeEnumValueRepository;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.AttributeEnumValueJpaDao;
import com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.dataobject.AttributeEnumValueDo;
import com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.mapper.AttributeEnumValueDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class AttributeEnumValueRepositoryImpl implements AttributeEnumValueRepository {

    public final AttributeEnumValueJpaDao attributeEnumValueJpaDao;
    public final AttributeEnumValueDoMapper attributeEnumValueDoMapper;

    @Override
    public AttributeEnumValue save(AttributeEnumValue domain) {
        AttributeEnumValueDo attributeEnumValueDo = attributeEnumValueDoMapper.domainToDo(domain);
        attributeEnumValueDo = attributeEnumValueJpaDao.save(attributeEnumValueDo);
        return attributeEnumValueDoMapper.doToDomain(attributeEnumValueDo);
    }

    @Override
    public AttributeEnumValue findById(UUID id) {
        return attributeEnumValueDoMapper.doToDomain(attributeEnumValueJpaDao.findById(id).orElse(null));
    }

    @Override
    public AttributeEnumValue update(AttributeEnumValue domain) {
        AttributeEnumValueDo attributeEnumValueDo = attributeEnumValueJpaDao.findById(domain.getId()).orElse(null);
        if (null == attributeEnumValueDo) {
            throw new ImsBusinessException(ATTRIBUTE_ENUM_VALUE_NOT_FOUND);
        }
        AttributeEnumValueDo target = attributeEnumValueDoMapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
            "createdAt"));
        BeanUtils.copyProperties(target, attributeEnumValueDo, ignoreProperties.toArray(new String[0]));
        attributeEnumValueDo = attributeEnumValueJpaDao.save(attributeEnumValueDo);
        return attributeEnumValueDoMapper.doToDomain(attributeEnumValueDo);
    }

    @Override
    public AttributeEnumValue deleteById(UUID id) {
        AttributeEnumValueDo attributeEnumValueDo = attributeEnumValueJpaDao.findById(id).orElse(null);
        if (null == attributeEnumValueDo) {
            return null;
        }
        attributeEnumValueDo.setDeletedAt(Instant.now());
        attributeEnumValueDo.setDeletedBy(SecurityUtil.getLoginUserId());
        attributeEnumValueDo = attributeEnumValueJpaDao.save(attributeEnumValueDo);
        return attributeEnumValueDoMapper.doToDomain(attributeEnumValueDo);
    }

    @Override
    public List<AttributeEnumValue> findByAttributeId(UUID attributeId) {
        return Optional.ofNullable(attributeEnumValueJpaDao.findByAttributeId(attributeId))
            .orElse(Collections.emptyList())
            .stream()
            .map(attributeEnumValueDoMapper::doToDomain)
            .toList().reversed();
    }
}
