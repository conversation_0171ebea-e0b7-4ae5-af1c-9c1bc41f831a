package com.mercaso.ims.infrastructure.schedule;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskType;
import com.mercaso.ims.domain.taskqueue.service.ApiTaskQueueService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.apitaskprocess.ApiTaskProcessor;
import com.mercaso.ims.infrastructure.apitaskprocess.ApiTaskProcessorRegistry;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.ratelimiter.RequestNotPermitted;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Scheduler for processing API tasks with rate limiting
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApiTaskScheduler {

    private static final Integer PROCESS_TASKS_LOCK_KEY = "[ApiTaskScheduler.processTasks]".hashCode();
    private static final Integer RESET_STUCK_TASKS_LOCK_KEY = "[ApiTaskScheduler.resetStuckTasks]".hashCode();
    private static final Integer CLEANUP_COMPLETED_TASKS_LOCK_KEY = "[ApiTaskScheduler.cleanupCompletedTasks]".hashCode();
    private static final int BATCH_SIZE = 50;
    private static final Duration STUCK_TASK_TIMEOUT = Duration.ofMinutes(30);
    private static final Duration CLEANUP_OLDER_THAN = Duration.ofDays(7);

    private final ApiTaskQueueService apiTaskQueueService;
    private final List<ApiTaskProcessor<?>> taskProcessors;
    private final ApiTaskProcessorRegistry processorRegistry;
    private final RateLimiterRegistry rateLimiterRegistry;
    private final PgAdvisoryLock pgAdvisoryLock;
    private final EntityManagerFactory managerFactory;
    private final ObjectMapper objectMapper;

    private Map<String, ApiTaskProcessor<?>> processorMap;

    /**
     * Initialize processor map after bean creation
     */
    public void initProcessorMap() {
        if (processorMap == null) {
            processorMap = taskProcessors.stream()
                    .collect(Collectors.toMap(
                            ApiTaskProcessor::getTaskType,
                            Function.identity()
                    ));
            log.info("Initialized {} task processors: {}", processorMap.size(), processorMap.keySet());
        }
    }

    /**
     * Process pending API tasks every 60 seconds
     */
    @Scheduled(fixedDelay = 60000)
    public void processTasks() {
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                    PROCESS_TASKS_LOCK_KEY,
                    "Process API Tasks");
            if (isAcquired == null || !isAcquired) {
                log.debug("[ApiTaskScheduler.processTasks] is already in progress, skipping...");
                return;
            }

            initProcessorMap();
            processExecutableTasks();

        } finally {
            pgAdvisoryLock.unLock(entityManager, PROCESS_TASKS_LOCK_KEY, "unlock [ApiTaskScheduler.processTasks]");
            entityManager.close();
        }
    }

    /**
     * Reset stuck tasks every 10 minutes
     */
    @Scheduled(fixedDelay = 600000)
    public void resetStuckTasks() {
        EntityManager entityManager = managerFactory.createEntityManager();

        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                    RESET_STUCK_TASKS_LOCK_KEY,
                    "Reset Stuck Task");
            if (isAcquired == null || !isAcquired) {
                log.debug("[ApiTaskScheduler.resetStuckTasks] is already in progress, skipping...");
                return;
            }

            int resetCount = apiTaskQueueService.resetStuckTasks(STUCK_TASK_TIMEOUT);
            if (resetCount > 0) {
                log.warn("Reset {} stuck tasks that were processing for more than {}",
                        resetCount, STUCK_TASK_TIMEOUT);
            }
        } catch (Exception e) {
            log.error("Error resetting stuck tasks", e);
        } finally {
            pgAdvisoryLock.unLock(entityManager, RESET_STUCK_TASKS_LOCK_KEY, "unlock [ApiTaskScheduler.resetStuckTasks]");
            entityManager.close();
        }
    }

    /**
     * Clean up completed tasks daily at 2 AM
     */
    @Scheduled(cron = "0 0 2 * * *")
    public void cleanupCompletedTasks() {
        EntityManager entityManager = managerFactory.createEntityManager();

        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                    CLEANUP_COMPLETED_TASKS_LOCK_KEY,
                    "Cleanup Completed Tasks");
            if (isAcquired == null || !isAcquired) {
                log.debug("[ApiTaskScheduler.cleanupCompletedTasks] is already in progress, skipping...");
                return;
            }

            int cleanedCount = apiTaskQueueService.cleanupCompletedTasks(CLEANUP_OLDER_THAN);
            if (cleanedCount > 0) {
                log.info("Cleaned up {} completed tasks older than {}",
                        cleanedCount, CLEANUP_OLDER_THAN);
            }
        } catch (Exception e) {
            log.error("Error cleaning up completed tasks", e);
        } finally {
            pgAdvisoryLock.unLock(entityManager, CLEANUP_COMPLETED_TASKS_LOCK_KEY, "unlock [ApiTaskScheduler.cleanupCompletedTasks]");
            entityManager.close();
        }
    }

    /**
     * Process executable tasks for all task types
     */
    private void processExecutableTasks() {
        // Get all pending task types
        List<ApiTaskQueue> executableTasks = apiTaskQueueService.getExecutableTasks(BATCH_SIZE);

        for (ApiTaskQueue taskQueue : executableTasks) {
            String taskType = taskQueue.getTaskType();
            try {

                if (null == taskType) {
                    log.error("Task {} has no task type, skipping", taskQueue.getId());
                    continue;
                }

                // Find a processor that can handle this task type
                ApiTaskProcessor<?> processor = findProcessorForTaskType(taskType);
                if (processor != null) {
                    processTasksForType(processor, taskQueue);
                } else {
                    log.warn("No processor found for task type: {}", taskType);
                }
            } catch (Exception e) {
                log.error("Error processing tasks for type {}: {}", taskType, e.getMessage(), e);
            }
        }
    }

    /**
     * Find a processor that can handle the given task type
     */
    private ApiTaskProcessor<?> findProcessorForTaskType(String taskType) {
        // Use the registry to find the appropriate processor
        return processorRegistry.findProcessorForTaskType(taskType);
    }

    /**
     * Get the appropriate rate limiter for the given task type
     */
    private RateLimiter getRateLimiterForTaskType(String taskType) {
        try {
            TaskType taskTypeEnum = TaskType.fromTaskType(taskType);
            return rateLimiterRegistry.rateLimiter(taskTypeEnum.getRateLimiterName());
        } catch (IllegalArgumentException e) {
            log.warn("Unknown task type {}, using default rate limiter", taskType);
            return rateLimiterRegistry.rateLimiter("finaleGetProduct");
        }
    }

    /**
     * Process tasks for a specific task type using the given processor
     */
    private void processTasksForType(ApiTaskProcessor<?> processor, ApiTaskQueue taskQueue) {
        String taskType = taskQueue.getTaskType();

        // Get the appropriate rate limiter for this task type
        RateLimiter rateLimiter = getRateLimiterForTaskType(taskType);

        log.debug("Processing tasks for type {} with processor {}", taskType, processor.getClass().getSimpleName());

        try {
            processTask(taskQueue, processor, rateLimiter);
        } catch (Exception e) {
            log.error("Error processing task {}: {}", taskQueue.getId(), e.getMessage(), e);
        }
    }

    /**
     * Process a single task with rate limiting
     */
    private void processTask(ApiTaskQueue task, ApiTaskProcessor<?> processor, RateLimiter rateLimiter) {
        try {
            // Apply rate limiting
            rateLimiter.executeRunnable(
                    () -> {
                        try {
                            executeTask(task, processor);
                        } catch (Exception e) {
                            throw new ImsBusinessException("Error executing task: " + task.getId(), e);
                        }
                    });
        } catch (RequestNotPermitted e) {
            log.error("Rate limit exceeded for task type {}, will retry later", processor.getTaskType());
        } catch (RuntimeException e) {
            handleTaskException(task, processor, e);
        }
    }

    /**
     * Execute a single task
     */
    private void executeTask(ApiTaskQueue task, ApiTaskProcessor<?> processor) throws Exception {
        // Mark task as started
        apiTaskQueueService.markTaskAsStarted(task.getId());

        try {
            // Execute the task
            Object result = processor.executeTaskWithLogging(task);

            // Handle response
            String responsePayload = null;
            if (processor.needsResponse() && result != null) {
                responsePayload = objectMapper.writeValueAsString(result);
            }

            // Mark task as completed
            apiTaskQueueService.markTaskAsCompleted(task.getId(), responsePayload);

            log.info("Successfully completed task {} of type {}", task.getId(), task.getTaskType());

        } catch (Exception e) {
            handleTaskException(task, processor, e);
            throw e;
        }
    }

    /**
     * Handle task execution exceptions
     */
    private void handleTaskException(ApiTaskQueue task, ApiTaskProcessor<?> processor, Exception exception) {
        String errorMessage = exception.getMessage();

        // Unwrap ImsBusinessException to check the underlying cause for retry logic
        Exception exceptionToCheck = exception;
        if (exception instanceof ImsBusinessException && exception.getCause() instanceof Exception) {
            exceptionToCheck = (Exception) exception.getCause();
        }

        if (processor.isRetryableException(exceptionToCheck) && !task.hasReachedMaxRetries()) {
            // Schedule for retry
            long retryDelay = processor.calculateRetryDelay(task.getCurrentRetryCount() + 1);
            apiTaskQueueService.markTaskForRetry(task.getId(), errorMessage, Duration.ofSeconds(retryDelay));

            log.warn("Task {} failed, scheduled for retry in {} seconds: {}",
                    task.getId(), retryDelay, errorMessage);
        } else {
            // Mark as permanently failed
            apiTaskQueueService.markTaskAsFailed(task.getId(), errorMessage);

            log.error("Task {} permanently failed: {}", task.getId(), errorMessage);
        }
    }
}
