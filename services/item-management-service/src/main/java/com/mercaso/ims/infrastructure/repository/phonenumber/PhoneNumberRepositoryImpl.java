package com.mercaso.ims.infrastructure.repository.phonenumber;

import com.mercaso.ims.domain.phone.PhoneNumber;
import com.mercaso.ims.domain.phone.PhoneNumberRepository;
import com.mercaso.ims.domain.phone.enums.PhoneType;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.phonenumber.jpa.PhoneNumberJpaDao;
import com.mercaso.ims.infrastructure.repository.phonenumber.jpa.dataobject.PhoneNumberDo;
import com.mercaso.ims.infrastructure.repository.phonenumber.jpa.mapper.PhoneNumberDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class PhoneNumberRepositoryImpl implements PhoneNumberRepository {

    private final PhoneNumberDoMapper phoneNumberDoMapper;
    private final PhoneNumberJpaDao phoneNumberJpaDao;

    @Override
    public PhoneNumber save(PhoneNumber domain) {
        PhoneNumberDo phoneNumberDo = phoneNumberDoMapper.domainToDo(domain);
        phoneNumberDo = phoneNumberJpaDao.save(phoneNumberDo);
        return phoneNumberDoMapper.doToDomain(phoneNumberDo);
    }

    @Override
    public PhoneNumber findById(UUID id) {
        return phoneNumberDoMapper.doToDomain(phoneNumberJpaDao.findById(id).orElse(null));
    }

    @Override
    public PhoneNumber update(PhoneNumber domain) {
        PhoneNumberDo phoneNumberDo = phoneNumberJpaDao.findById(domain.getId()).orElse(null);
        if (null == phoneNumberDo) {
            throw new ImsBusinessException(ErrorCodeEnums.PHONE_NUMBER_NOT_FOUND.getCode());
        }
        PhoneNumberDo target = phoneNumberDoMapper.domainToDo(domain);
        List<String> ignoreProperties = List.of("createdBy", "createdAt");
        BeanUtils.copyProperties(target, phoneNumberDo, ignoreProperties.toArray(new String[0]));
        phoneNumberDo = phoneNumberJpaDao.save(phoneNumberDo);
        return phoneNumberDoMapper.doToDomain(phoneNumberDo);
    }

    @Override
    public PhoneNumber deleteById(UUID id) {
        PhoneNumberDo phoneNumberDo = phoneNumberJpaDao.findById(id).orElse(null);
        if (null == phoneNumberDo) {
            return null;
        }
        phoneNumberDo.setDeletedAt(Instant.now());
        phoneNumberDo.setDeletedBy(SecurityUtil.getLoginUserId());
        phoneNumberDo.setDeletedUserName(SecurityUtil.getUserName());
        phoneNumberDo = phoneNumberJpaDao.save(phoneNumberDo);
        return phoneNumberDoMapper.doToDomain(phoneNumberDo);
    }

    @Override
    public List<PhoneNumber> findByEntityTypeAndEntityId(String entityType, UUID entityId) {
        return Optional.ofNullable(phoneNumberJpaDao.findByEntityTypeAndEntityId(entityType, entityId))
                .orElse(List.of())
                .stream()
                .map(phoneNumberDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<PhoneNumber> findByEntityTypeAndEntityIdAndPhoneType(String entityType, UUID entityId, PhoneType phoneType) {
        return Optional.ofNullable(phoneNumberJpaDao.findByEntityTypeAndEntityIdAndPhoneType(entityType, entityId, phoneType))
                .orElse(List.of())
                .stream()
                .map(phoneNumberDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<PhoneNumber> findByPhoneNumber(String phoneNumber) {
        return Optional.ofNullable(phoneNumberJpaDao.findByPhoneNumber(phoneNumber))
                .orElse(List.of())
                .stream()
                .map(phoneNumberDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<PhoneNumber> findByEntityType(String entityType) {
        return Optional.ofNullable(phoneNumberJpaDao.findByEntityType(entityType))
                .orElse(List.of())
                .stream()
                .map(phoneNumberDoMapper::doToDomain)
                .toList();
    }
}
