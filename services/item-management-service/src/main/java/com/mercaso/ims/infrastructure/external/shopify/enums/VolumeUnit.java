package com.mercaso.ims.infrastructure.external.shopify.enums;

import lombok.Getter;
@Getter
public enum VolumeUnit {
    MILLILITERS("ml", "ml"),
    LITERS("L", "Liter"),
    LITERS_ALIAS("L", "l"),
    FLUID_OUNCES("fl_oz", "oz"),
    PINTS("pt", ""),
    QUARTS("qt", ""),
    GALLONS("gal", "gallon"),
    CUBIC_CENTIMETERS("cm3","cm3"),
    CUBIC_METERS("m3", "m3"),
    CUBIC_INCHES("in3", "in3"),
    CUBIC_FEET("ft3", "ft3"),;


    private final String value;
    private final String imsValue;



    VolumeUnit(String value, String imsValue) {
        this.value = value;
        this.imsValue = imsValue;
    }

    public static VolumeUnit valueFromImsValue(String imsValue) {
        if (imsValue == null) {
            return null;
        }
        for (VolumeUnit volumeUnit : VolumeUnit.values()) {
            if (imsValue.equalsIgnoreCase(volumeUnit.imsValue)) {
                return volumeUnit;
            }
        }
        return null;
    }
}
