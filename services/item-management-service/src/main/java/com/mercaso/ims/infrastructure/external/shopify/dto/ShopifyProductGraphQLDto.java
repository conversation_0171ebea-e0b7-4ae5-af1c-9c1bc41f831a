package com.mercaso.ims.infrastructure.external.shopify.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class ShopifyProductGraphQLDto {

    private String sku;


    public String buildQueryBySkuQraphQL() {
        String query = "{"
            + "    \"query\": \"query { products(first: 10, reverse: true, query:\\\"sku:%s\\\") { edges { node { id tags variants(first: 3) { edges{ node{ id title sku } }} images(first: 10) {edges {node {id}}} }}}}\""
            + "}";

        String formattedQuery = String.format(query, sku);
        log.info("[buildGraphQlQuery] query: {}", formattedQuery);

        return formattedQuery;
    }

}
