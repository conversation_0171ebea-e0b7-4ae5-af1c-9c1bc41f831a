package com.mercaso.ims.infrastructure.repository.category.jpa;

import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.query.CategoryQuery;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedCategoryJpaDaoImpl implements CustomizedCategoryJpaDao {


    private static final String QUERY_CATEGORY_TREE = """
        SELECT ch.category_id, c.name as category_name, ch.ancestor_category_id, ch.depth, ch.sort_order, anc.name as ancestor_name
        FROM category_hierarchy ch
        JOIN category c ON ch.category_id = c.id
        JOIN category anc ON ch.ancestor_category_id = anc.id
        WHERE 1=1 AND ch.deleted_at IS NULL AND c.deleted_at IS NULL AND anc.deleted_at IS NULL
        """;

    private final NamedParameterJdbcTemplate jdbcTemplate;

    @Override
    public List<CategoryDto> getCategories(CategoryQuery categoryQuery) {
        StringBuilder sql = new StringBuilder(QUERY_CATEGORY_TREE);
        sql.append(this.buildParamCondition(categoryQuery));
        sql.append(" ORDER BY ch.depth, ch.sort_order ");

        MapSqlParameterSource params = buildQueryParams(categoryQuery);

        return jdbcTemplate.query(sql.toString(), params, new RowMapper<CategoryDto>() {
            @Override
            public CategoryDto mapRow(ResultSet rs, int rowNum) throws SQLException {
                CategoryDto categoryDto = new CategoryDto();
                categoryDto.setCategoryId(UUID.fromString(rs.getString("category_id")));
                categoryDto.setCategoryName(rs.getString("category_name"));
                categoryDto.setAncestorName(rs.getString("ancestor_name"));
                categoryDto.setAncestorCategoryId(UUID.fromString(rs.getString("ancestor_category_id")));
                categoryDto.setDepth(rs.getInt("depth"));
                categoryDto.setSortOrder(rs.getInt("sort_order"));
                return categoryDto;
            }
        });
    }


    private String buildParamCondition(CategoryQuery categoryQuery) {
        StringBuilder sql = new StringBuilder();
        if (null != categoryQuery.getCategoryId()) {
            sql.append(" AND ch.category_id = :categoryId ");
        }
        if (null != categoryQuery.getCategoryIds() && !categoryQuery.getCategoryIds().isEmpty()) {
            sql.append(" AND ch.category_id IN (:categoryIds) ");
        }
        if (null != categoryQuery.getAncestorCategoryId()) {
            sql.append(" AND ch.ancestor_category_id = :ancestorCategoryId ");
        }
        if (null != categoryQuery.getDepth()) {
            sql.append(" AND ch.depth = :depth ");
        }
        if (null != categoryQuery.getStatus()) {
            sql.append(" AND c.status = :status ");
        }
        if (null != categoryQuery.getLeafCategoryName()) {
            sql.append(" AND c.name = :leafCategoryName ");
        }
        return sql.toString();
    }

    private MapSqlParameterSource buildQueryParams(CategoryQuery categoryQuery) {
        MapSqlParameterSource params = new MapSqlParameterSource();

        if (categoryQuery.getCategoryId() != null) {
            params.addValue("categoryId", categoryQuery.getCategoryId());
        }
        if (categoryQuery.getCategoryIds() != null && !categoryQuery.getCategoryIds().isEmpty()) {
            params.addValue("categoryIds", categoryQuery.getCategoryIds());
        }
        if (categoryQuery.getAncestorCategoryId() != null) {
            params.addValue("ancestorCategoryId", categoryQuery.getAncestorCategoryId());
        }
        if (categoryQuery.getDepth() != null) {
            params.addValue("depth", categoryQuery.getDepth());
        }
        if (categoryQuery.getStatus() != null) {
            params.addValue("status", categoryQuery.getStatus().name());
        }
        if (categoryQuery.getLeafCategoryName() != null) {
            params.addValue("leafCategoryName", categoryQuery.getLeafCategoryName());
        }

        return params;
    }
}
