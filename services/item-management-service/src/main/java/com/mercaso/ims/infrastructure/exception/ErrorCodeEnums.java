package com.mercaso.ims.infrastructure.exception;

public enum ErrorCodeEnums {

    COMMON_CODE("000000"),
    NOT_FOUND("000400"),

    //    BRAND
    BRAND_NOT_FOUND("001001"),
    BRAND_ALREADY_EXIST("001002"),
    INVALID_BRAND_NAME("001003"),
    BRAND_HAS_ITEMS("001004"),

    //   CATEGORY
    CATEGORY_NOT_FOUND("002001"),
    CATEGORY_NAME_ALREADY_EXISTS("002002"),
    CATEGORY_RELATIONSHIP_IS_EMPTY("002003"),
    CATEGORY_SHOULD_BE_NOT_NULL("002004"),
    CATEGORY_HAS_ITEMS("002005"),

    //    ATTRIBUTE
    ATTRIBUTE_NOT_FOUND("003001"),
    ATTRIBUTE_ENUM_VALUE_NOT_FOUND("003002"),
    ATTRIBUTE_GROUP_NOT_FOUND("003003"),


    //    COMPANY
    COMPANY_NOT_FOUND("004001"),


    //    ITEM
    ITEM_NOT_FOUND("005001"),
    ITEM_ALREADY_EXIST("005002"),
    INVALID_SKU_NUMBER("005003"),
    ITEM_BINDING_PHOTO_ERROR("005004"),
    INVALID_BOTTLE_UNIT("005005"),
    ITEM_LIST_SEARCH_INVALID_FILTER_KEY("005006"),
    ITEM_LIST_SEARCH_JSON_PROCESSING_EXCEPTION("005007"),

    //    ITEM PROMO
    ITEM_PROMO_PRICE_NOT_FOUND("006001"),
    ITEM_GENERATE_PROMO_PHOTO_ERROR("006002"),


    //    ITEM REG
    ITEM_REG_PRICE_NOT_FOUND("007001"),
    ITEM_REG_PRICE_ALREADY_EXIST("007002"),
    ITEM_REG_PRICE_HAS_BEEN_CHANGED("007003"),
    ITEM_REG_PRICE_CAN_NOT_CHANGED("007004"),
    ITEM_REG_PRICE_ALREADY_BOUND("007005"),


    //    LOCATION
    LOCATION_NOT_FOUND("008001"),


    //    VENDOR
    VENDOR_NOT_FOUND("009001"),
    VENDOR_ALREADY_EXISTS("009002"),
    INVALID_BACKUP_VENDOR("009003"),
    INVALID_PRIMARY_PO_VENDOR("009004"),
    INVALID_PRIMARY_JIT_VENDOR("009005"),
    VENDOR_HAS_ASSOCIATED_ITEMS("009006"),
    VENDOR_IS_EXTERNAL("009007"),

    //    VENDOR ITEM
    VENDOR_ITEM_NOT_FOUND("010001"),
    PRIMARY_VENDOR_ITEM_NOT_FOUND("010002"),
    VENDOR_ITEM_ALREADY_EXISTS("010003"),
    BACKUP_VENDOR_ITEM_NOT_FOUND("010004"),
    CANNOT_DELETE_PRIMARY_DIRECT_VENDOR_ITEM("010005"),
    CANNOT_DELETE_PRIMARY_JIT_VENDOR_ITEM("010006"),
    CANNOT_DELETE_LAST_VENDOR_ITEM_WITHOUT_ALTERNATIVE("010007"),


    //    Plytix other data
    PLYTIX_OTHER_DATA_NOT_FOUND("011001"),


    //
    ITEM_ADJUSTMENT_NOT_FOUND("012001"),
    ITEM_ADJUSTMENT_STATUS_HAS_BEEN_CHANGED("012002"),

    //
    ITEM_ADJUSTMENT_REQUEST_NOT_FOUND("013001"),

    //Shopify
    SHOPIFY_REQUEST_PARAM_ERROR("015001"),
    SHOPIFY_MODIFY_MUST_HAS_GID("015002"),
    SHOPIFY_REQUEST_ERROR("015003"),
    SHOPIFY_INVOKE_API_TIMEOUT("015004"),
    SHOPIFY_CREATE_ERROR("015005"),

    //Finale
    FINALE_CHANGE_STOCK_FAILED("016001"),
    FINALE_API_ERROR("016002"),
    FINALE_LOGIN_ERROR("016003"),
    FINALE_CREATE_VENDOR_FAILED("016004"),
    FINALE_QUERY_VENDOR_FAILED("016005"),
    FINALE_UPDATE_VENDOR_ITEM_FAILED("016006"),
    FINALE_QUERY_PURCHASE_ORDER_FAILED("016007"),
    FINALE_CREATE_ITEM_FAILED("016008"),
    FINALE_CHECK_SKU_FAILED("016009"),
    FINALE_GET_PRODUCT_FAILED("016010"),
    FINALE_CHANGE_STATUS_FAILED("016011"),
    FINALE_UPDATE_VENDOR_FAILED("016012"),

    //    item sync info
    ITEM_SYNC_INFO_NOT_FOUND("017001"),

    // Merchandise Report
    GENERATE_MERCHANDISE_REPORT_IN_PROGRESS("018001"),
    GET_CHANGE_RESULT_MERCHANDISE_REPORT_FILE_ERROR("018002"),
    FAILED_TO_GENERATE_THE_MERCHANDISE_REPORT("018003"),

    //   ItemCostCollection
    VENDOR_PO_INVOICE_ITEM_NOT_FOUND("019001"),
    ITEM_COST_COLLECTION_NOT_FOUND("019002"),
    ITEM_COST_COLLECTION_REQUEST_SEARCH_INVALID_FILTER_KEY("019003"),


    // upload file error
    UPLOAD_FILE_ERROR("021001"),

    // AWS Analyze Expense Error
    AWS_ANALYZE_EXPENSE_ERROR("022001"),
    NOT_FOUND_AWS_ANALYZE_EXPENSE_RECORD("022002"),
    REQUIRED_MATCHED_ID_IS_EMPTY("022003"),

    //downey
    INVOKE_DOWNEY_API_ERROR("023001"),

    INITIALIZE_GOOGLE_SHEETS_ERROR("024001"),
    READ_GOOGLE_SHEET_ERROR("024002"),
    NO_SHEETS_FOUND_IN_SPREADSHEET("024003"),
    GET_FIRST_SHEET_NAME_ERROR("024004"),


    VENDOR_PO_ANALYZE_RECORD_NOT_FOUND("024001"),

    //vernon
    VERNOR_API_ERROR("025001"),

    //Item price group
    ITEM_PRICE_GROUP_NOT_FOUND("026001"),
    ITEM_PRICE_GROUP_ALREADY_EXISTS("026002"),

    //exception record group
    EXCEPTION_RECORD_NOT_FOUND("027001"),
    UNSUPPORTED_EXCEPTION_RECORD_STATUS("027002"),

    //    ITEM VERSION
    ITEM_VERSION_NOT_FOUND("028001"),
    ITEM_VERSION_DUPLICATE_KEY("028002"),

    //    Bulk Export Records
    BULK_EXPORT_RECORDS_NOT_FOUND("029001"),

    // DIFY
    DIFY_API_CALL_FAILED("030001"),
    DIFY_RESPONSE_BODY_NULL("030002"),
    GET_RECOMMENDED_CATEGORIES_ERROR("030003"),
    DIFY_SEGMENT_ID_IS_NULL("030004"),

    //OpenAI
    OPEN_AI_ERROR("040001"),

    //Vendor Rebate
    HAS_OVERLAPPING_REBATES("050001"),
    VENDOR_REBATE_NOT_FOUND("050002"),

    //Phone Number
    PHONE_NUMBER_NOT_FOUND("051001"),

    //Email
    EMAIL_NOT_FOUND("051002"),

    //Address
    ADDRESS_NOT_FOUND("051003"),


    //Unexpected error
    UNEXPECTED_ERROR("020001");


    private String code;

    ErrorCodeEnums(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
