package com.mercaso.ims.infrastructure.external.finale.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.mercaso.ims.infrastructure.util.FinaleOffsetDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonPropertyOrder({"facilityUrl", "physicalInventoryUrl", "physicalInventoryDate", "physicalInventoryTypeId", "inventoryItemVarianceList", "generalComments", "statusId"})
public class StockChangeRequestDto {
    @JsonProperty("facilityUrl")
    private String facilityUrl;
    @JsonProperty("physicalInventoryUrl")
    private String physicalInventoryUrl;
    @JsonSerialize(using = FinaleOffsetDateTimeSerializer.class)
    @JsonProperty("physicalInventoryDate")
    private OffsetDateTime physicalInventoryDate;
    @JsonProperty("physicalInventoryTypeId")
    private String physicalInventoryTypeId;
    @JsonProperty("inventoryItemVarianceList")
    private List<InventoryItemVarianceDto> inventoryItemVarianceList;
    @JsonProperty("generalComments")
    private String generalComments;
    @JsonProperty("statusId")
    private String statusId;


}