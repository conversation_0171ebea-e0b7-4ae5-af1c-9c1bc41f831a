package com.mercaso.ims.infrastructure.repository.location.jpa;

import com.mercaso.ims.infrastructure.repository.location.jpa.dataobject.LocationDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface LocationJpaDao extends JpaRepository<LocationDo, UUID> {

    List<LocationDo> findByCompanyUUID(UUID companyUUID);

    LocationDo findByLocationId(Long locationId);
}
