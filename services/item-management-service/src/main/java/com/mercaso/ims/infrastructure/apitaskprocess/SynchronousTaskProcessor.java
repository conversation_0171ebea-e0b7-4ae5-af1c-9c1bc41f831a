package com.mercaso.ims.infrastructure.apitaskprocess;

import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.github.resilience4j.ratelimiter.RequestNotPermitted;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import com.mercaso.ims.infrastructure.apitaskprocess.payload.TaskExecutionContextPayload;



/**
 * Processor for synchronous task execution with rate limiting
 * Handles direct method execution with resilience4j rate limiting
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SynchronousTaskProcessor {

    private final RateLimiterRegistry rateLimiterRegistry;

    /**
     * Execute a task synchronously with rate limiting
     *
     * @param context The task execution context
     * @return The result of the method execution
     * @throws Throwable If execution fails
     */
    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public Object executeTask(TaskExecutionContextPayload context) throws Throwable {
        log.info("Applying direct rate limiting for synchronous method: {}", context.getFullMethodName());

        RateLimiter rateLimiter = rateLimiterRegistry.rateLimiter(context.getRateLimiterName());

        try {
            // Apply rate limiting and execute directly
            return rateLimiter.executeSupplier(
                    () -> {
                        try {
                            log.info(
                                    "Executing rate-limited synchronous method: {}", context.getFullMethodName());
                            return context.executeOriginalMethod();
                        } catch (Throwable e) {
                            throw new ImsBusinessException(
                                    "Error executing synchronous method: " + context.getFullMethodName(), e);
                        }
                    });
        } catch (RequestNotPermitted e) {
            log.warn("Rate limit exceeded for synchronous method {}, failing fast to prevent thread blocking", context.getFullMethodName());
            return handleRateLimitExceeded(context, e);
        }
    }

    /**
     * Handle rate limit exceeded scenario
     * For synchronous processing, we fail fast instead of blocking the thread
     */
    private Object handleRateLimitExceeded(TaskExecutionContextPayload context, RequestNotPermitted originalException) {
        // For synchronous processing, we don't want to block threads with delays
        // Instead, we fail fast and let the caller (or @Retryable annotation) handle retries
        throw new ImsBusinessException("Rate limit exceeded for method: " + context.getFullMethodName() +
                ", request rejected to prevent thread blocking in synchronous processing", originalException);
    }
}
