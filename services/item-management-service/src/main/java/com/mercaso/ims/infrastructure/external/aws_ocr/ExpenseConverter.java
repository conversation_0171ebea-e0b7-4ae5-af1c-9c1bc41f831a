package com.mercaso.ims.infrastructure.external.aws_ocr;

import com.mercaso.ims.infrastructure.external.aws_ocr.dto.AnalyzeExpenseResponseDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.BlockDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.BoundingBoxDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.DocumentMetadataDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.ExpenseCurrencyDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.ExpenseDocumentDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.ExpenseFieldDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.ExpenseGroupPropertyDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.ExpenseTypeDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.GeometryDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.LabelDetectionDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.LineItemDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.LineItemGroupDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.PointDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.RelationshipDTO;
import com.mercaso.ims.infrastructure.external.aws_ocr.dto.ValueDetectionDTO;
import java.util.List;
import software.amazon.awssdk.services.textract.model.Block;
import software.amazon.awssdk.services.textract.model.BoundingBox;
import software.amazon.awssdk.services.textract.model.DocumentMetadata;
import software.amazon.awssdk.services.textract.model.ExpenseCurrency;
import software.amazon.awssdk.services.textract.model.ExpenseDetection;
import software.amazon.awssdk.services.textract.model.ExpenseDocument;
import software.amazon.awssdk.services.textract.model.ExpenseField;
import software.amazon.awssdk.services.textract.model.ExpenseGroupProperty;
import software.amazon.awssdk.services.textract.model.ExpenseType;
import software.amazon.awssdk.services.textract.model.Geometry;
import software.amazon.awssdk.services.textract.model.GetExpenseAnalysisResponse;
import software.amazon.awssdk.services.textract.model.LineItemFields;
import software.amazon.awssdk.services.textract.model.LineItemGroup;
import software.amazon.awssdk.services.textract.model.Point;
import software.amazon.awssdk.services.textract.model.Relationship;

public class ExpenseConverter {

    private ExpenseConverter() {
        throw new UnsupportedOperationException("Utility class");
    }

    public static AnalyzeExpenseResponseDTO convert(GetExpenseAnalysisResponse source) {
        AnalyzeExpenseResponseDTO dto = new AnalyzeExpenseResponseDTO();
        dto.setDocumentMetadata(convertDocumentMetadata(source.documentMetadata()));
        dto.setExpenseDocuments(convertExpenseDocuments(source.expenseDocuments()));
        return dto;
    }

    private static DocumentMetadataDTO convertDocumentMetadata(DocumentMetadata source) {
        if (source == null) {
            return null;
        }

        DocumentMetadataDTO dto = new DocumentMetadataDTO();
        dto.setPages(source.pages());
        return dto;
    }

    private static List<ExpenseDocumentDTO> convertExpenseDocuments(List<ExpenseDocument> sources) {
        return safeStream(sources)
            .map(source -> {
                ExpenseDocumentDTO dto = new ExpenseDocumentDTO();
                dto.setSummaryFields(convertExpenseFields(source.summaryFields()));
                dto.setLineItemGroups(convertLineItemGroups(source.lineItemGroups()));
                dto.setBlocks(convertBlocks(source.blocks()));

                return dto;
            })
            .toList();
    }

    private static List<BlockDTO> convertBlocks(List<Block> sources) {
        return safeStream(sources)
            .map(ExpenseConverter::convertBlock)
            .toList();
    }

    private static BlockDTO convertBlock(Block source) {
        BlockDTO dto = new BlockDTO();
        dto.setBlockType(source.blockType().name());
        dto.setText(source.text());
        dto.setPage(source.page());
        dto.setConfidence(source.confidence());
        dto.setGeometry(convertGeometry(source.geometry()));
        dto.setRelationships(convertRelationships(source.relationships()));
        return dto;
    }

    private static List<RelationshipDTO> convertRelationships(List<Relationship> sources) {
        return safeStream(sources)
            .map(ExpenseConverter::convertRelationship)
            .toList();
    }

    private static RelationshipDTO convertRelationship(Relationship source) {
        RelationshipDTO dto = new RelationshipDTO();
        dto.setType(source.type().name());
        dto.setIds(source.ids());
        return dto;
    }

    private static List<LineItemGroupDTO> convertLineItemGroups(List<LineItemGroup> sources) {
        return safeStream(sources)
            .map(source -> {
                LineItemGroupDTO dto = new LineItemGroupDTO();
                dto.setLineItems(convertLineItems(source.lineItems()));
                return dto;
            })
            .toList();
    }

    private static List<LineItemDTO> convertLineItems(List<LineItemFields> sources) {
        return safeStream(sources)
            .map(source -> {
                LineItemDTO dto = new LineItemDTO();
                dto.setLineItemExpenseFields(convertExpenseFields(source.lineItemExpenseFields()));
                return dto;
            })
            .toList();
    }

    private static List<ExpenseFieldDTO> convertExpenseFields(List<ExpenseField> sources) {
        return safeStream(sources)
            .map(source -> {
                ExpenseFieldDTO dto = new ExpenseFieldDTO();
                dto.setType(convertExpenseType(source.type()));
                dto.setLabelDetection(convertLabelDetection(source.labelDetection()));
                dto.setValueDetection(convertValueDetection(source.valueDetection()));
                dto.setPageNumber(source.pageNumber());
                dto.setCurrency(convertExpenseCurrency(source.currency()));
                dto.setGroupProperties(convertGroupProperties(source.groupProperties()));
                return dto;
            })
            .toList();
    }

    private static List<ExpenseGroupPropertyDTO> convertGroupProperties(List<ExpenseGroupProperty> sources) {
        return safeStream(sources)
            .map(ExpenseConverter::convertExpenseGroupProperty)
            .toList();
    }

    private static ExpenseGroupPropertyDTO convertExpenseGroupProperty(ExpenseGroupProperty source) {
        if (source == null) {
            return null;
        }

        ExpenseGroupPropertyDTO dto = new ExpenseGroupPropertyDTO();
        dto.setTypes(source.types());
        dto.setId(source.id());
        return dto;
    }

    private static ExpenseCurrencyDTO convertExpenseCurrency(ExpenseCurrency source) {
        if (source == null) {
            return null;
        }

        ExpenseCurrencyDTO dto = new ExpenseCurrencyDTO();
        dto.setCode(source.code());
        dto.setConfidence(source.confidence());
        return dto;
    }

    private static ExpenseTypeDTO convertExpenseType(ExpenseType source) {
        if (source == null) {
            return null;
        }

        ExpenseTypeDTO dto = new ExpenseTypeDTO();
        dto.setText(source.text());
        dto.setConfidence(source.confidence());
        return dto;
    }

    private static LabelDetectionDTO convertLabelDetection(ExpenseDetection source) {
        if (source == null) {
            return null;
        }

        LabelDetectionDTO dto = new LabelDetectionDTO();
        dto.setText(source.text());
        dto.setConfidence(source.confidence());

        Geometry awsGeometry = source.geometry();
        if (awsGeometry != null) {
            dto.setGeometry(convertGeometry(awsGeometry));
        }

        return dto;
    }

    private static ValueDetectionDTO convertValueDetection(ExpenseDetection source) {
        if (source == null) {
            return null;
        }

        ValueDetectionDTO dto = new ValueDetectionDTO();
        dto.setText(source.text());
        dto.setConfidence(source.confidence());
        dto.setGeometry(convertGeometry(source.geometry()));
        return dto;
    }

    private static BoundingBoxDTO convertBoundingBox(BoundingBox source) {
        if (source == null) {
            return null;
        }

        BoundingBoxDTO dto = new BoundingBoxDTO();
        dto.setWidth(source.width());
        dto.setHeight(source.height());
        dto.setLeft(source.left());
        dto.setTop(source.top());
        return dto;
    }


    private static GeometryDTO convertGeometry(Geometry source) {
        if (source == null) {
            return null;
        }

        GeometryDTO dto = new GeometryDTO();
        dto.setBoundingBox(convertBoundingBox(source.boundingBox()));
        dto.setPolygon(convertPoints(source.polygon()));
        return dto;
    }

    private static List<PointDTO> convertPoints(List<Point> sources) {
        return safeStream(sources)
            .map(ExpenseConverter::convertPoint)
            .toList();
    }

    private static PointDTO convertPoint(Point source) {
        if (source == null) {
            return null;
        }

        PointDTO dto = new PointDTO();
        dto.setX(source.x());
        dto.setY(source.y());
        return dto;
    }

    private static <T> java.util.stream.Stream<T> safeStream(List<T> list) {
        return list != null ? list.stream() : java.util.stream.Stream.empty();
    }
}