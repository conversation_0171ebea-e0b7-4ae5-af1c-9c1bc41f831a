package com.mercaso.ims.infrastructure.external.shopify.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
public class ShopifyPublicationChannelGraphQLDto {

    private Long shopifyProductId;

    private String channelId;


    public String buildPublicationChannelQraphQL() {
        String query = "{"
            + "    \"query\": \"mutation publishablePublish($id: ID!, $input: [PublicationInput!]!) { publishablePublish(id: $id, input: $input) { publishable { availablePublicationsCount { count } resourcePublicationsCount { count } } shop { publicationCount } userErrors { field message } } }\","
            + "    \"variables\": {"
            + "        \"id\": \"gid://shopify/Product/" + shopifyProductId + "\","
            + "        \"input\": {"
            + "            \"publicationId\": \"" + channelId + "\""
            + "        }"
            + "    }"
            + "}";

        log.info("[buildPublicationChannelQraphQL] query: {}", query);

        return query;
    }

}
