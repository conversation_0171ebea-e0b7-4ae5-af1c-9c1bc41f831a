package com.mercaso.ims.infrastructure.repository.businessevententity.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "business_event_entity")
@SQLDelete(sql = "update business_event_entity set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class BusinessEventEntityDo extends BaseDo {

    @Column(name = "business_event_id", nullable = false)
    private UUID businessEventId;

    @Column(name = "entity_id", nullable = false)
    private UUID entityId;

    @Column(name = "entity_type", nullable = false)
    private String entityType;

}
