package com.mercaso.ims.infrastructure.repository.businessevent;

import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.ims.domain.businessevent.BusinessEvent;
import com.mercaso.ims.domain.businessevent.BusinessEventRepository;
import com.mercaso.ims.domain.businessevent.enums.EntityEnums;
import com.mercaso.ims.infrastructure.repository.businessevent.jpa.BusinessEventJpaDao;
import com.mercaso.ims.infrastructure.repository.businessevent.jpa.dataobject.BusinessEventDo;
import com.mercaso.ims.infrastructure.repository.businessevent.jpa.mapper.BusinessEventDoMapper;
import com.mercaso.ims.infrastructure.repository.businessevententity.jpa.BusinessEventEntityJpaDao;
import com.mercaso.ims.infrastructure.repository.businessevententity.jpa.dataobject.BusinessEventEntityDo;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import java.util.Collections;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class BusinessEventRepositoryImpl implements BusinessEventRepository {

    private static final String SPAN_OPERATION_NAME = "dispatchEvent";

    private final BusinessEventDoMapper businessEventDoMapper;

    private final BusinessEventJpaDao businessEventJpaDao;

    private final BusinessEventEntityJpaDao businessEventEntityJpaDao;

    @Override
    public BusinessEvent save(BusinessEvent domain) {
        BusinessEventDo businessEventDo = businessEventDoMapper.domainToDo(domain);
        businessEventDo.setCorrelationId(getCorrelationId());
        businessEventDo = businessEventJpaDao.save(businessEventDo);

        Map<EntityEnums, List<UUID>> relatedEntities = this.getRelatedEntities(domain);
        for (Map.Entry<EntityEnums, List<UUID>> entity : relatedEntities.entrySet()) {
            for (UUID entityId : entity.getValue()) {
                BusinessEventEntityDo entityDo = BusinessEventEntityDo.builder()
                    .businessEventId(businessEventDo.getId())
                    .entityId(entityId)
                    .entityType(entity.getKey().getValue())
                    .createdAt(businessEventDo.getCreatedAt())
                    .createdBy(businessEventDo.getCreatedBy())
                    .updatedAt(businessEventDo.getUpdatedAt())
                    .updatedBy(businessEventDo.getUpdatedBy())
                    .createdUserName(businessEventDo.getCreatedUserName())
                    .updatedUserName(businessEventDo.getUpdatedUserName())
                    .deletedUserName(businessEventDo.getDeletedUserName())
                    .build();
                businessEventEntityJpaDao.save(entityDo);
            }
        }

        return businessEventDoMapper.doToDomain(businessEventDo);
    }

    @Override
    public BusinessEvent findById(UUID id) {
        BusinessEventDo businessEventDo = businessEventJpaDao.findById(id).orElse(null);
        return businessEventDoMapper.doToDomain(businessEventDo);
    }

    @Override
    public BusinessEvent update(BusinessEvent domain) {
        return null;
    }

    @Override
    public BusinessEvent deleteById(UUID id) {
        log.debug("delete.business.event.{}", id);
        return null;
    }

    public Map<EntityEnums, List<UUID>> getRelatedEntities(BusinessEvent businessEvent) {
        EnumMap<EntityEnums, List<UUID>> ret = new EnumMap<>(EntityEnums.class);

        JsonNode payload = SerializationUtils.readTree(businessEvent.getPayload());
        JsonNode payoutId = payload.get("testOrderId");
        if (payoutId != null && payoutId.isTextual()) {
            ret.put(EntityEnums.TEST_ORDER, List.of(UUID.fromString(payoutId.asText())));
        }

        JsonNode itemAdjustmentRequestId = payload.get("itemAdjustmentRequestId");
        if (itemAdjustmentRequestId != null && itemAdjustmentRequestId.isTextual()) {
            ret.put(EntityEnums.ITEM_ADJUSTMENT_REQUEST, List.of(UUID.fromString(itemAdjustmentRequestId.asText())));
        }

        JsonNode itemAdjustmentRequestDetailId = payload.get("itemAdjustmentRequestDetailId");
        if (itemAdjustmentRequestDetailId != null && itemAdjustmentRequestDetailId.isTextual()) {
            ret.put(EntityEnums.ITEM_ADJUSTMENT_REQUEST_DETAIL, List.of(UUID.fromString(itemAdjustmentRequestDetailId.asText())));
        }

        JsonNode itemId = payload.get("itemId");
        if (itemId != null && itemId.isTextual()) {
            ret.put(EntityEnums.ITEM, List.of(UUID.fromString(itemId.asText())));
        }

        JsonNode itemCostCollectionId = payload.get("itemCostCollectionId");
        if (itemCostCollectionId != null && itemCostCollectionId.isTextual()) {
            ret.put(EntityEnums.ITEM_COST_COLLECTION, List.of(UUID.fromString(itemCostCollectionId.asText())));
        }

        JsonNode itemCostChangeRequestId = payload.get("itemCostChangeRequestId");
        if (itemCostChangeRequestId != null && itemCostChangeRequestId.isTextual()) {
            ret.put(EntityEnums.ITEM_COST_CHANGE_REQUEST, List.of(UUID.fromString(itemCostChangeRequestId.asText())));
        }

        JsonNode vendorPoAnalyzeRecordId = payload.get("vendorPoAnalyzeRecordId");
        if (vendorPoAnalyzeRecordId != null && vendorPoAnalyzeRecordId.isTextual()) {
            ret.put(EntityEnums.ANALYZE_VENDOR_PO_RECORD, List.of(UUID.fromString(vendorPoAnalyzeRecordId.asText())));
        }
        JsonNode vendorItemId = payload.get("vendorItemId");
        if (vendorItemId != null && vendorItemId.isTextual()) {
            ret.put(EntityEnums.VENDOR_ITEM, List.of(UUID.fromString(vendorItemId.asText())));
        }

        JsonNode itemPriceGroupId = payload.get("itemPriceGroupId");
        if (itemPriceGroupId != null && itemPriceGroupId.isTextual()) {
            ret.put(EntityEnums.ITEM_PRICE_GROUP, List.of(UUID.fromString(itemPriceGroupId.asText())));
        }

        JsonNode categoryId = payload.get("categoryId");
        if (categoryId != null && categoryId.isTextual()) {
            ret.put(EntityEnums.CATEGORY, List.of(UUID.fromString(categoryId.asText())));
        }

        JsonNode exceptionRecordId = payload.get("exceptionRecordId");
        if (exceptionRecordId != null && exceptionRecordId.isTextual()) {
            ret.put(EntityEnums.EXCEPTION_RECORD, List.of(UUID.fromString(exceptionRecordId.asText())));
        }

        JsonNode bulkExportRecordsId = payload.get("bulkExportRecordsId");
        if (bulkExportRecordsId != null && bulkExportRecordsId.isTextual()) {
            ret.put(EntityEnums.BULK_EXPORT_RECORDS, List.of(UUID.fromString(bulkExportRecordsId.asText())));
        }

        JsonNode brandId = payload.get("brandId");
        if (brandId != null && brandId.isTextual()) {
            ret.put(EntityEnums.BRAND, List.of(UUID.fromString(brandId.asText())));
        }

        JsonNode vendorId = payload.get("vendorId");
        if (vendorId != null && vendorId.isTextual()) {
            ret.put(EntityEnums.VENDOR, List.of(UUID.fromString(vendorId.asText())));
        }
        return ret;
    }

    private String getCorrelationId() {
        return UUID.randomUUID().toString();
    }

    @Override
    public List<BusinessEvent> findByEntityIdAndType(UUID entityId, String entityType) {
        List<UUID> businessEventIds = Optional.ofNullable(businessEventEntityJpaDao.findByEntityIdAndEntityType(entityId,
                entityType))
            .orElse(Collections.emptyList()).stream().map(BusinessEventEntityDo::getBusinessEventId).distinct().toList();

        return Optional.ofNullable(businessEventJpaDao.findByIdInOrderByCreatedAtDesc(businessEventIds))
            .orElse(Collections.emptyList())
            .stream()
            .map(businessEventDoMapper::doToDomain)
            .toList();
    }

    @Override
    public List<BusinessEvent> findByIdInAndOrderByCreatedAt(List<UUID> businessEventIds) {
        return Optional.ofNullable(businessEventJpaDao.findByIdInOrderByCreatedAtDesc(businessEventIds))
            .orElse(Collections.emptyList())
            .stream()
            .map(businessEventDoMapper::doToDomain)
            .toList();
    }
}
