package com.mercaso.ims.infrastructure.repository.vendoritem.mapper;

import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshot;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemAvailabilitySnapshotDo;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface VendorItemAvailabilitySnapshotMapper extends BaseDoMapper<VendorItemAvailabilitySnapshotDo, VendorItemAvailabilitySnapshot> {

} 