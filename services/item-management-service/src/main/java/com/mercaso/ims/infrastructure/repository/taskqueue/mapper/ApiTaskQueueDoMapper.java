package com.mercaso.ims.infrastructure.repository.taskqueue.mapper;

import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.infrastructure.repository.taskqueue.jpa.dataobject.ApiTaskQueueDo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

/**
 * Mapper for converting between ApiTaskQueue domain objects and ApiTaskQueueDo data objects
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ApiTaskQueueDoMapper {

    /**
     * Convert domain object to data object
     *
     * @param domain Domain object
     * @return Data object
     */
    @Mapping(target = "createdUserName", ignore = true)
    @Mapping(target = "updatedUserName", ignore = true)
    @Mapping(target = "deletedUserName", ignore = true)
    ApiTaskQueueDo domainToDo(ApiTaskQueue domain);

    /**
     * Convert data object to domain object
     *
     * @param dataObject Data object
     * @return Domain object
     */
    ApiTaskQueue doToDomain(ApiTaskQueueDo dataObject);
}
