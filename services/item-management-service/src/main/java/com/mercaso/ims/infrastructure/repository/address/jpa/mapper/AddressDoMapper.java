package com.mercaso.ims.infrastructure.repository.address.jpa.mapper;

import com.mercaso.ims.domain.BaseValueObjectDoMapper;
import com.mercaso.ims.domain.address.Address;
import com.mercaso.ims.infrastructure.repository.address.jpa.dataobject.AddressDo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface AddressDoMapper extends BaseValueObjectDoMapper<AddressDo, Address> {

    AddressDoMapper INSTANCE = Mappers.getMapper(AddressDoMapper.class);

    @Override
    Address doToDomain(AddressDo addressDo);

    @Override
    AddressDo domainToDo(Address address);
}
