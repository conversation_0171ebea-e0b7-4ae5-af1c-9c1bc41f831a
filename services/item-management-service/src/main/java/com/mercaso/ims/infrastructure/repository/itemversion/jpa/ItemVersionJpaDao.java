package com.mercaso.ims.infrastructure.repository.itemversion.jpa;

import com.mercaso.ims.infrastructure.repository.itemversion.jpa.dataobject.ItemVersionDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ItemVersionJpaDao extends JpaRepository<ItemVersionDo, UUID> {

    ItemVersionDo findBySkuNumberAndVersionNumber(String skuNumber, Integer versionNumber);

    ItemVersionDo findByItemIdAndVersionNumber(UUID itemId, Integer versionNumber);

    List<ItemVersionDo> findByItemId(UUID itemId);
}
