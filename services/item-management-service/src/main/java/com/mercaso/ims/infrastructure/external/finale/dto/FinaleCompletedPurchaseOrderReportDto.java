package com.mercaso.ims.infrastructure.external.finale.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO representing a completed purchase order item from Finale's pivot table report
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinaleCompletedPurchaseOrderReportDto {

    @JsonProperty("Order ID")
    private String orderId;

    @JsonProperty("Status")
    private String status;

    @JsonProperty("Order date")
    private String orderDate;

    @JsonProperty("Appt Date")
    private String apptDate;

    @JsonProperty("Appt Time")
    private String apptTime;

    @JsonProperty("Record last updated")
    private String recordLastUpdated;

    @JsonProperty("Record last updated user")
    private String recordLastUpdatedUser;

    @JsonProperty("Due date")
    private String dueDate;

    @JsonProperty("Customer")
    private String customer;

    @JsonProperty("Supplier party ID")
    private String supplierPartyId;

    @JsonProperty("Supplier")
    private String supplier;

    @JsonProperty("Supplier product ID")
    private String supplierProductId;

    @JsonProperty("Product ID")
    private String productId;

    @JsonProperty("Packing")
    private String packing;

    @JsonProperty("Price per unit")
    private Double pricePerUnit;

    @JsonProperty("Amount")
    private Double amount;

    @JsonProperty("Product units ordered")
    private String productUnitsOrdered;

    @JsonProperty("Description")
    private String description;

    @JsonProperty("Shipments status summary")
    private String shipmentsStatusSummary;

    @JsonProperty("Shipments summary")
    private String shipmentsSummary;

    @JsonProperty("Type")
    private String type;
}
