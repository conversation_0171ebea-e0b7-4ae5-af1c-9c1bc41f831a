package com.mercaso.ims.infrastructure.external.finale.dto;

import lombok.*;

import java.math.BigDecimal;


@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StockDto {
    private Data data;


    public static class Data {

        private Product product;

        public Product getProduct() {
            return product;
        }

        public void setProduct(Product product) {
            this.product = product;
        }


        public static class Product {

            private String stockOnHand;
            private String stockOnOrder;
            private String stockReserved;
            private BigDecimal valuation;


            public String getStockOnHand() {
                return stockOnHand;
            }

            public void setStockOnHand(String stockOnHand) {
                this.stockOnHand = stockOnHand;
            }

            public String getStockOnOrder() {
                return stockOnOrder;
            }

            public void setStockOnOrder(String stockOnOrder) {
                this.stockOnOrder = stockOnOrder;
            }

            public String getStockReserved() {
                return stockReserved;
            }

            public void setStockReserved(String stockReserved) {
                this.stockReserved = stockReserved;
            }

            public BigDecimal getValuation() {
                return valuation;
            }

            public void setValuation(BigDecimal valuation) {
                this.valuation = valuation;
            }
        }
    }

}
