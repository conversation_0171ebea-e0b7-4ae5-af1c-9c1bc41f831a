package com.mercaso.ims.infrastructure.repository.vendor;

import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.VendorJpaDao;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.dataobject.VendorDo;
import com.mercaso.ims.infrastructure.repository.vendor.jpa.mapper.VendorDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class VendorRepositoryImpl implements VendorRepository {

    private final VendorDoMapper vendorDoMapper;

    private final VendorJpaDao vendorJpaDao;


    @Override
    public Vendor save(Vendor vendor) {
        VendorDo vendorDo = vendorDoMapper.domainToDo(vendor);
        vendorDo = vendorJpaDao.save(vendorDo);
        return vendorDoMapper.doToDomain(vendorDo);
    }

    @Override
    public Vendor findById(UUID id) {
        VendorDo vendorDo = vendorJpaDao.findById(id).orElse(null);
        return vendorDoMapper.doToDomain(vendorDo);
    }

    @Override
    public Vendor update(Vendor vendor) {
        VendorDo vendorDo = vendorJpaDao.findById(vendor.getId()).orElse(null);
        if (Objects.isNull(vendorDo)) {
            throw new ImsBusinessException(ErrorCodeEnums.VENDOR_NOT_FOUND.getCode());
        }
        VendorDo vendorDoTarget = vendorDoMapper.domainToDo(vendor);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(vendorDoTarget, vendorDo, ignoreProperties.toArray(new String[0]));
        VendorDo result = vendorJpaDao.save(vendorDo);
        return vendorDoMapper.doToDomain(result);
    }

    @Override
    public Vendor deleteById(UUID id) {
        VendorDo vendorDo = vendorJpaDao.findById(id).orElse(null);
        if (null == vendorDo) {
            return null;
        }
        vendorDo.setDeletedAt(Instant.now());
        vendorDo.setDeletedBy(SecurityUtil.getLoginUserId());
        return vendorDoMapper.doToDomain(vendorJpaDao.save(vendorDo));
    }

    @Override
    public Vendor findByVendorName(String vendorName) {
        VendorDo vendorDo = vendorJpaDao.findByVendorName(vendorName);
        if (null == vendorDo) {
            return null;
        }
        return vendorDoMapper.doToDomain(vendorDo);
    }

    @Override
    public Vendor findByFinaleId(String finaleId) {
        VendorDo vendorDo = vendorJpaDao.findByFinaleId(finaleId);
        if (null == vendorDo) {
            return null;
        }
        return vendorDoMapper.doToDomain(vendorDo);
    }

    @Override
    public List<Vendor> findAllByIdIn(List<UUID> ids) {
        return Optional.ofNullable(vendorJpaDao.findAllByIdIn(ids))
            .orElse(Collections.emptyList())
            .stream()
            .map(vendorDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<Vendor> findAll() {
        return Optional.of(vendorJpaDao.findAll())
            .orElse(Collections.emptyList())
            .stream()
            .map(vendorDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<Vendor> findByFuzzyName(String vendorName) {
        return Optional.of(vendorJpaDao.findByVendorNameContainsIgnoreCase(vendorName))
            .orElse(Collections.emptyList())
            .stream()
            .map(vendorDoMapper::doToDomain)
            .toList();
    }

    @Override
    public List<Vendor> findByExternalPicking(Boolean externalPicking) {
        return Optional.of(vendorJpaDao.findByExternalPicking(externalPicking))
            .orElse(Collections.emptyList())
            .stream()
            .map(vendorDoMapper::doToDomain)
            .toList();
    }

    @Override
    public List<Vendor> findByShutdownWindowEnabled() {
        return Optional.of(vendorJpaDao.findByShutdownWindowEnabledTrue())
            .orElse(Collections.emptyList())
            .stream()
            .map(vendorDoMapper::doToDomain)
            .toList();
    }
}
