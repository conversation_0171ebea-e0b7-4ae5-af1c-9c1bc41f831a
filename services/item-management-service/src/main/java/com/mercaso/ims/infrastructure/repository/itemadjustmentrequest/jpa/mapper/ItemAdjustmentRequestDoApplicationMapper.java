package com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa.mapper;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.mapper.BaseDoApplicationMapper;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa.dataobject.ItemAdjustmentRequestDo;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ItemAdjustmentRequestDoApplicationMapper extends
    BaseDoApplicationMapper<ItemAdjustmentRequestDo, ItemAdjustmentRequestDto> {

    @Override
    ItemAdjustmentRequestDto doToDto(ItemAdjustmentRequestDo itemAdjustmentRequestDo);
}

