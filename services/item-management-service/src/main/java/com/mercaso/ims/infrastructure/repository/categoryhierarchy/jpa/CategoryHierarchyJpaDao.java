package com.mercaso.ims.infrastructure.repository.categoryhierarchy.jpa;

import com.mercaso.ims.infrastructure.repository.categoryhierarchy.jpa.dataobject.CategoryHierarchyDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface CategoryHierarchyJpaDao extends JpaRepository<CategoryHierarchyDo, UUID> {

    List<CategoryHierarchyDo> findByCategoryIdAndDeletedAtIsNullOrderByDepthDesc (UUID categoryId);

    List<CategoryHierarchyDo> findByAncestorCategoryIdAndDeletedAtIsNullOrderByDepthDesc (UUID ancestorCategoryId);

    List<CategoryHierarchyDo> findByDepthAndDeletedAtIsNull (Integer depth);

    CategoryHierarchyDo findByCategoryIdAndAncestorCategoryIdAndDeletedAtIsNull (UUID categoryId, UUID ancestorCategoryId);

    List<CategoryHierarchyDo> findByAncestorCategoryIdAndDepthAndDeletedAtIsNull (UUID ancestorCategoryId, Integer depth);

    List<CategoryHierarchyDo> findByCategoryIdAndDepthAndDeletedAtIsNull (UUID categoryId, Integer depth);

    List<CategoryHierarchyDo> findByCategoryIdInAndDeletedAtIsNullOrderByDepthDesc (List<UUID> categoryIds);



}
