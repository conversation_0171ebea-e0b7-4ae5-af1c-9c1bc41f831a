package com.mercaso.ims.infrastructure.repository.vendoritem.mapper;

import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshotDetail;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemAvailabilitySnapshotDetailDo;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface VendorItemAvailabilitySnapshotDetailMapper extends BaseDoMapper<VendorItemAvailabilitySnapshotDetailDo, VendorItemAvailabilitySnapshotDetail> {

} 