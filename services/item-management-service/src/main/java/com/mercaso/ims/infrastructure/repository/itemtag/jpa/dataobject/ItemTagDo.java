package com.mercaso.ims.infrastructure.repository.itemtag.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;


@Entity
@Table(name = "item_tag")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_tag set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemTagDo extends BaseDo {

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "tag_name")
    private String tagName;
}
