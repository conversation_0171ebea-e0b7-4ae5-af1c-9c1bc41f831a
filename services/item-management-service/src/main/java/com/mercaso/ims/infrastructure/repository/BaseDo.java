package com.mercaso.ims.infrastructure.repository;

import com.mercaso.ims.infrastructure.util.SecurityUtil;
import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Version;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseDo implements Serializable {

    @Id
    @GeneratedValue(generator = "uuid-gen")
    @GenericGenerator(name = "uuid-gen", strategy = "org.hibernate.id.UUIDGenerator")
    private UUID id;

    @Column(name = "created_at", updatable = false)
    @CreatedDate
    @NotNull
    private Instant createdAt;


    @Column(name = "created_by", updatable = false)
    @CreatedBy
    private String createdBy;

    @Version
    @Column(name = "updated_at")
    @LastModifiedDate
    private Instant updatedAt;

    @Column(name = "updated_by")
    @LastModifiedBy
    private String updatedBy;

    @Column(name = "deleted_at")
    private Instant deletedAt;

    @Column(name = "deleted_by")
    private String deletedBy;

    @Column(name = "created_user_name")
    private String createdUserName;

    @Column(name = "updated_user_name")
    private String updatedUserName;

    @Column(name = "deleted_user_name")
    private String deletedUserName;

    @PrePersist
    protected void onCreate() {
        Instant now = Instant.now();
        createdAt = now;
        createdBy = SecurityUtil.getLoginUserId() == null ? SecurityUtil.getSystemUserId() : SecurityUtil.getLoginUserId();
        updatedAt = now;
        updatedBy = SecurityUtil.getLoginUserId() == null ? SecurityUtil.getSystemUserId() : SecurityUtil.getLoginUserId();
        createdUserName = SecurityUtil.getUserName() == null ? SecurityUtil.getSystemUserName() : SecurityUtil.getUserName();
        updatedUserName = SecurityUtil.getUserName() == null ? SecurityUtil.getSystemUserName() : SecurityUtil.getUserName();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
        updatedBy = SecurityUtil.getLoginUserId() == null ? SecurityUtil.getSystemUserId() : SecurityUtil.getLoginUserId();
        updatedUserName = SecurityUtil.getUserName() == null ? SecurityUtil.getSystemUserName() : SecurityUtil.getUserName();
    }
}
