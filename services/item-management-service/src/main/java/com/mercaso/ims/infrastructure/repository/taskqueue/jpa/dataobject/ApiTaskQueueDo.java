package com.mercaso.ims.infrastructure.repository.taskqueue.jpa.dataobject;

import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.Type;

import java.time.Instant;

/**
 * API Task Queue data object for JPA persistence
 */
@Entity
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "api_task_queue")
@SQLDelete(sql = "update api_task_queue set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class ApiTaskQueueDo extends BaseDo {

    /**
     * Type of the task (e.g., FINALE_GET_PRODUCT, FINALE_UPDATE_STOCK)
     */
    @Column(name = "task_type", nullable = false, length = 100)
    private String taskType;

    /**
     * API endpoint URL or identifier
     */
    @Column(name = "api_endpoint", nullable = false, length = 500)
    private String apiEndpoint;

    /**
     * HTTP method (GET, POST, PUT, DELETE, PATCH)
     */
    @Column(name = "http_method", nullable = false, length = 10)
    private String httpMethod;

    /**
     * Request payload in JSON format
     */
    @Column(name = "request_payload", columnDefinition = "jsonb")
    @Type(JsonType.class)
    private String requestPayload;

    /**
     * Response payload in JSON format
     */
    @Column(name = "response_payload", columnDefinition = "jsonb")
    @Type(JsonType.class)
    private String responsePayload;

    /**
     * Current status of the task
     */
    @Column(name = "status", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private TaskStatus status;

    /**
     * Task priority (higher number = higher priority)
     */
    @Column(name = "priority", nullable = false)
    private Integer priority;

    /**
     * Maximum number of retry attempts
     */
    @Column(name = "max_retry_count", nullable = false)
    private Integer maxRetryCount;

    /**
     * Current retry attempt count
     */
    @Column(name = "current_retry_count", nullable = false)
    private Integer currentRetryCount;

    /**
     * Scheduled execution time (for delayed execution or retry)
     */
    @Column(name = "scheduled_at")
    private Instant scheduledAt;

    /**
     * Actual start time of task execution
     */
    @Column(name = "started_at")
    private Instant startedAt;

    /**
     * Task completion time
     */
    @Column(name = "completed_at")
    private Instant completedAt;

    /**
     * Error message if task failed
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
}
