package com.mercaso.ims.infrastructure.external.shopify.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import java.util.List;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Slf4j
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductPublicationResponseDto {

    @JsonProperty("data")
    private ProductData data;

    @Data
    public static class ProductData {
        @JsonProperty("product")
        private Product product;

        @Data
        public static class Product {
            @JsonProperty("resourcePublicationsV2")
            private ResourcePublicationsV2 resourcePublicationsV2;

            @Data
            public static class ResourcePublicationsV2 {
                @JsonProperty("edges")
                private List<Edge> edges;

                @Data
                public static class Edge {
                    @JsonProperty("node")
                    private Node node;

                    @Data
                    public static class Node {
                        @JsonProperty("isPublished")
                        private boolean isPublished;
                        @JsonProperty("publication")
                        private Publication publication;

                        @Data
                        public static class Publication {
                            @JsonProperty("id")
                            private String id;
                            @JsonProperty("name")
                            private String name;
                        }
                    }
                }
            }
        }
    }
}