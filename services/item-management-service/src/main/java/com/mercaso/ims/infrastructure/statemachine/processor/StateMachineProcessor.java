package com.mercaso.ims.infrastructure.statemachine.processor;


import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.statemachine.StateTransitionType;
import com.mercaso.ims.infrastructure.statemachine.StateType;
import com.mercaso.ims.infrastructure.statemachine.StatefulContext;
import com.mercaso.ims.infrastructure.statemachine.StatemachineFactory;
import com.mercaso.ims.infrastructure.statemachine.factory.ImsStateMachineFactory;
import com.mercaso.ims.infrastructure.util.SpringContextUtil;

public interface StateMachineProcessor<T extends StatefulContext<S>, S extends StateType, E extends StateTransitionType> {
    void processEvent(T domain, E event);

    default ImsStateMachineFactory<S, E, T> getStateMachineFactory(T domain) {
        String[] statemachineFactories = SpringContextUtil.getBeansWithAnnotation(StatemachineFactory.class);

        for (String beanName : statemachineFactories) {
            StatemachineFactory statemachineFactory = SpringContextUtil.findAnnotationOnBean(beanName,
                    StatemachineFactory.class);
            if (domain.getClass().isAssignableFrom(statemachineFactory.domainClass())) {
                return SpringContextUtil.getBean(beanName);
            }
        }
        throw new ImsBusinessException("Unsupported statemachine factory");
    }
}
