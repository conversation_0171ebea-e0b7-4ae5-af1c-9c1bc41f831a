package com.mercaso.ims.infrastructure.repository.businessevent;

import com.mercaso.ims.application.dto.BusinessEventDto;
import com.mercaso.ims.application.query.BusinessEventQuery;
import com.mercaso.ims.infrastructure.repository.DynamicSearchCondition;
import com.mercaso.ims.infrastructure.repository.SearchConditionResolver;
import com.mercaso.ims.infrastructure.util.CollectionUtils;
import com.mercaso.ims.infrastructure.util.DateUtils;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedBusinessEventJapDaoImpl implements CustomizedBusinessEventJapDao {

    private static final String BUSINESS_EVENT_QUERY = """
              select e.id, e.type, e.payload, b.*
              from business_event_entity b
                       left join business_event e
                                 on b.business_event_id = e.id::uuid
              where 1 = 1
        """;
    private final JdbcTemplate jdbcTemplate;

    @Override
    public List<BusinessEventDto> getBusinessEventDtoList(BusinessEventQuery businessEventQuery) {
        BusinessEventDynamicSearch search = new BusinessEventDynamicSearch();

        StringBuilder sql = new StringBuilder(BUSINESS_EVENT_QUERY);
        sql.append(search.generateConditionBlock(businessEventQuery));
        sql.append("  order by b.created_at desc");

        return jdbcTemplate.query(sql.toString(),
            ps -> search.bindSqlParameter(ps, businessEventQuery),
            (rs, rowNumber) -> BusinessEventDto.builder()
                .id(UUID.fromString(rs.getString("id")))
                .type(rs.getString("type"))
                .payload(SerializationUtils.toTree(rs.getString("payload")))
                .entityId(UUID.fromString(rs.getString("entity_id")))
                .entityType(rs.getString("entity_type"))
                .createdAt(DateUtils.fromTimestamp(rs.getTimestamp("created_at")))
                .createdBy(rs.getString("created_by"))
                .build());

    }


    private static class BusinessEventDynamicSearch extends DynamicSearchCondition<BusinessEventQuery> {

        public BusinessEventDynamicSearch() {
            super(List.of(new TypeCondition(), new EntityIdsCondition()));
        }
    }

    private static class TypeCondition implements SearchConditionResolver<BusinessEventQuery> {

        @Override
        public String generateConditionBlock(BusinessEventQuery query) {
            if (StringUtils.isNotBlank(query.getType())) {
                return " and e.type=? ";
            }
            return StringUtils.EMPTY;
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, BusinessEventQuery query, int index) throws SQLException {
            if (StringUtils.isNotBlank(query.getType())) {
                ps.setString(index++, query.getType());
            }
            return index;
        }
    }

    private static class EntityIdsCondition implements SearchConditionResolver<BusinessEventQuery> {

        @Override
        public String generateConditionBlock(BusinessEventQuery query) {
            Set<UUID> set = query.getEntityIds().stream().filter(Objects::nonNull).collect(Collectors.toSet());

            if (CollectionUtils.isNotEmpty(set)) {
                return " and b.entity_id in (" + StringUtils.substringBeforeLast(" ?, ".repeat(set.size()), ",") + ") ";
            }
            return StringUtils.EMPTY;
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, BusinessEventQuery query, int index) throws SQLException {
            Set<UUID> set = query.getEntityIds().stream().filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(set)) {
                for (UUID id : set) {
                    ps.setObject(index++, id);
                }
            }
            return index;
        }
    }
}
