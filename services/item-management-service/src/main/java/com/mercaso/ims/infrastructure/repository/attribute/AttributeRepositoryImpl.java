package com.mercaso.ims.infrastructure.repository.attribute;

import com.mercaso.ims.domain.attribute.Attribute;
import com.mercaso.ims.domain.attribute.AttributeRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.attribute.jpa.AttributeJpaDao;
import com.mercaso.ims.infrastructure.repository.attribute.jpa.dataobject.AttributeDo;
import com.mercaso.ims.infrastructure.repository.attribute.jpa.mapper.AttributeDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class AttributeRepositoryImpl implements AttributeRepository {

    public final AttributeDoMapper attributeDoMapper;
    private final AttributeJpaDao attributeJpaDao;

    @Override
    public Attribute save(Attribute domain) {
        AttributeDo attributeDo = attributeDoMapper.domainToDo(domain);
        attributeDo = attributeJpaDao.save(attributeDo);
        return attributeDoMapper.doToDomain(attributeDo);
    }

    @Override
    public Attribute findById(UUID id) {
        return attributeDoMapper.doToDomain(attributeJpaDao.findById(id).orElse(null));
    }

    @Override
    public Attribute update(Attribute domain) {
        AttributeDo attributeDo = attributeJpaDao.findById(domain.getId()).orElse(null);
        if (null == attributeDo) {
            throw new ImsBusinessException(ErrorCodeEnums.ATTRIBUTE_NOT_FOUND.getCode());
        }
        AttributeDo target = attributeDoMapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
                "createdAt"));
        BeanUtils.copyProperties(target, attributeDo, ignoreProperties.toArray(new String[0]));
        attributeDo = attributeJpaDao.save(attributeDo);
        return attributeDoMapper.doToDomain(attributeDo);
    }

    @Override
    public Attribute deleteById(UUID id) {
        AttributeDo attributeDo = attributeJpaDao.findById(id).orElse(null);
        if (null == attributeDo) {
            return null;
        }
        attributeDo.setDeletedAt(Instant.now());
        attributeDo.setDeletedBy(SecurityUtil.getLoginUserId());
        attributeDo.setDeletedBy(SecurityUtil.getUserName());
        attributeDo = attributeJpaDao.save(attributeDo);
        return attributeDoMapper.doToDomain(attributeDo);
    }

    @Override
    public List<Attribute> findByName(String name) {
        return Optional.ofNullable(attributeJpaDao.findByName(name))
                .orElse(Collections.emptyList())
                .stream()
                .map(attributeDoMapper::doToDomain).toList().reversed();
    }

    @Override
    public Attribute findByCategoryIdAndName(UUID categoryId, String name) {
        AttributeDo attributeDo = attributeJpaDao.findByCategoryIdAndName(categoryId, name);
        return attributeDoMapper.doToDomain(attributeDo);
    }

    @Override
    public List<Attribute> findByCategoryId(UUID categoryId) {
        return Optional.ofNullable(attributeJpaDao.findByCategoryId(categoryId))
                .orElse(Collections.emptyList())
                .stream()
                .map(attributeDoMapper::doToDomain)
                .toList().reversed();
    }

    @Override
    public List<Attribute> findAllByIdIn(List<UUID> ids) {
        return Optional.ofNullable(attributeJpaDao.findAllByIdIn(ids))
                .orElse(Collections.emptyList())
                .stream()
                .map(attributeDoMapper::doToDomain)
                .toList().reversed();
    }

    @Override
    public List<Attribute> findAll() {
        return Optional.of(attributeJpaDao.findAll())
            .orElse(Collections.emptyList())
            .stream()
            .map(attributeDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<Attribute> findByFuzzyName(String name) {
        return Optional.of(attributeJpaDao.findByNameContainsIgnoreCase(name))
            .orElse(Collections.emptyList())
            .stream()
            .map(attributeDoMapper::doToDomain)
            .toList();
    }
}
