package com.mercaso.ims.infrastructure.schedule;


import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.external.vernon.VernonAdaptor;
import com.mercaso.ims.infrastructure.external.vernon.dto.VernonItemDto;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class VendorAvailableScheduler {

    private static final Integer VERNON_AVAILABLE_LOCK_KEY = "[VendorPoInvoiceScheduler.syncVernorItemAvailableByVernorWebsite]".hashCode();
    private final PgAdvisoryLock pgAdvisoryLock;
    private final EntityManagerFactory managerFactory;
    private final VernonAdaptor vernonAdaptor;
    private final VendorService vendorService;
    private final VendorItemService vendorItemService;
    private final VendorItemApplicationService vendorItemApplicationService;


    @Scheduled(cron = "0 0 6 * * *", zone = "America/Los_Angeles")
    public void syncVernorItemAvailableByVernorWebsite() {
        log.info("Starting sync vernor item available from website Task");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                VERNON_AVAILABLE_LOCK_KEY,
                "Sync vernor po invoice Task");
            if (isAcquired == null || !isAcquired) {
                log.warn(
                    "[VendorPoInvoiceScheduler.syncVernorItemAvailableByVernorWebsite] is already in progress, please try again later.");
                return;
            }
            updateVendorItemAvailability();

        } catch (Exception e) {
            log.error("[syncVernorItemAvailableByVernorWebsite] Exception occurred: {}", e.getMessage(), e);
        } finally {
            pgAdvisoryLock.unLock(entityManager,
                VERNON_AVAILABLE_LOCK_KEY,
                "unlock [VendorPoInvoiceScheduler.syncVernorItemAvailableByVernorWebsite]");
            entityManager.close();
            log.info("Finished sync vernor item available from website Task");
        }
    }

    private void updateVendorItemAvailability() {
        Vendor vendor = vendorService.findByVendorName(VendorConstant.VERNON_SALES);
        if (vendor.isWithinShutdownWindow()) {
            log.info("Vernon Vendor is within shutdown window, skip checking availability");
            return;
        }
        List<VendorItem> vendorItems = vendorItemService.findByVendorID(vendor.getId());

        // Process matched items, need to check Vernon store
        vendorItems.forEach(vendorItem -> {
            try {
                processVendorItemAvailability(vendorItem);
            } catch (Exception e) {
                log.error("Error processing vendor item availability for item {}: {}",
                    vendorItem.getVendorSkuNumber(), e.getMessage(), e);
            }
        });
    }

    /**
     * Process a single vendor item to check and update its availability
     *
     * @param vendorItem the vendor item to process
     */
    private void processVendorItemAvailability(VendorItem vendorItem) {
        log.info("Processing Vernon Vendor Item Availability for vendor item number: {} - Matched, checking Vernon store",
            vendorItem.getVendorSkuNumber());

        boolean availability = false;
        // Query Vernon store
        if (StringUtils.isNotBlank(vendorItem.getVendorSkuNumber())) {
            List<VernonItemDto> vernonItemDtos = vernonAdaptor.searchVernonItem(vendorItem.getVendorSkuNumber());
            if (!CollectionUtils.isEmpty(vernonItemDtos)) {
                availability = vernonItemDtos.stream()
                    .anyMatch(vernonItemDto -> vendorItem.getVendorSkuNumber().equals(vernonItemDto.getItemNo()));
            }
        }

        log.info("Vernon store check for vendor item number: {} - Available: {}",
            vendorItem.getVendorSkuNumber(),
            availability);
        updateVendorItemAvailabilityStatus(vendorItem, availability);
    }

    private void updateVendorItemAvailabilityStatus(VendorItem vendorItem, boolean availability) {
        UpdateVendorItemCommand updateVendorItemCommand = UpdateVendorItemCommand.builder()
            .vendorItemId(vendorItem.getId())
            .vendorSkuNumber(vendorItem.getVendorSkuNumber())
            .vendorItemName(vendorItem.getVendorItemName())
            .cost(vendorItem.getCost())
            .backupCost(vendorItem.getBackupPackPlusCrvCost())
            .note(vendorItem.getNote())
            .aisle(vendorItem.getAisle())
            .isCostRefreshed(false)
            .isBackupCostRefreshed(false)
            .availability(availability)
            .build();
        vendorItemApplicationService.update(updateVendorItemCommand);
    }

}