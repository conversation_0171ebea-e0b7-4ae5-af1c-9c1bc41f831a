package com.mercaso.ims.infrastructure.repository.vendoritem;

import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshot;
import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshotDetail;
import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshotRepository;
import com.mercaso.ims.domain.vendoritem.enums.SnapshotType;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.VendorItemAvailabilitySnapshotDetailJpaDao;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.VendorItemAvailabilitySnapshotJpaDao;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemAvailabilitySnapshotDetailDo;
import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemAvailabilitySnapshotDo;
import com.mercaso.ims.infrastructure.repository.vendoritem.mapper.VendorItemAvailabilitySnapshotDetailMapper;
import com.mercaso.ims.infrastructure.repository.vendoritem.mapper.VendorItemAvailabilitySnapshotMapper;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@RequiredArgsConstructor
@Slf4j
public class VendorItemAvailabilitySnapshotRepositoryImpl implements VendorItemAvailabilitySnapshotRepository {

    private final VendorItemAvailabilitySnapshotJpaDao snapshotJpaDao;
    private final VendorItemAvailabilitySnapshotDetailJpaDao detailJpaDao;
    private final VendorItemAvailabilitySnapshotMapper snapshotMapper;
    private final VendorItemAvailabilitySnapshotDetailMapper detailMapper;

    @Override
    public VendorItemAvailabilitySnapshot save(VendorItemAvailabilitySnapshot domain) {
        VendorItemAvailabilitySnapshotDo dataObject = snapshotMapper.domainToDo(domain);
        dataObject = snapshotJpaDao.save(dataObject);
        return snapshotMapper.doToDomain(dataObject);
    }

    @Override
    public VendorItemAvailabilitySnapshot findById(UUID id) {
        return snapshotJpaDao.findById(id)
            .map(snapshotDo -> {
                VendorItemAvailabilitySnapshot snapshot = snapshotMapper.doToDomain(snapshotDo);
                // Load associated details
                List<VendorItemAvailabilitySnapshotDetailDo> detailDos = detailJpaDao.findBySnapshotId(snapshotDo.getId());
                List<VendorItemAvailabilitySnapshotDetail> details = detailDos.stream()
                    .map(detailMapper::doToDomain)
                    .toList();
                snapshot.setDetails(details);
                return snapshot;
            })
            .orElse(null);
    }

    @Override
    public VendorItemAvailabilitySnapshot update(VendorItemAvailabilitySnapshot domain) {
        VendorItemAvailabilitySnapshotDo dataObject = snapshotMapper.domainToDo(domain);
        dataObject = snapshotJpaDao.save(dataObject);
        return snapshotMapper.doToDomain(dataObject);
    }

    @Override
    public VendorItemAvailabilitySnapshot deleteById(UUID id) {
        VendorItemAvailabilitySnapshot domain = findById(id);
        if (domain != null) {
            snapshotJpaDao.deleteById(id);
        }
        return domain;
    }

    @Override
    public List<VendorItemAvailabilitySnapshot> findByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(UUID vendorId,
        SnapshotType snapshotType) {
        List<VendorItemAvailabilitySnapshotDo> dataObjects = snapshotJpaDao.findByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(
            vendorId,
            snapshotType);
        return dataObjects.stream()
            .map(snapshotDo -> {
                VendorItemAvailabilitySnapshot snapshot = snapshotMapper.doToDomain(snapshotDo);
                // Load associated details
                List<VendorItemAvailabilitySnapshotDetailDo> detailDos = detailJpaDao.findBySnapshotId(snapshotDo.getId());
                List<VendorItemAvailabilitySnapshotDetail> details = detailDos.stream()
                    .map(detailMapper::doToDomain)
                    .toList();
                snapshot.setDetails(details);
                return snapshot;
            })
            .toList();
    }

    @Override
    public List<VendorItemAvailabilitySnapshot> findByVendorIdAndSnapshotTypeAndSnapshotTimeAfterOrderBySnapshotTimeDesc(UUID vendorId,
        SnapshotType snapshotType,
        Instant startTime) {
        List<VendorItemAvailabilitySnapshotDo> dataObjects = snapshotJpaDao.findByVendorIdAndSnapshotTypeAndSnapshotTimeAfterOrderBySnapshotTimeDesc(
            vendorId,
            snapshotType,
            startTime);
        return dataObjects.stream()
            .map(snapshotDo -> {
                VendorItemAvailabilitySnapshot snapshot = snapshotMapper.doToDomain(snapshotDo);
                // Load associated details
                List<VendorItemAvailabilitySnapshotDetailDo> detailDos = detailJpaDao.findBySnapshotId(snapshotDo.getId());
                List<VendorItemAvailabilitySnapshotDetail> details = detailDos.stream()
                    .map(detailMapper::doToDomain)
                    .toList();
                snapshot.setDetails(details);
                return snapshot;
            })
            .toList();
    }

    @Override
    public Optional<VendorItemAvailabilitySnapshot> findLatestByVendorIdAndSnapshotType(UUID vendorId,
        SnapshotType snapshotType) {
        return snapshotJpaDao.findTop1ByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(vendorId, snapshotType)
            .map(snapshotDo -> {
                VendorItemAvailabilitySnapshot snapshot = snapshotMapper.doToDomain(snapshotDo);
                // Load associated details
                List<VendorItemAvailabilitySnapshotDetailDo> detailDos = detailJpaDao.findBySnapshotId(snapshotDo.getId());
                List<VendorItemAvailabilitySnapshotDetail> details = detailDos.stream()
                    .map(detailMapper::doToDomain)
                    .toList();
                snapshot.setDetails(details);
                return snapshot;
            });
    }

    @Override
    @Transactional
    public void saveSnapshotWithDetails(VendorItemAvailabilitySnapshot snapshot,
        List<VendorItemAvailabilitySnapshotDetail> details) {

        VendorItemAvailabilitySnapshotDo snapshotDo = snapshotMapper.domainToDo(snapshot);
        snapshotDo = snapshotJpaDao.save(snapshotDo);
        UUID snapshotId = snapshotDo.getId();

        if (details != null && !details.isEmpty()) {
            List<VendorItemAvailabilitySnapshotDetailDo> detailDos = details.stream()
                .map(detail -> {
                    VendorItemAvailabilitySnapshotDetailDo detailDo = detailMapper.domainToDo(detail);
                    detailDo.setSnapshotId(snapshotId);
                    return detailDo;
                })
                .toList();

            detailJpaDao.saveAll(detailDos);
        }

        log.info("Saved snapshot with {} details for vendor: {}",
            details != null ? details.size() : 0, snapshot.getVendorId());
    }
} 