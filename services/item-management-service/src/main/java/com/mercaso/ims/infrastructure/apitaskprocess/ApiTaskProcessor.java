package com.mercaso.ims.infrastructure.apitaskprocess;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;

/**
 * Interface for processing API tasks
 * Each implementation handles a specific type of API call with rate limiting
 *
 * @param <T> The type of response expected from the API call
 */
public interface ApiTaskProcessor<T> {

    /**
     * Get the task type this processor handles
     *
     * @return The task type string (e.g., "FINALE_GET_PRODUCT")
     */
    String getTaskType();


    boolean canProcess(String taskType);

    /**
     * Execute the API task
     *
     * @param task The task to execute containing request parameters
     * @return The response from the API call
     * @throws JsonProcessingException If the API call fails
     */
    T executeTask(ApiTaskQueue task) throws JsonProcessingException;

    /**
     * Execute the API task with logging
     * Default implementation calls executeTask - can be overridden for enhanced logging
     *
     * @param task The task to execute containing request parameters
     * @return The response from the API call
     */
    default T executeTaskWithLogging(ApiTaskQueue task) {
        try {
            return executeTask(task);
        } catch (JsonProcessingException e) {
            // Wrap in business exception for consistent error handling
            throw new ImsBusinessException("Task execution failed", e);
        }
    }

    /**
     * Check if this task type needs to store the response
     * Some tasks are fire-and-forget, others need the response for further processing
     *
     * @return true if response should be stored, false otherwise
     */
    boolean needsResponse();

    /**
     * Calculate retry delay in seconds based on retry attempt
     * Implements exponential backoff strategy
     *
     * @param retryAttempt The current retry attempt (1-based)
     * @return Delay in seconds before next retry
     */
    default long calculateRetryDelay(int retryAttempt) {
        // Exponential backoff: 2^attempt seconds, max 300 seconds (5 minutes)
        return Math.min(300, (long) Math.pow(2, retryAttempt));
    }

    /**
     * Check if the exception is retryable
     * Some exceptions (like network timeouts) should trigger retries,
     * others (like authentication errors) should not
     *
     * @param exception The exception that occurred
     * @return true if the task should be retried, false otherwise
     */
    default boolean isRetryableException(Exception exception) {
        // Default implementation: retry on most exceptions except authentication/authorization
        String message = exception.getMessage();
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            // Don't retry on authentication/authorization errors
            if (lowerMessage.contains("unauthorized") ||
                    lowerMessage.contains("forbidden") ||
                    lowerMessage.contains("authentication") ||
                    lowerMessage.contains("401") ||
                    lowerMessage.contains("403")) {
                return false;
            }
        }
        return true;
    }

    /**
     * Validate task parameters before execution
     * Implementations can override this to add specific validation logic
     *
     * @param task The task to validate
     * @throws IllegalArgumentException If task parameters are invalid
     */
    default void validateTask(ApiTaskQueue task) {
        if (task == null) {
            throw new IllegalArgumentException("Task cannot be null");
        }
        if (task.getTaskType() == null || !task.getTaskType().equals(getTaskType())) {
            throw new IllegalArgumentException("Invalid task type for this processor");
        }
        if (task.getRequestPayload() == null) {
            throw new IllegalArgumentException("Request payload cannot be null");
        }
    }
}
