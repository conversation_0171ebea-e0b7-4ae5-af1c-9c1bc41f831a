package com.mercaso.ims.infrastructure.repository.itemimage.jpa.mapper;

import com.mercaso.ims.domain.BaseValueObjectDoMapper;
import com.mercaso.ims.domain.item.ItemImage;
import com.mercaso.ims.infrastructure.repository.itemimage.jpa.dataobject.ItemImageDo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ItemImageDoMapper extends BaseValueObjectDoMapper<ItemImageDo, ItemImage> {

    ItemImageDoMapper INSTANCE = Mappers.getMapper(ItemImageDoMapper.class);

    @Override
    ItemImage doToDomain(ItemImageDo itemImageDo);

    @Override
    ItemImageDo domainToDo(ItemImage itemImage);
}
