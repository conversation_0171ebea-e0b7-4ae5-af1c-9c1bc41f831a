package com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.mapper;

import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.dataobject.ItemCostCollectionDo;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ItemCostCollectionDoMapper extends BaseDoMapper<ItemCostCollectionDo, ItemCostCollection> {

}