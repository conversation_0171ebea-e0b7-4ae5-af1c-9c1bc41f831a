package com.mercaso.ims.infrastructure.apitaskprocess;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.infrastructure.apitaskprocess.payload.TaskRequestPayload;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Abstract base class for API task processors
 * Provides common functionality for JSON serialization/deserialization and method execution
 *
 * @param <T> The type of response expected from the API call
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractApiTaskProcessor<T> implements ApiTaskProcessor<T> {

    protected final ObjectMapper objectMapper;

    /**
     * Parse request payload from JSON string to specified type
     *
     * @param task The task containing the JSON payload
     * @param requestType The class type to deserialize to
     * @param <R> The request type
     * @return The deserialized request object
     * @throws JsonProcessingException If JSON parsing fails
     */
    protected <R> R parseRequestPayload(ApiTaskQueue task, Class<R> requestType) throws JsonProcessingException {
        if (task.getRequestPayload() == null) {
            throw new IllegalArgumentException("Request payload is null for task: " + task.getId());
        }

        try {
            return objectMapper.readValue(task.getRequestPayload(), requestType);
        } catch (JsonProcessingException e) {
            log.error("Failed to parse request payload for task {}: {}", task.getId(), e.getMessage());
            throw e;
        }
    }

    /**
     * Serialize response object to JSON string
     *
     * @param response The response object to serialize
     * @return JSON string representation of the response
     * @throws JsonProcessingException If JSON serialization fails
     */
    protected String serializeResponse(T response) throws JsonProcessingException {
        if (response == null) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(response);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize response: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Log task execution start
     */
    protected void logTaskStart(ApiTaskQueue task) {
        log.info("Starting execution of task {} with type {} for endpoint {}",
                task.getId(), task.getTaskType(), task.getApiEndpoint());
    }

    /**
     * Log task execution success
     */
    protected void logTaskSuccess(ApiTaskQueue task, T response) {
        log.info("Successfully executed task {} with type {}",
                task.getId(), task.getTaskType());
        if (log.isDebugEnabled() && response != null) {
            log.debug("Task {} response: {}", task.getId(), response.toString());
        }
    }

    /**
     * Log task execution failure
     */
    protected void logTaskFailure(ApiTaskQueue task, Exception exception) {
        log.error("Failed to execute task {} with type {}: {}",
                task.getId(), task.getTaskType(), exception.getMessage(), exception);
    }

    /**
     * Template method for task execution with common logging and error handling
     * Automatically manages task execution context to prevent infinite loops
     */
    @Override
    public final T executeTaskWithLogging(ApiTaskQueue task) {
        validateTask(task);
        logTaskStart(task);

        return TaskExecutionContext.executeInTaskContext(() -> {
            try {
                T response = executeTask(task);
                logTaskSuccess(task, response);
                return response;
            } catch (Exception e) {
                logTaskFailure(task, e);
                throw new ImsBusinessException("Task execution failed", e);
            }
        });
    }


    protected String extractParameter(TaskRequestPayload requestPayload, String... paramNames) {
        if (requestPayload.getParameters() == null) {
            return null;
        }

        for (String paramName : paramNames) {
            Object param = requestPayload.getParameters().get(paramName);
            if (param != null) {
                return serializeParameter(param);
            }
        }

        return null;
    }

    /**
     * Serialize parameter to string, handling both simple and complex objects
     * For simple types (String, Number, Boolean), use toString()
     * For complex objects, use JSON serialization
     */
    private String serializeParameter(Object param) {
        if (param == null) {
            return null;
        }

        // For simple types, use toString()
        if (param instanceof String ||
            param instanceof Number ||
            param instanceof Boolean ||
            param instanceof Character) {
            return param.toString();
        }

        // For complex objects, use JSON serialization
        try {
            return objectMapper.writeValueAsString(param);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize parameter of type {}, falling back to toString(): {}",
                    param.getClass().getSimpleName(), e.getMessage());
            return param.toString();
        }
    }
}
