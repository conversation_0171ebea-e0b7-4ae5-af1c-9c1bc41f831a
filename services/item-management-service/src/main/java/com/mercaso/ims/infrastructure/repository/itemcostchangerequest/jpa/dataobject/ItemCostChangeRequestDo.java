package com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa.dataobject;

import com.mercaso.ims.domain.itemcostchangerequest.enums.ItemCostChangeRequestStatus;
import com.mercaso.ims.domain.itemcostchangerequest.enums.MatchedType;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Table(name = "item_cost_change_request")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_cost_change_request set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class ItemCostChangeRequestDo extends BaseDo {

    @Column(name = "item_cost_collection_id")
    private UUID itemCostCollectionId;

    @Column(name = "vendor_id")
    private UUID vendorId;

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "sku_number")
    private String skuNumber;

    @Column(name = "vendor_sku_number")
    private String vendorSkuNumber;

    @Column(name = "vendor_item_name")
    private String vendorItemName;

    @Column(name = "previous_cost")
    private BigDecimal previousCost;

    @Column(name = "target_cost")
    private BigDecimal targetCost;


    @Column(name = "tax")
    private BigDecimal tax;

    @Column(name = "crv")
    private BigDecimal crv;

    @Column(name = "match_type")
    @Enumerated(EnumType.STRING)
    private MatchedType matchType;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private ItemCostChangeRequestStatus status;

    @Column(name = "vendor_item_upc")
    private String vendorItemUpc;


    @Column(name = "availability")
    private Boolean availability;

    @Column(name = "cost_type")
    private String costType;

    @Column(name = "aisle")
    private String aisle;


}
