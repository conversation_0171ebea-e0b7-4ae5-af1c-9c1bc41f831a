package com.mercaso.ims.infrastructure.external.finale;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class FinalePackingParser {

    private FinalePackingParser() {
    }

    // Secure regex for Finale normalized packing string format: "{conversion_factor} cs {user_visible_representation}"
    // Using possessive quantifiers to prevent ReDoS attacks
    private static final Pattern PACKING_PATTERN = Pattern.compile(
        "^(\\d+(?>\\.\\d+)?)\\s++cs\\s++(.++)$"
    );
    
    // Maximum input length to prevent extremely long strings
    private static final int MAX_INPUT_LENGTH = 1000;

    public static Double extractConversionFactor(String packingString) {
        if (packingString == null || packingString.isEmpty()) {
            return null; // open stock item
        }

        // Validate input length to prevent ReDoS attacks
        if (packingString.length() > MAX_INPUT_LENGTH) {
            log.warn("Packing string exceeds maximum length: {} characters. Truncating.", packingString.length());
            packingString = packingString.substring(0, MAX_INPUT_LENGTH);
        }

        String trimmedString = packingString.trim();
        
        Matcher matcher = PACKING_PATTERN.matcher(trimmedString);
        if (matcher.matches()) {
            try {
                return Double.parseDouble(matcher.group(1));
            } catch (NumberFormatException e) {
                log.error("Failed to parse conversion factor from packing string: \"" + packingString + "\"", e);
                return null;
            }
        }
        
        log.debug("Packing string does not match expected format: \"{}\"", packingString);
        return null;
    }
}
