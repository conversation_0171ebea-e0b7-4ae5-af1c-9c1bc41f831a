package com.mercaso.ims.infrastructure.external.vernon;

import com.mercaso.ims.infrastructure.client.HttpClient;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.exception.ServiceRetryableException;
import com.mercaso.ims.infrastructure.external.vernon.dto.VernonItemDto;
import com.mercaso.ims.infrastructure.external.vernon.dto.VernonOrderInvoiceDTO;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Cookie;
import okhttp3.FormBody;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class VernonAdaptor {

    private static final String LOGIN_URL = "http://www.vernonsales.com/ais101/WebPass.php";
    private static final String BASE_URL = "http://www.vernonsales.com/";
    private static final String QUERY_ORDERS_URL = "http://www.vernonsales.com/ais101/CsSaStatus.php";
    private static final String QUERY_ORDER_DETAIL_URL = "http://www.vernonsales.com/ais101/CsSaStatusDetail.php?status=I&trno=926136";
    private static final String QUERY_VERNON_ITEM_URL = "http://www.vernonsales.com/ais101/itemdisp.php?keywords=%s&action=Search&action2=Search";
    private static final String UPC_NUMBER = "UPC#:";
    private static final String ITEM_NUMBER = "Item No.:";
    private static final String COOKIE = "Cookie";

    private final HttpClient client;
    @Value("${external.vernon.userno}")
    private String userno;
    @Value("${external.vernon.passno}")
    private String passno;

    private void login() {
        log.info("[login] Start login to vernor");
        FormBody formBody = new FormBody.Builder()
            .add("userno", userno)
            .add("passno", passno)
            .add("action", "Login")
            .build();

        Request request = new Request.Builder()
            .url(LOGIN_URL)
            .addHeader("User-Agent",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36")
            .post(formBody)
            .build();
        try (Response response = client.execute(request)) {
            log.info("[login] response: {}", response.body().string());
        } catch (IOException e) {
            log.error("[loginVernor] Error: {}", e.getMessage(), e);
            throw new ImsBusinessException(ErrorCodeEnums.VERNOR_API_ERROR, "Failed to get longin vernor ");
        }

    }

    @Retryable(retryFor = {ServiceRetryableException.class},
        maxAttempts = 2,
        backoff = @Backoff(multiplier = 2))
    public List<VernonOrderInvoiceDTO> getOrderList() {
        log.info("[getOrderListHtmlContent] Start getOrderListHtmlContent");
        String phpCookie = buildCookie();
        if (StringUtils.isBlank(phpCookie)) {
            login();
            throw new ServiceRetryableException("Need to login to vernor from Blank phpCookie");
        }
        Request request = new Request.Builder()
            .url(QUERY_ORDERS_URL)
            .header(COOKIE, phpCookie)
            .build();

        try (Response response = client.execute(request)) {
            if (response.isSuccessful()) {
                assert response.body() != null;
                String htmlContent = response.body().string();

                if (!isLogin(htmlContent)) {
                    login();
                    throw new ServiceRetryableException("Need to login to vernor from not Login yet");
                }

                return parseVernonOrderInvoice(htmlContent);
            } else {
                log.error("[getOrderListHtmlContent]  Failed, response code: {}", response.code());
                throw new ImsBusinessException(ErrorCodeEnums.VERNOR_API_ERROR, "Failed to get vernor OrderListHtmlContent");
            }
        } catch (IOException e) {
            log.error("[getOrderListHtmlContent] Error: {}", e.getMessage(), e);
            throw new ImsBusinessException(ErrorCodeEnums.VERNOR_API_ERROR, "Failed to get vernor OrderListHtmlContent");
        }
    }


    @Retryable(retryFor = {ServiceRetryableException.class},
        maxAttempts = 2,
        backoff = @Backoff(multiplier = 2))
    public String getOrderDetailPage(String orderNumber) {
        log.info("[getOrderDetail] Start getOrderDetail :{}", orderNumber);
        String phpCookie = buildCookie();
        if (StringUtils.isBlank(phpCookie)) {
            login();
            throw new ServiceRetryableException("Need to login to vernor");
        }
        HttpUrl url = HttpUrl.parse(QUERY_ORDER_DETAIL_URL).newBuilder()
            .addQueryParameter("status", "I")
            .addQueryParameter("trno", orderNumber)
            .build();
        Request request = new Request.Builder()
            .url(url)
            .header(COOKIE, phpCookie)
            .get()
            .build();

        try (Response response = client.execute(request)) {
            if (response.isSuccessful()) {
                assert response.body() != null;
                String htmlContent = response.body().string();
                log.info("[getOrderDetail] response: {}", htmlContent);

                if (!isLogin(htmlContent)) {
                    login();
                    throw new ServiceRetryableException("Need to login to vernor");
                }

                return htmlContent;
            } else {
                log.error("[getOrderDetail]  Failed, response code: {}", response.code());
                throw new ImsBusinessException(ErrorCodeEnums.VERNOR_API_ERROR, "Failed to get order detail from vernor");
            }
        } catch (IOException e) {
            log.error("[getOrderDetail] Error: {}", e.getMessage(), e);
            throw new ImsBusinessException(ErrorCodeEnums.VERNOR_API_ERROR, "ailed to get order detail from vernor");
        }
    }


    @Retryable(retryFor = {ImsBusinessException.class},
        maxAttempts = 2,
        backoff = @Backoff(multiplier = 2))
    public List<VernonItemDto> searchVernonItem(String vernonItemNumber) {
        log.info("[searchVernonItem] Start searchVernonItem for item: {}", vernonItemNumber);
        String phpCookie = buildCookie();
        if (StringUtils.isBlank(phpCookie)) {
            login();
        }

        String url = String.format(QUERY_VERNON_ITEM_URL, vernonItemNumber);
        Request request = new Request.Builder()
            .url(url)
            .header(COOKIE, phpCookie)
            .build();

        try (Response response = client.execute(request)) {
            if (response.isSuccessful()) {
                assert response.body() != null;
                String htmlContent = response.body().string();

                return parseVernonItems(htmlContent);
            } else {
                log.error("[searchVernonItem] Failed, response code: {}", response.code());
                throw new ImsBusinessException(ErrorCodeEnums.VERNOR_API_ERROR, "Failed to get item detail from vernor");
            }
        } catch (IOException e) {
            log.error("[searchVernonItem] Error: {}", e.getMessage(), e);
            throw new ImsBusinessException(ErrorCodeEnums.VERNOR_API_ERROR, "ailed to get item detail from vernor");
        }
    }

    private String buildCookie() {
        List<Cookie> cookies = client.getCookies(BASE_URL);
        StringBuilder cookieBuilder = new StringBuilder();
        for (Cookie cookie : cookies) {
            cookieBuilder.append(cookie.name()).append("=").append(cookie.value()).append(";");

        }
        return cookieBuilder.toString();
    }

    private boolean isLogin(String htmlContent) {
        Document doc = Jsoup.parse(htmlContent);
        // msgBoxOnly
        Element msgBox = doc.selectFirst("div#msgBoxOnly");
        if (msgBox != null) {
            String message = msgBox.text();

            return !message.contains("You need to login to access this area");
        } else {
            return true;
        }

    }


    public List<VernonOrderInvoiceDTO> parseVernonOrderInvoice(String htmlContent) {
        Document document = Jsoup.parse(htmlContent);

        List<VernonOrderInvoiceDTO> orderList = new ArrayList<>();

        Element table = document.select("table.CsSaStatus:has(td.header1:contains(Orders invoiced & shipped))").first();

        Elements rows = table.select("tr");

        for (int i = 2; i < rows.size(); i++) {
            Elements cells = rows.get(i).select("td");

            String invoiceNo = cells.get(0).text();
            String date = cells.get(1).text();
            String amount = cells.get(2).text();
            String shippedDate = cells.get(3).text();
            String shipVia = cells.get(4).text();
            String shipTrackingNo = cells.get(5).text();

            VernonOrderInvoiceDTO order = new VernonOrderInvoiceDTO(invoiceNo,
                date,
                amount,
                shippedDate,
                shipVia,
                shipTrackingNo);
            orderList.add(order);
        }
        return orderList;


    }

    private List<VernonItemDto> parseVernonItems(String htmlContent) {
        Document document = Jsoup.parse(htmlContent);
        List<VernonItemDto> itemList = new ArrayList<>();

        // Check for "NO MATCHES" message
        Element noMatchesElement = document.select("div#msgBoxOnly h3:contains(NO MATCHES)").first();
        if (noMatchesElement != null) {
            log.info("[parseVernonItems] No matching items found");
            return itemList; // Return empty list
        }

        // Find all tables with class "itdisp07"
        Elements itemTables = document.select("table.itdisp07");

        // If no item tables found, return empty list
        if (itemTables.isEmpty()) {
            log.info("[parseVernonItems] No item tables found in HTML content");
            return itemList;
        }

        for (Element table : itemTables) {
            try {
                Element detailCell = table.select("td.detimage01").first();
                if (detailCell == null) {
                    continue;
                }

                // Extract all fields directly from the text
                String itemNoText = detailCell.getElementsContainingOwnText(ITEM_NUMBER).text();
                String itemNo = "";
                String description = "";
                String upcNo = "";
                String unitsPack = "";
                String packCost = "";

                if (itemNoText.contains(ITEM_NUMBER)) {

                    String[] parts = itemNoText.split(ITEM_NUMBER);
                    if (parts.length > 1) {
                        String remaining = parts[1].trim();
                        int firstSpace = remaining.indexOf(" ");
                        if (firstSpace > 0) {
                            itemNo = remaining.substring(0, firstSpace).trim();

                            int upcIndex = remaining.indexOf(UPC_NUMBER);
                            if (upcIndex > firstSpace) {
                                description = remaining.substring(firstSpace, upcIndex).trim();
                            }
                        } else {
                            itemNo = remaining;
                        }
                    }

                    if (itemNoText.contains(UPC_NUMBER)) {
                        String[] upcParts = itemNoText.split(UPC_NUMBER);
                        if (upcParts.length > 1) {
                            String upcRemaining = upcParts[1].trim();
                            int upcEndIndex = upcRemaining.indexOf(" Units/Pack:");
                            if (upcEndIndex > 0) {
                                upcNo = upcRemaining.substring(0, upcEndIndex).trim();
                            }
                        }
                    }

                    if (itemNoText.contains("Units/Pack:")) {
                        String[] unitsParts = itemNoText.split("Units/Pack:");
                        if (unitsParts.length > 1) {
                            String unitsRemaining = unitsParts[1].trim();
                            int unitsEndIndex = unitsRemaining.indexOf(" Unit Cost:");
                            if (unitsEndIndex > 0) {
                                unitsPack = unitsRemaining.substring(0, unitsEndIndex).trim();
                            }
                        }
                    }

                    if (itemNoText.contains("Pack Cost:")) {
                        String[] packCostParts = itemNoText.split("Pack Cost:");
                        if (packCostParts.length > 1) {
                            String packRemaining = packCostParts[1].trim();
                            int packEndIndex = packRemaining.indexOf(" Quantity:");
                            if (packEndIndex > 0) {
                                packCost = packRemaining.substring(0, packEndIndex).trim().replace("$", "");
                            } else {
                                packCost = packRemaining.split("\\s+")[0].replace("$", "");
                            }
                        }
                    }
                }

                // We don't have expiration date in the provided HTML, so leaving it blank
                String expDate = "";

                VernonItemDto item = VernonItemDto.builder()
                    .itemNo(itemNo)
                    .description(description)
                    .upcNo(upcNo)
                    .expDate(expDate)
                    .unitsPack(unitsPack)
                    .packCost(packCost)
                    .build();

                itemList.add(item);

            } catch (Exception e) {
                log.error("[parseVernonItems] Error parsing item: {}", e.getMessage(), e);
                // Continue to next item if there's an error with current one
            }
        }

        log.info("[parseVernonItems] Found item info: {} ", itemList);
        return itemList;
    }

}