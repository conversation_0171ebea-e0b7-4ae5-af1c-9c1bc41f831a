package com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa;

import com.mercaso.ims.application.dto.ItemAdjustmentRequestDto;
import com.mercaso.ims.application.query.ItemAdjustmentRequestQuery;

import java.util.List;

public interface CustomizedItemAdjustmentRequestJpaDao {

    List<ItemAdjustmentRequestDto> getItemAdjustmentRequestDto(ItemAdjustmentRequestQuery query);

    long countQuery(ItemAdjustmentRequestQuery query);


}
