package com.mercaso.ims.infrastructure.annotation;

import com.mercaso.ims.domain.taskqueue.enums.TaskType;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark methods that should be executed through the rate-limited task queue
 * When applied to a method, the method execution will be queued and processed asynchronously
 * with appropriate rate limiting based on the task type.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RateLimitedTask {

    /**
     * Task type identifier for rate limiting and processing
     * This determines which rate limiter configuration to use
     *
     * @return Task type (e.g., "FINALE_GET_PRODUCT", "FINALE_UPDATE_STOCK")
     */
    TaskType taskType();

    /**
     * API endpoint identifier for tracking and logging
     *
     * @return API endpoint name or URL
     */
    String endpoint() default "";

    /**
     * HTTP method for the API call
     *
     * @return HTTP method (GET, POST, PUT, DELETE, PATCH)
     */
    String httpMethod() default "GET";

    /**
     * Whether this task needs to store and return the response
     * Set to false for fire-and-forget operations
     *
     * @return true if response should be stored, false otherwise
     */
    boolean needsResponse() default true;

    /**
     * Task priority (higher number = higher priority)
     *
     * @return Priority value (default: 0)
     */
    int priority() default 0;

    /**
     * Maximum number of retry attempts
     *
     * @return Maximum retry count (default: 3)
     */
    int maxRetryCount() default 3;

    /**
     * Whether to execute synchronously (wait for result) or asynchronously
     * Synchronous execution will block until the task completes or times out
     *
     * @return true for synchronous execution, false for asynchronous
     */
    boolean synchronous() default false;

    /**
     * Whether to bypass the task queue and execute directly
     * This can be useful for testing or emergency scenarios
     *
     * @return true to bypass task queue, false to use task queue
     */
    boolean bypass() default false;

    /**
     * Custom error message when task creation fails
     *
     * @return Error message template
     */
    String errorMessage() default "Failed to create rate-limited task for method: {method}";
}
