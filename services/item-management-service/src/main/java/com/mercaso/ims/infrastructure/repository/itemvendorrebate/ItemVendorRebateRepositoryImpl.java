package com.mercaso.ims.infrastructure.repository.itemvendorrebate;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateRepository;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.ItemVendorRebateJpaDao;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.dataobject.ItemVendorRebateDo;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.mapper.ItemVendorRebateDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_REBATE_NOT_FOUND;

/**
 * Implementation of ItemVendorRebateRepository
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ItemVendorRebateRepositoryImpl implements ItemVendorRebateRepository {

    private final ItemVendorRebateJpaDao itemVendorRebateJpaDao;

    private final ItemVendorRebateDoMapper itemVendorRebateDoMapper;

    @Override
    public ItemVendorRebate save(ItemVendorRebate itemVendorRebate) {
        log.info("Saving ItemVendorRebate: {}", itemVendorRebate);
        ItemVendorRebateDo itemVendorRebateDo = itemVendorRebateDoMapper.domainToDo(itemVendorRebate);
        itemVendorRebateDo = itemVendorRebateJpaDao.save(itemVendorRebateDo);
        return itemVendorRebateDoMapper.doToDomain(itemVendorRebateDo);
    }

    @Override
    public ItemVendorRebate findById(UUID id) {
        log.info("Finding ItemVendorRebate by id: {}", id);
        ItemVendorRebateDo itemVendorRebateDo = itemVendorRebateJpaDao.findById(id).orElse(null);
        if (null == itemVendorRebateDo) {
            return null;
        }
        return itemVendorRebateDoMapper.doToDomain(itemVendorRebateDo);
    }

    @Override
    public ItemVendorRebate update(ItemVendorRebate domain) {
        ItemVendorRebateDo itemVendorRebateDo = itemVendorRebateJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(itemVendorRebateDo)) {
            throw new ImsBusinessException(VENDOR_REBATE_NOT_FOUND.getCode());
        }
        ItemVendorRebateDo itemVendorRebateDoTarget = itemVendorRebateDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList(
                "createdBy",
                "createdAt",
                "updatedAt");
        BeanUtils.copyProperties(itemVendorRebateDoTarget, itemVendorRebateDo, ignoreProperties.toArray(new String[0]));
        ItemVendorRebateDo result = itemVendorRebateJpaDao.save(itemVendorRebateDo);
        return itemVendorRebateDoMapper.doToDomain(result);
    }

    @Override
    public ItemVendorRebate deleteById(UUID id) {
        ItemVendorRebateDo itemVendorRebateDo = itemVendorRebateJpaDao.findById(id).orElse(null);
        if (null == itemVendorRebateDo) {
            return null;
        }

        itemVendorRebateDo.setDeletedAt(Instant.now());
        itemVendorRebateDo.setDeletedBy(SecurityUtil.getLoginUserId());
        itemVendorRebateDo.setDeletedUserName(SecurityUtil.getUserName());
        return itemVendorRebateDoMapper.doToDomain(itemVendorRebateJpaDao.save(itemVendorRebateDo));
    }

    @Override
    public List<ItemVendorRebate> findByVendorId(UUID vendorId) {
        log.debug("Finding ItemVendorRebates by vendorId: {}", vendorId);
        List<ItemVendorRebateDo> itemVendorRebateDos = itemVendorRebateJpaDao.findByVendorId(vendorId);
        if (null == itemVendorRebateDos) {
            return List.of();
        }
        return itemVendorRebateDos.stream()
                .map(itemVendorRebateDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<ItemVendorRebate> findByItemId(UUID itemId) {
        log.debug("Finding ItemVendorRebates by itemId: {}", itemId);
        List<ItemVendorRebateDo> itemVendorRebateDos = itemVendorRebateJpaDao.findByItemId(itemId);
        if (null == itemVendorRebateDos) {
            return List.of();
        }
        return itemVendorRebateDos.stream()
                .map(itemVendorRebateDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<ItemVendorRebate> findByVendorItemId(UUID vendorItemId) {
        log.debug("Finding ItemVendorRebates by vendorItemId: {}", vendorItemId);
        List<ItemVendorRebateDo> itemVendorRebateDos = itemVendorRebateJpaDao.findByVendorItemId(vendorItemId);
        if (null == itemVendorRebateDos) {
            return List.of();
        }
        return itemVendorRebateDos.stream()
                .map(itemVendorRebateDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<ItemVendorRebate> findByVendorIdAndItemId(UUID vendorId, UUID itemId) {
        List<ItemVendorRebateDo> itemVendorRebateDos = itemVendorRebateJpaDao.findByVendorIdAndItemId(vendorId, itemId);
        if (null == itemVendorRebateDos) {
            return List.of();
        }
        return itemVendorRebateDos.stream()
                .map(itemVendorRebateDoMapper::doToDomain)
                .toList();
    }
}
