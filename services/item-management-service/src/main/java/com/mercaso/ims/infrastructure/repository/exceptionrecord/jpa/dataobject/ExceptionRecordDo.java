package com.mercaso.ims.infrastructure.repository.exceptionrecord.jpa.dataobject;

import com.mercaso.ims.domain.exceptionrecord.enums.EntityType;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordStatus;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordType;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Table(name = "exception_record")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update exception_record set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ExceptionRecordDo extends BaseDo {

    @Column(name = "business_event_id")
    private UUID businessEventId;
    @Column(name = "entity_id")
    private UUID entityId;
    @Column(name = "entity_type")
    @Enumerated(EnumType.STRING)
    private EntityType entityType;
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private ExceptionRecordStatus status;
    @Column(name = "exception_type")
    @Enumerated(EnumType.STRING)
    private ExceptionRecordType exceptionType;
    @Column(name = "description")
    private String description;
    @Column(name = "note")
    private String note;
}
