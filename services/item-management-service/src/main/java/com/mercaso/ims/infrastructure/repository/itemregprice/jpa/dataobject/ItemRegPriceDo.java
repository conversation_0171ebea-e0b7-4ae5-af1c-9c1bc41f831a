package com.mercaso.ims.infrastructure.repository.itemregprice.jpa.dataobject;

import com.mercaso.ims.domain.itemregprice.enums.ItemRegPriceStatus;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Table(name = "item_reg_price")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_reg_price set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemRegPriceDo extends BaseDo {

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "crv")
    private BigDecimal crv;

    @Column(name = "reg_price")
    private BigDecimal regPrice;

    @Column(name = "reg_price_individual")
    private BigDecimal regPriceIndividual;

    @Column(name = "reg_price_plus_crv")
    private BigDecimal regPricePlusCrv;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private ItemRegPriceStatus itemRegPriceStatus;

    @Column(name = "crv_flag")
    private Boolean crvFlag;

    @Column(name = "item_price_group_id")
    private UUID itemPriceGroupId;

}
