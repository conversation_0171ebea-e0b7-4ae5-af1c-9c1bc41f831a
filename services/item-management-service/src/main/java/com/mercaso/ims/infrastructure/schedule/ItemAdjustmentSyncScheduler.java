package com.mercaso.ims.infrastructure.schedule;

import com.mercaso.ims.application.dto.event.ItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemDeletedApplicationEvent;
import com.mercaso.ims.application.service.ShopifyApplicationService;
import com.mercaso.ims.domain.businessevent.BusinessEvent;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.itemsyncinfo.ItemAdjustmentSyncStatus;
import com.mercaso.ims.domain.itemsyncinfo.ItemAdjustmentSyncStatusRepository;
import com.mercaso.ims.domain.itemsyncinfo.enums.SyncShopifyStatus;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import com.mercaso.ims.infrastructure.event.factory.ApplicationEventFactory;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Consumer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ItemAdjustmentSyncScheduler {

    private static final Integer LOCK_KEY = "[ItemAdjustmentSyncService.startSyncShopifyTask]".hashCode();
    private final ItemAdjustmentSyncStatusRepository itemAdjustmentSyncStatusRepository;
    private final BusinessEventService businessEventService;
    private final ApplicationEventFactory applicationEventFactory;
    private final ShopifyApplicationService shopifyApplicationService;
    private final RateLimiterRegistry rateLimiterRegistry;
    private final PgAdvisoryLock pgAdvisoryLock;
    private final EntityManagerFactory managerFactory;

    @Scheduled(fixedRate = 300000)
    public void startSyncShopifyTask() {
        log.info("Starting Shopify sync task");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                LOCK_KEY,
                "Sync Shopify Task");
            if (isAcquired == null || !isAcquired) {
                log.warn("[ItemAdjustmentSyncService.startSyncShopifyTask] is already in progress, please try again later.");
                return;
            }

            RateLimiter rateLimiter = rateLimiterRegistry.rateLimiter("syncShopify");

            List<BusinessEvent> businessEvents = getBusinessEvents();
            if (!businessEvents.isEmpty()) {
                for (BusinessEvent event : businessEvents) {
                    rateLimiter.executeRunnable(() -> syncItemAdjustmentToShopify(event));
                }
            }
        } catch (Exception e) {
            log.error("[SyncShopifyTask] Exception occurred: {}", e.getMessage(), e);
        } finally {
            pgAdvisoryLock.unLock(entityManager, LOCK_KEY, "unlock request refresh shipment total count");
            entityManager.close();
            log.info("Finished Shopify sync task");
        }
    }

    private void syncItemAdjustmentToShopify(BusinessEvent businessEvent) {
        long beginTime = System.currentTimeMillis();

        invokeShopify(businessEvent);

        log.debug("syncItemAdjustmentToShopify cost time:{}", System.currentTimeMillis() - beginTime);
    }

    private List<BusinessEvent> getBusinessEvents() {
        List<ItemAdjustmentSyncStatus> itemAdjustmentSyncStatuses = itemAdjustmentSyncStatusRepository.findBySyncShopifyStatus(
            SyncShopifyStatus.FAILURE);

        if (CollectionUtils.isEmpty(itemAdjustmentSyncStatuses)) {
            log.debug("No item adjustment to sync to Shopify");
            return List.of();
        }

        List<UUID> businessEventIds = itemAdjustmentSyncStatuses.stream()
            .map(ItemAdjustmentSyncStatus::getBusinessEventId)
            .distinct()
            .toList();

        List<BusinessEvent> businessEvents = businessEventService.findByIdInAndOrderByCreatedAt(businessEventIds);
        log.info("Found {} business events for item adjustment sync status", businessEvents.size());

        return businessEvents;
    }


    private void invokeShopify(BusinessEvent businessEvent) {
        Optional<? extends BaseApplicationEvent> baseApplicationEvent = applicationEventFactory.buildApplicationEvent(
            businessEvent);
        switch (businessEvent.getType()) {
            case ITEM_CREATED -> processEvent(baseApplicationEvent,
                ItemCreatedApplicationEvent.class,
                shopifyApplicationService::syncItemCreatedEvent);
            case ITEM_AMEND -> processEvent(baseApplicationEvent,
                ItemAmendApplicationEvent.class,
                shopifyApplicationService::syncItemAmendEvent);
            case ITEM_DELETED -> processEvent(baseApplicationEvent,
                ItemDeletedApplicationEvent.class,
                shopifyApplicationService::syncItemDeleteEvent);
            default -> log.error("Unsupported business event type: {}", businessEvent.getType());
        }
    }


    private <T extends BaseApplicationEvent> void processEvent(
        Optional<? extends BaseApplicationEvent> baseApplicationEvent,
        Class<T> eventType,
        Consumer<T> eventConsumer) {

        baseApplicationEvent
            .filter(eventType::isInstance)
            .map(eventType::cast)
            .ifPresent(eventConsumer);
    }
}
