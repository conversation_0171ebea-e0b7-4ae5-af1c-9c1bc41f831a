package com.mercaso.ims.infrastructure.repository.plytixother.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Type;


@Entity
@Table(name = "plytix_other_data")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update plytix_other_data set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class PlytixOtherDataDo extends BaseDo {

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "sku")
    private String sku;

    @Column(name = "body_html")
    private String bodyHTML;

    @Column(name = "type")
    private String type;

    @Column(name = "variant_cost")
    private BigDecimal variantCost;

    @Column(name = "variant_fulfillment_service")
    private String variantFulfillmentService;

    @Column(name = "variant_inventory_policy")
    private String variantInventoryPolicy;

    @Column(name = "variant_inventory_tracker")
    private String variantInventoryTracker;

    @Column(name = "variant_requires_shipping")
    private Boolean variantRequiresShipping;

    @Column(name = "variant_taxable")
    private Boolean variantTaxable;

    @Column(name = "published_scope")
    private String publishedScope;

    @Column(name = "published_at")
    private String publishedAt;

    @Column(name = "upc_reference")
    private String upcReference;

    @Column(name = "awesome_price")
    private BigDecimal awesomePrice;

    @Column(name = "banner")
    private String banner;

    @Column(name = "banner_expiry")
    private String bannerExpiry;

    @Column(name = "banner_flag")
    private String bannerFlag;

    @Column(name = "clazz")
    private String clazz;

    @Column(name = "cooler")
    private Boolean cooler;

    @Column(name = "files_migration")
    private String filesMigration;

    @Column(name = "id_tag")
    private String idTag;

    @Column(name = "image_altText")
    private String imageAltText;

    @Column(name = "image_height")
    private Double imageHeight;

    @Column(name = "image_notes")
    private String imageNotes;

    @Column(name = "image_width")
    private Double imageWidth;

    @Column(name = "mfc_item")
    private Boolean mfcItem;

    @Column(name = "new_description")
    private String newDescription;

    @Column(name = "prop_65")
    private Boolean prop65;

    @Column(name = "secondary_handle")
    private String secondaryHandle;

    @Column(name = "variant_inventory_item_id")
    private String variantInventoryItemId;

    @Column(name = "variant_jc_show_app_price")
    private Double variantJCShowAppPrice;

    @Column(name = "venture_partners_cost")
    private BigDecimal venturePartnersCost;

    @Column(name = "product_id")
    private String productId;

    @Column(name = "variation_of")
    private String variationOf;

    @Column(name = "variants")
    private String variants;

    @Column(name = "assets")
    private String assets;

    @Column(name = "price_linking")
    private String priceLinking;

    @Column(name = "main_part_original_data")
    @Type(JsonType.class)
    private String mainPartOriginalData;

    @Column(name = "variant_pack_plus_crv_original_data")
    @Type(JsonType.class)
    private String variantPackPlusCrvOriginalData;

    @Column(name = "variant_individual_original_data")
    @Type(JsonType.class)
    private String variantIndividualOriginalData;

    @Column(name = "variant_pack_no_crv_original_data")
    @Type(JsonType.class)
    private String variantPackNoCrvOriginalData;


}
