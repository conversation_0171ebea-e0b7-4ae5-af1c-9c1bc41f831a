package com.mercaso.ims.infrastructure.repository.itemupc.jpa.mapper;

import com.mercaso.ims.domain.BaseValueObjectDoMapper;
import com.mercaso.ims.domain.item.ItemUPC;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ItemUPCDoMapper extends BaseValueObjectDoMapper<ItemUPCDo, ItemUPC> {

    ItemUPCDoMapper INSTANCE = Mappers.getMapper(ItemUPCDoMapper.class);


    @Override
    ItemUPC doToDomain(ItemUPCDo itemUPCDo);

    @Override
    ItemUPCDo domainToDo(ItemUPC itemUPC);
}
