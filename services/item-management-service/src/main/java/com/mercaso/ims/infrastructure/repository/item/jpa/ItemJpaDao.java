package com.mercaso.ims.infrastructure.repository.item.jpa;

import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ItemJpaDao extends JpaRepository<ItemDo, UUID> {

    ItemDo findBySkuNumber(String sku);

    Page<ItemDo> findByCreatedAtBetweenAndPhotoIsLikeIgnoreCase(Instant beginTime,
        Instant endTime,
        String photo,
        Pageable pageable);

    List<ItemDo> findAllByIdIn(List<UUID> id);

    Page<ItemDo> findByUpdatedAtBetween(Instant beginTime,
        Instant endTime,
        Pageable pageable);

    List<ItemDo> findByItemUPCs_UpcNumber(String upcNumber);

    List<ItemDo> findByCategoryId(UUID categoryId);

    List<ItemDo> findByBrandId(UUID brandId);

}
