package com.mercaso.ims.infrastructure.apitaskprocess;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Registry for managing all API task processors
 * Provides centralized access to processors and handles processor selection
 */
@Slf4j
@Component
public class ApiTaskProcessorRegistry {

    private final Map<String, ApiTaskProcessor<?>> processorMap = new HashMap<>();
    private final List<ApiTaskProcessor<?>> allProcessors;

    @Autowired
    public ApiTaskProcessorRegistry(List<ApiTaskProcessor<?>> processors) {
        this.allProcessors = processors;
    }

    @PostConstruct
    public void initializeRegistry() {
        log.info("Initializing API Task Processor Registry with {} processors", allProcessors.size());

        for (ApiTaskProcessor<?> processor : allProcessors) {
            String taskType = processor.getTaskType();
            
            // Skip processors that return null task type
            if (taskType == null) {
                log.warn("Skipping processor {} because it returns null task type", 
                        processor.getClass().getSimpleName());
                continue;
            }
            
            processorMap.put(taskType, processor);
            log.info("Registered processor {} for task type {}",
                    processor.getClass().getSimpleName(), taskType);
        }

        log.info("API Task Processor Registry initialized with {} processors", processorMap.size());
    }

    /**
     * Find a processor that can handle the given task type
     *
     * @param taskType The task type to find a processor for
     * @return The processor that can handle the task type, or null if none found
     */
    @SuppressWarnings("rawtypes")
    public ApiTaskProcessor findProcessorForTaskType(String taskType) {
        // First try exact match
        ApiTaskProcessor<?> processor = processorMap.get(taskType);
        if (processor != null) {
            log.info("Found exact match processor {} for task type {}",
                    processor.getClass().getSimpleName(), taskType);
            return processor;
        }

        // Then try processors that can handle the task type dynamically
        for (ApiTaskProcessor<?> candidateProcessor : allProcessors) {
            if (candidateProcessor.canProcess(taskType)) {
                log.info("Found dynamic processor {} for task type {}",
                        candidateProcessor.getClass().getSimpleName(), taskType);
                return candidateProcessor;
            }
        }

        log.warn("No processor found for task type: {}", taskType);
        return null;
    }

    /**
     * Get processor by exact task type match
     */
    @SuppressWarnings("rawtypes")
    public ApiTaskProcessor getProcessor(String taskType) {
        return processorMap.get(taskType);
    }

    /**
     * Check if a processor exists for the given task type
     */
    public boolean hasProcessor(String taskType) {
        return findProcessorForTaskType(taskType) != null;
    }

    /**
     * Get all supported task types
     */
    public String[] getSupportedTaskTypes() {
        return processorMap.keySet().toArray(new String[0]);
    }

    /**
     * Get processor statistics
     */
    public ProcessorStats getProcessorStats() {
        ProcessorStats stats = new ProcessorStats();
        stats.setTotalProcessors(allProcessors.size());
        stats.setRegisteredProcessors(processorMap.size());

        // Count finale-specific processors (with null safety)
        long finaleProcessors = processorMap.keySet().stream()
                .filter(taskType -> taskType != null && taskType.startsWith("FINALE_"))
                .count();
        stats.setFinaleProcessors((int) finaleProcessors);

        // Count universal processors (with null safety)
        long universalProcessors = allProcessors.stream()
                .filter(processor -> {
                    String taskType = processor.getTaskType();
                    return taskType != null && (taskType.contains("UNIVERSAL") || taskType.contains("ANNOTATION"));
                })
                .count();
        stats.setUniversalProcessors((int) universalProcessors);

        return stats;
    }

    /**
     * Statistics about registered processors
     */
    @Data
    @ToString
    public static class ProcessorStats {
        private int totalProcessors;
        private int registeredProcessors;
        private int finaleProcessors;
        private int universalProcessors;
    }
}
