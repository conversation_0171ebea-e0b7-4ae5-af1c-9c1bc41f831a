package com.mercaso.ims.infrastructure.repository.exceptionrecord.jpa;

import com.mercaso.ims.application.dto.ItemPriceExceptionRecordDto;
import com.mercaso.ims.application.dto.VendorItemCostExceptionRecordDto;
import com.mercaso.ims.application.query.ExceptionRecordQuery.SortType;
import com.mercaso.ims.application.query.ItemPriceExceptionRecordQuery;
import com.mercaso.ims.application.query.VendorItemCostExceptionRecordQuery;
import com.mercaso.ims.domain.exceptionrecord.enums.EntityType;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordStatus;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordType;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.infrastructure.util.DateUtils;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedExceptionRecordJpaDaoImpl implements CustomizedExceptionRecordJpaDao {

    private static final String QUERY_ITEM_PRICE_EXCEPTION_RECORD_DTO_LIST = """
        SELECT
                                     er.id AS id,
                                     er.business_event_id,
                                     er.description,
                                     er.note,
                                     er.entity_id,
                                     er.entity_type,
                                     er.exception_type,
                                     er.status,
                                     er.created_at,
                                     er.created_by,
                                     er.created_user_name,
                                     i.sku_number AS sku_number,
                                     i.title AS item_title,
                                     i.id AS item_id,
                                     i.availability_status,
                                     i.primary_vendor_id,
                                         COALESCE(vi.pack_plus_crv_cost, vi.pack_no_crv_cost) AS primary_cost,
                                         CASE WHEN be.type = 'ITEM_AMEND' THEN be.payload -> 'data' -> 'previous' ->'itemRegPrice' ->> 'regPrice'
                                             ELSE NULL END AS previous_price,
                                         CASE WHEN be.type = 'ITEM_AMEND' THEN be.payload -> 'data' -> 'current' ->'itemRegPrice' ->> 'regPrice'
                                              WHEN be.type = 'ITEM_CREATED' THEN be.payload -> 'data' -> 'itemRegPrice' ->> 'regPrice'
                                              ELSE NULL END AS target_price
                                 FROM
                                      exception_record er
                                          LEFT JOIN
                                     item i ON er.entity_id =i.id
                                          LEFT JOIN
                                     vendor_item vi ON vi.item_id =i.id and vi.vendor_id=i.primary_vendor_id
                                         LEFT JOIN
                                     business_event be on er.business_event_id = be.id
                                 WHERE
                                     er.deleted_at  IS NULL
                                     and er.exception_type ='PRICE_EXCEPTION'
                                     and er.entity_type='ITEM'
        """;

    private static final String ITEM_PRICE_COUNT_QUERY = """
        SELECT count(er.*)
        FROM
            exception_record er
                LEFT JOIN
            item i ON er.entity_id =i.id
        WHERE
            er.deleted_at  IS NULL
            and er.exception_type ='PRICE_EXCEPTION'
            and er.entity_type='ITEM'
        """;


    private static final String QUERY_VENDOR_ITEM_COST_EXCEPTION_RECORD_DTO_LIST = """
        SELECT
            er.id AS id,
            er.business_event_id,
            er.description,
            er.note,
            er.entity_id,
            er.entity_type,
            er.exception_type,
            er.status,
            er.created_at,
            er.created_by,
            er.created_user_name,
            i.sku_number AS sku_number,
            i.title AS item_title,
            i.id AS item_id,
            i.availability_status,
            i.primary_vendor_id,
            v.vendor_name,
            vi.vendor_id,
            vi.vendor_sku_number,
            COALESCE(irg.reg_price_plus_crv, irg.reg_price) AS reg_price,
            CASE WHEN  i.primary_vendor_id = vi.vendor_id THEN true ELSE false END AS is_primary_vendor,
            CASE WHEN be.type = 'VENDOR_ITEM_AMEND' THEN be.payload -> 'data' -> 'previous' ->> 'cost'
                 ELSE NULL END AS previous_cost,
            CASE WHEN be.type = 'VENDOR_ITEM_AMEND' THEN be.payload -> 'data' -> 'current' ->> 'cost'
                 WHEN be.type = 'VENDOR_ITEM_CREATED' THEN be.payload -> 'data'  ->> 'cost'
                 ELSE NULL END AS target_cost
        FROM
            exception_record er
                LEFT JOIN
            vendor_item vi ON er.entity_id =vi.id
                LEFT JOIN
            vendor v on v.id= vi.vendor_id
                LEFT JOIN
            item i ON vi.item_id = i.id
                LEFT JOIN
            item_reg_price irg ON i.id = irg.item_id
                LEFT JOIN
            business_event be on er.business_event_id = be.id
        WHERE
            er.deleted_at  IS NULL
          and er.exception_type  in ('PO_COST_EXCEPTION', 'JIT_COST_EXCEPTION')
          and er.entity_type='VENDOR_ITEM'
        """;

    private static final String VENDOR_ITEM_COST_COUNT_QUERY = """
        SELECT count(er.*)
        FROM
            exception_record er
                LEFT JOIN
            vendor_item vi ON er.entity_id =vi.id
                LEFT JOIN
            item i ON vi.item_id = i.id
        WHERE
            er.deleted_at  IS NULL
          and er.exception_type in ('PO_COST_EXCEPTION','JIT_COST_EXCEPTION')
          and er.entity_type='VENDOR_ITEM'
        """;
    private final NamedParameterJdbcTemplate jdbcTemplate;


    @Override
    public List<ItemPriceExceptionRecordDto> fetchItemPriceExceptionRecordDtoList(ItemPriceExceptionRecordQuery query) {

        StringBuilder sql = new StringBuilder(QUERY_ITEM_PRICE_EXCEPTION_RECORD_DTO_LIST);
        sql.append(buildCreatedAtCondition(query.getCreatedAtBegin(), query.getCreatedAtEnd()));
        sql.append(buildStatusCondition(query.getStatus()));
        sql.append(buildCreatedByCondition(query.getCreatedBy()));
        sql.append(buildSkuNumberCondition(query.getSkuNumber()));
        sql.append(buildItemTitleCondition(query.getItemTitle()));
        sql.append(buildItemStatusCondition(query.getItemStatus()));
        sql.append(this.buildSortSql(query.getSort()));
        sql.append(query.offsetLimitSql());

        Map<String, Object> params = SerializationUtils.entityToMap(query);

        return jdbcTemplate.query(
            sql.toString(),
            params,
            (rs, rowNumber) -> ItemPriceExceptionRecordDto.builder()
                .id(UUID.fromString(rs.getString("id")))
                .status(ExceptionRecordStatus.valueOf(rs.getString("status")))
                .exceptionType(ExceptionRecordType.valueOf(rs.getString("exception_type")))
                .entityType(EntityType.valueOf(rs.getString("entity_type")))
                .businessEventId(UUID.fromString(rs.getString("business_event_id")))
                .createdAt(DateUtils.fromTimestamp(rs.getTimestamp("created_at")))
                .createdBy(rs.getString("created_by"))
                .createdUserName(rs.getString("created_user_name"))
                .description(rs.getString("description"))
                .note(rs.getString("note"))
                .skuNumber(rs.getString("sku_number"))
                .itemTitle(rs.getString("item_title"))
                .itemId(UUID.fromString(rs.getString("item_id")))
                .itemStatus(rs.getString("availability_status"))
                .primaryCost(rs.getBigDecimal("primary_cost"))
                .previousPrice(rs.getBigDecimal("previous_price"))
                .targetPrice(rs.getBigDecimal("target_price"))
                .build());
    }

    @Override
    public List<VendorItemCostExceptionRecordDto> fetchVendorItemCostExceptionRecordDtoList(VendorItemCostExceptionRecordQuery query) {
        StringBuilder sql = new StringBuilder(QUERY_VENDOR_ITEM_COST_EXCEPTION_RECORD_DTO_LIST);
        sql.append(buildCreatedAtCondition(query.getCreatedAtBegin(), query.getCreatedAtEnd()));
        sql.append(buildStatusCondition(query.getStatus()));
        sql.append(buildCreatedByCondition(query.getCreatedBy()));
        sql.append(buildSkuNumberCondition(query.getSkuNumber()));
        sql.append(buildItemTitleCondition(query.getItemTitle()));
        sql.append(buildVendorIdCondition(query.getVendorId()));
        sql.append(buildItemStatusCondition(query.getItemStatus()));
        sql.append(this.buildSortSql(query.getSort()));
        sql.append(query.offsetLimitSql());

        Map<String, Object> params = SerializationUtils.entityToMap(query);

        return jdbcTemplate.query(
            sql.toString(),
            params,
            (rs, rowNumber) -> VendorItemCostExceptionRecordDto.builder()
                .id(UUID.fromString(rs.getString("id")))
                .status(ExceptionRecordStatus.valueOf(rs.getString("status")))
                .exceptionType(ExceptionRecordType.valueOf(rs.getString("exception_type")))
                .entityType(EntityType.valueOf(rs.getString("entity_type")))
                .businessEventId(UUID.fromString(rs.getString("business_event_id")))
                .createdAt(DateUtils.fromTimestamp(rs.getTimestamp("created_at")))
                .createdBy(rs.getString("created_by"))
                .createdUserName(rs.getString("created_user_name"))
                .description(rs.getString("description"))
                .note(rs.getString("note"))
                .skuNumber(rs.getString("sku_number"))
                .itemTitle(rs.getString("item_title"))
                .itemId(UUID.fromString(rs.getString("item_id")))
                .itemStatus(rs.getString("availability_status"))
                .vendorName(rs.getString("vendor_name"))
                .vendorSkuNumber(rs.getString("vendor_sku_number"))
                .isPrimaryVendor(rs.getBoolean("is_primary_vendor"))
                .regPrice(rs.getBigDecimal("reg_price"))
                .previousCost(rs.getBigDecimal("previous_cost"))
                .targetCost(rs.getBigDecimal("target_cost"))
                .vendorId(UUID.fromString(rs.getString("vendor_id")))
                .primaryVendorId(UUID.fromString(rs.getString("primary_vendor_id")))
                .build());
    }

    @Override
    public long itemPriceExceptionRecordCountQuery(ItemPriceExceptionRecordQuery query) {
        StringBuilder sql = new StringBuilder(ITEM_PRICE_COUNT_QUERY);
        sql.append(buildCreatedAtCondition(query.getCreatedAtBegin(), query.getCreatedAtEnd()));
        sql.append(buildStatusCondition(query.getStatus()));
        sql.append(buildCreatedByCondition(query.getCreatedBy()));
        sql.append(buildSkuNumberCondition(query.getSkuNumber()));
        sql.append(buildItemTitleCondition(query.getItemTitle()));
        Map<String, Object> params = SerializationUtils.entityToMap(query);

        Long count = jdbcTemplate.queryForObject(sql.toString(), params, Long.class);

        return count != null ? count : 0;

    }

    @Override
    public long vendorItemCostExceptionRecordCountQuery(VendorItemCostExceptionRecordQuery query) {
        StringBuilder sql = new StringBuilder(VENDOR_ITEM_COST_COUNT_QUERY);
        sql.append(buildCreatedAtCondition(query.getCreatedAtBegin(), query.getCreatedAtEnd()));
        sql.append(buildStatusCondition(query.getStatus()));
        sql.append(buildCreatedByCondition(query.getCreatedBy()));
        sql.append(buildSkuNumberCondition(query.getSkuNumber()));
        sql.append(buildItemTitleCondition(query.getItemTitle()));
        sql.append(buildVendorIdCondition(query.getVendorId()));
        sql.append(buildItemStatusCondition(query.getItemStatus()));
        Map<String, Object> params = SerializationUtils.entityToMap(query);
        Long count = jdbcTemplate.queryForObject(sql.toString(), params, Long.class);

        return count != null ? count : 0;

    }


    private String buildCreatedAtCondition(Instant createdAtBegin, Instant createdAtEnd) {
        if (null != createdAtBegin && null != createdAtEnd) {
            return " AND er.created_at BETWEEN :createdAtBegin AND :createdAtEnd ";
        }
        return StringUtils.EMPTY;
    }

    private String buildStatusCondition(ExceptionRecordStatus status) {
        if (null != status) {
            return " AND er.status = :status ";
        }
        return StringUtils.EMPTY;
    }

    private String buildCreatedByCondition(String createdBy) {
        if (StringUtils.isNotBlank(createdBy)) {
            return " AND er.created_by = :createdBy ";
        }
        return StringUtils.EMPTY;
    }

    private String buildSkuNumberCondition(String skuNumber) {
        if (StringUtils.isNotBlank(skuNumber)) {
            return " AND i.sku_number = :skuNumber ";
        }
        return StringUtils.EMPTY;
    }

    private String buildItemTitleCondition(String itemTitle) {
        if (StringUtils.isNotBlank(itemTitle)) {
            return " AND i.title ilike CONCAT('%', :itemTitle, '%') ";
        }
        return StringUtils.EMPTY;
    }

    private String buildItemStatusCondition(AvailabilityStatus itemStatus) {
        if (null != itemStatus) {
            return " AND i.availability_status = :itemStatus ";
        }
        return StringUtils.EMPTY;
    }


    private String buildVendorIdCondition(UUID vendorId) {
        if (null != vendorId) {
            return " AND vi.vendor_id = :vendorId ";
        }
        return StringUtils.EMPTY;
    }


    private String buildSortSql(SortType sort) {
        String sql = StringUtils.EMPTY;
        if (sort == null || sort == SortType.CREATED_AT_DESC) {
            return " ORDER BY er.created_at DESC ";
        }
        return sql;
    }
}
