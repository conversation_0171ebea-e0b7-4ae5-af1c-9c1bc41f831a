package com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.mapper;

import com.mercaso.ims.domain.attributeenumvalue.AttributeEnumValue;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.dataobject.AttributeEnumValueDo;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface AttributeEnumValueDoMapper extends BaseDoMapper<AttributeEnumValueDo, AttributeEnumValue> {
}