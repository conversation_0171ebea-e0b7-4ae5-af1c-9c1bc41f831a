package com.mercaso.ims.infrastructure.repository.itemversion.jpa.mapper;

import com.mercaso.ims.domain.itemversion.ItemVersion;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.itemversion.jpa.dataobject.ItemVersionDo;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ItemVersionDoMapper extends BaseDoMapper<ItemVersionDo, ItemVersion> {

    @Override
    ItemVersion doToDomain(ItemVersionDo itemVersionDo);

    @Override
    ItemVersionDo domainToDo(ItemVersion itemVersion);
}
