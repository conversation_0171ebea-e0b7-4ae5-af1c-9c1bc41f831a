package com.mercaso.ims.infrastructure.external.downey;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.INVOKE_DOWNEY_API_ERROR;

import com.mercaso.ims.infrastructure.config.metrics.MetricsTypeEnum;
import com.mercaso.ims.infrastructure.config.metrics.ReportMetric;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class DowneyAdaptor {

    @Value("${downey.search_url}")
    private String downeySearchUrl;

    @ReportMetric(metricsType = MetricsTypeEnum.QUERY_SHOPIFY_PRODUCT)
    public String queryDowneyItemNumber(String upcNumber) {
        log.info("[queryDowneyItemNumber] request body: {}", upcNumber);

        if (StringUtils.isBlank(upcNumber)) {
            log.warn("[queryDowneyItemNumber] upcNumber is blank");
            return null;
        }

        String itemNumber = null;
        try {
            Document document = Jsoup.connect(downeySearchUrl + upcNumber).get();
            Elements elements = document.select("div.fusion-post-content-container p");

            Pattern pattern = Pattern.compile("ITEM #(\\d+)");


            for (Element element : elements) {
                Matcher matcher = pattern.matcher(element.text());
                if (matcher.find()) {
                    itemNumber = matcher.group(1);
                    log.info("[queryDowneyItemNumber] vendorItemNumber : {}", itemNumber);
                }
            }
        } catch (Exception e) {
            throw new ImsBusinessException(INVOKE_DOWNEY_API_ERROR);
        }

        return itemNumber;
    }
}
