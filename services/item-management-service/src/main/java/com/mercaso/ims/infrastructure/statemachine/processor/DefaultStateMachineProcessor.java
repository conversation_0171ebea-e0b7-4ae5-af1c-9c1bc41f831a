package com.mercaso.ims.infrastructure.statemachine.processor;


import com.mercaso.ims.infrastructure.statemachine.StateTransitionType;
import com.mercaso.ims.infrastructure.statemachine.StateType;
import com.mercaso.ims.infrastructure.statemachine.StatefulContext;
import com.mercaso.ims.infrastructure.statemachine.factory.ImsStateMachineFactory;
import com.mercaso.ims.infrastructure.statemachine.factory.StateEventHandler;
import org.springframework.stereotype.Component;

@Component
public class DefaultStateMachineProcessor<T extends StatefulContext<S>, S extends StateType, E extends StateTransitionType> implements StateMachineProcessor<T, S, E> {

    @Override
    public void processEvent(T domain, E event) {
        ImsStateMachineFactory<S, E, T> stateMachineFactory = this.getStateMachineFactory(domain);

        StateEventHandler.processEvent(stateMachineFactory, domain, event);
    }
}
