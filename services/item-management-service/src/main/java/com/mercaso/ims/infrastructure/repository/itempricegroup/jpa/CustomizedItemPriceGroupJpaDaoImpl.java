package com.mercaso.ims.infrastructure.repository.itempricegroup.jpa;

import com.mercaso.ims.application.dto.ItemPriceGroupItemDto;
import com.mercaso.ims.application.dto.ItemPriceGroupListItemDto;
import com.mercaso.ims.application.query.ItemPriceGroupQuery;
import com.mercaso.ims.application.query.ItemPriceGroupQuery.SortType;
import com.mercaso.ims.infrastructure.repository.DynamicSearchCondition;
import com.mercaso.ims.infrastructure.repository.SearchConditionResolver;
import com.mercaso.ims.infrastructure.util.DateUtils;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedItemPriceGroupJpaDaoImpl implements CustomizedItemPriceGroupJpaDao {

    private static final String QUERY_ITEM_PRICE_GROUP_DTO_LIST = """
        SELECT
            ipg.*,
            COUNT(irg.id) AS item_count
        FROM
            item_price_group ipg
                LEFT JOIN
            item_reg_price irg
            ON ipg.id = irg.item_price_group_id
                AND irg.deleted_at IS NULL
            LEFT JOIN  item i on irg.item_id = i.id
        WHERE
            ipg.deleted_at IS NULL AND i.deleted_at is null
        """;

    private static final String COUNT_QUERY = """
        SELECT count(ipg.*)
               FROM  item_price_group ipg
        WHERE
            ipg.deleted_at IS NULL
        """;


    private static final String GROUP_SQL = """
        GROUP BY ipg.id
        """;


    private static final String QUERY_ITEM_PRICE_GROUP_ITEM_DTO_LIST = """
        SELECT COALESCE(irg.reg_price, irg.reg_price_plus_crv) AS reg_price,
                                            i.id as item_id,
                                            i.title as item_title,
                                            i.sku_number AS sku_number,
                                            i.availability_status,
                                            COALESCE(vi.pack_plus_crv_cost, vi.pack_no_crv_cost) AS cost,
                                            vi_backup.backup_pack_plus_crv_cost AS backup_cost
        FROM item_reg_price irg
        LEFT JOIN  item i on irg.item_id = i.id
        LEFT JOIN  vendor_item vi ON i.primary_vendor_id = vi.vendor_id AND i.id = vi.item_id
        LEFT JOIN  vendor_item vi_backup ON i.backup_vendor_id = vi_backup.vendor_id AND i.id = vi_backup.item_id
        WHERE
            irg.deleted_at is null AND i.deleted_at is null
        """;

    private final JdbcTemplate jdbcTemplate;


    @Override
    public List<ItemPriceGroupListItemDto> fetchItemPriceGroupDtoList(ItemPriceGroupQuery query) {

        DynamicSearchCondition<ItemPriceGroupQuery> dynamicSearch = new ItemPriceGroupDynamicSearch();

        StringBuilder sql = new StringBuilder(QUERY_ITEM_PRICE_GROUP_DTO_LIST);
        sql.append(dynamicSearch.generateConditionBlock(query));
        sql.append(GROUP_SQL);
        sql.append(this.buildSortSql(query.getSort()));
        sql.append(query.offsetLimitSql());

        return jdbcTemplate.query(sql.toString(), ps -> dynamicSearch.bindSqlParameter(ps, query),
            (rs, rowNumber) -> ItemPriceGroupListItemDto.builder()
                .id(UUID.fromString(rs.getString("id")))
                .groupName(rs.getString("group_name"))
                .price(rs.getBigDecimal("price"))
                .createdAt(DateUtils.fromTimestamp(rs.getTimestamp("created_at")))
                .createdBy(rs.getString("created_by"))
                .createdUserName(rs.getString("created_user_name"))
                .createdUserName(rs.getString("created_user_name"))
                .itemCount(rs.getInt("item_count"))
                .build());
    }

    @Override
    public long countQuery(ItemPriceGroupQuery query) {
        DynamicSearchCondition<ItemPriceGroupQuery> dynamicSearch = new ItemPriceGroupDynamicSearch();
        StringBuilder sql = new StringBuilder(COUNT_QUERY);
        sql.append(dynamicSearch.generateConditionBlock(query));

        List<Long> count = jdbcTemplate.query(sql.toString(),
            ps -> dynamicSearch.bindSqlParameter(ps, query),
            (rs, rowNum) -> rs.getLong(1));

        return count.getFirst();
    }

    @Override
    public List<ItemPriceGroupItemDto> getItemPriceGroupItemDto(UUID itemPriceGroupId) {
        StringBuilder sql = new StringBuilder(QUERY_ITEM_PRICE_GROUP_ITEM_DTO_LIST);
        sql.append("and irg.item_price_group_id ='" + itemPriceGroupId + "'");
        return jdbcTemplate.query(sql.toString(),
            (rs, rowNumber) -> ItemPriceGroupItemDto.builder()
                .id(UUID.fromString(rs.getString("item_id")))
                .itemTitle(rs.getString("item_title"))
                .skuNumber(rs.getString("sku_number"))
                .availabilityStatus(rs.getString("availability_status"))
                .price(rs.getBigDecimal("reg_price"))
                .cost(rs.getBigDecimal("cost"))
                .backupCost(rs.getBigDecimal("backup_cost"))
                .build());
    }

    static class ItemPriceGroupDynamicSearch extends DynamicSearchCondition<ItemPriceGroupQuery> {

        public ItemPriceGroupDynamicSearch() {
            super(List.of(new CreatedAtCondition(),
                new CreatedByCondition(),
                new GroupNameCondition()));
        }
    }

    private static class CreatedAtCondition implements SearchConditionResolver<ItemPriceGroupQuery> {

        @Override
        public String generateConditionBlock(ItemPriceGroupQuery query) {
            if (null == query.getCreatedAtBegin() || null == query.getCreatedAtEnd()) {
                return StringUtils.EMPTY;
            }
            return " AND ipg.created_at BETWEEN ? AND ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemPriceGroupQuery query, int index) throws SQLException {

            if (null != query.getCreatedAtBegin() && null != query.getCreatedAtEnd()) {
                ps.setTimestamp(index++, Timestamp.from(query.getCreatedAtBegin()));
                ps.setTimestamp(index++, Timestamp.from(query.getCreatedAtEnd()));
            }
            return index;
        }
    }

    private static class CreatedByCondition implements SearchConditionResolver<ItemPriceGroupQuery> {

        @Override
        public String generateConditionBlock(ItemPriceGroupQuery query) {
            if (null == query.getCreatedBy()) {
                return StringUtils.EMPTY;
            }
            return " AND ipg.created_by = ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemPriceGroupQuery query, int index) throws SQLException {

            if (StringUtils.isNotBlank(query.getCreatedBy())) {
                ps.setString(index++, query.getCreatedBy());
            }
            return index;
        }
    }

    private static class GroupNameCondition implements SearchConditionResolver<ItemPriceGroupQuery> {

        @Override
        public String generateConditionBlock(ItemPriceGroupQuery query) {
            if (null == query.getGroupName()) {
                return StringUtils.EMPTY;
            }
            return " AND ipg.group_name ILIKE ? ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemPriceGroupQuery query, int index) throws SQLException {

            if (StringUtils.isNotBlank(query.getGroupName())) {
                ps.setString(index++, "%" + query.getGroupName() + "%");
            }
            return index;
        }
    }

    private String buildSortSql(SortType sortType) {
        if (sortType == null) {
            return " ORDER BY ipg.created_at DESC ";
        }

        return switch (sortType) {
            case CREATED_AT_DESC -> " ORDER BY ipg.created_at DESC ";
            case CREATED_AT_ASC -> " ORDER BY ipg.created_at ASC ";
            case GROUP_NAME_DESC -> " ORDER BY ipg.group_name DESC ";
            case GROUP_NAME_ASC -> " ORDER BY ipg.group_name ASC ";
            case PRICE_DESC -> " ORDER BY ipg.price DESC ";
            case PRICE_ASC -> " ORDER BY ipg.price ASC ";
        };
    }

}
