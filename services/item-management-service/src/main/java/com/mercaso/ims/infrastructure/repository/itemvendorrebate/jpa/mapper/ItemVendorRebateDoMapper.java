package com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.mapper;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.dataobject.ItemVendorRebateDo;
import org.mapstruct.Mapper;


/**
 * Mapper for converting between ItemVendorRebate domain entity and ItemVendorRebateDo data object
 */
@Mapper(componentModel = "spring")
public interface ItemVendorRebateDoMapper extends BaseDoMapper<ItemVendorRebateDo, ItemVendorRebate> {

    ItemVendorRebate doToDomain(ItemVendorRebateDo itemVendorRebateDo);

    ItemVendorRebateDo domainToDo(ItemVendorRebate itemVendorRebate);
}
