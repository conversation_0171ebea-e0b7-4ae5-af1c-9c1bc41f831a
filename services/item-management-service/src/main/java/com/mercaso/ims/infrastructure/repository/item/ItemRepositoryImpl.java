package com.mercaso.ims.infrastructure.repository.item;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_NOT_FOUND;

import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.item.jpa.ItemJpaDao;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.item.jpa.mapper.ItemDoMapper;
import com.mercaso.ims.infrastructure.repository.itemgrade.jpa.ItemGradeJpaDao;
import com.mercaso.ims.infrastructure.repository.itemgrade.jpa.dataobject.ItemGradeDo;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.ItemUPCJpaDao;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.*;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class ItemRepositoryImpl implements ItemRepository {

    private final ItemJpaDao itemJpaDao;

    private final ItemDoMapper itemDoMapper;

    private final ItemUPCJpaDao itemUPCJpaDao;

    private final ItemGradeJpaDao itemGradeJpaDao;


    @Override
    public Item save(Item item) {
        ItemDo itemDo = itemJpaDao.save(itemDoMapper.domainToDo(item));
        return this.doToDomain(itemDo);
    }

    @Override
    public Item findById(UUID id) {
        ItemDo itemDo = itemJpaDao.findById(id).orElse(null);
        log.info("[ItemRepository] find itemDo by id :{}", itemDo);
        if (null == itemDo) {
            return null;
        }
        return this.doToDomain(itemDo);
    }

    @Override
    public Item update(Item domain) {
        ItemDo itemDo = itemJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(itemDo)) {
            throw new ImsBusinessException(ITEM_NOT_FOUND.getCode());
        }
        ItemDo itemDoTarget = itemDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList("itemAttributes",
            "itemTags",
            "itemImages",
            "itemUPCs",
            "createdBy",
            "createdAt");
        BeanUtils.copyProperties(itemDoTarget, itemDo, ignoreProperties.toArray(new String[0]));
        ItemDo result = itemJpaDao.save(itemDo);
        return this.doToDomain(result);
    }

    @Override
    public Item deleteById(UUID id) {
        ItemDo itemDo = itemJpaDao.findById(id).orElse(null);
        if (null == itemDo) {
            return null;
        }

        Optional.ofNullable(itemDo.getItemUPCs()).ifPresent(List::clear);
        Optional.ofNullable(itemDo.getItemTags()).ifPresent(List::clear);
        Optional.ofNullable(itemDo.getItemAttributes()).ifPresent(List::clear);
        Optional.ofNullable(itemDo.getItemImages()).ifPresent(List::clear);

        itemDo.setDeletedAt(Instant.now());
        itemDo.setDeletedBy(SecurityUtil.getLoginUserId());
        itemDo.setDeletedUserName(SecurityUtil.getUserName());
        return this.doToDomain(itemJpaDao.save(itemDo));
    }

    @Override
    public Item findBySku(String sku) {
        log.info("findBySku :{}", sku);
        ItemDo itemDo = itemJpaDao.findBySkuNumber(sku);
        if (null == itemDo) {
            log.warn("findBySku error sku:{} not found", sku);
            return null;
        }
        log.info("findBySku success :{}", itemDo);
        return this.doToDomain(itemDo);
    }

    @Override
    public Page<Item> findByCreatedAtBetween(Instant begin, Instant end, String photo, Pageable pageable) {
        Page<ItemDo> byCreatedAtBefore = itemJpaDao.findByCreatedAtBetweenAndPhotoIsLikeIgnoreCase(begin, end, photo, pageable);
        return byCreatedAtBefore.map(this::doToDomain);
    }

    @Override
    public Page<Item> findAll(Pageable pageable) {
        Page<ItemDo> itemDoPage = itemJpaDao.findAll(pageable);
        return itemDoPage.map(this::doToDomain);
    }

    @Override
    public List<Item> findAllByIdIn(List<UUID> ids) {
        List<ItemDo> itemDos = itemJpaDao.findAllByIdIn(ids);
        return itemDos.stream().map(this::doToDomain).toList();
    }

    @Override
    public List<Item> findAllByUpcAndUpcType(String upc, ItemUpcType upcType) {
        List<ItemUPCDo> itemUPCDos = itemUPCJpaDao.findAllByUpcNumberAndItemUpcType(upc, upcType);
        if (null != itemUPCDos && !itemUPCDos.isEmpty()) {
            List<UUID> itemIds = itemUPCDos.stream().map(ItemUPCDo::getItemId).toList();
            return itemJpaDao.findAllByIdIn(itemIds).stream().map(this::doToDomain).toList();
        }
        return List.of();
    }

    @Override
    public Page<Item> findByUpdatedAtBetween(Instant begin, Instant end, Pageable pageable) {
        Page<ItemDo> byUpdatedAtBetween = itemJpaDao.findByUpdatedAtBetween(begin, end, pageable);
        return byUpdatedAtBetween.map(this::doToDomain);
    }

    @Override
    public List<Item> findByUpcNumber(String upcNumber) {
        List<ItemDo> itemDos = itemJpaDao.findByItemUPCs_UpcNumber(upcNumber);
        if (null == itemDos) {
            return List.of();
        }
        return itemDos.stream().map(this::doToDomain).toList();
    }

    @Override
    public List<Item> findByCategoryId(UUID categoryId) {
        List<ItemDo> itemDos = itemJpaDao.findByCategoryId(categoryId);
        if (null == itemDos) {
            return List.of();
        }
        return itemDos.stream().map(this::doToDomain).toList();
    }

    @Override
    public List<Item> findByBrandId(UUID brandId) {
        List<ItemDo> itemDos = itemJpaDao.findByBrandId(brandId);
        if (null == itemDos) {
            return List.of();
        }
        return itemDos.stream().map(this::doToDomain).toList();
    }


    private Item doToDomain(ItemDo itemDo) {
        Item item = itemDoMapper.doToDomain(itemDo);
        if (item != null && null == item.getItemGrade()) {
            ItemGradeDo itemGradeDo = itemGradeJpaDao.findByItemId(item.getId().toString());
            if (null != itemGradeDo) {
                item.setItemGrade(itemGradeDo.getGrade());
            }
        }
        return item;
    }

}
