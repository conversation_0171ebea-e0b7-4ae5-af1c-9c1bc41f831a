package com.mercaso.ims.infrastructure.repository.itemattribute.jpa.dataobject;

import com.mercaso.ims.domain.item.enums.AttributeType;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;


@Entity
@Table(name = "item_attribute")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_attribute set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemAttributeDo extends BaseDo {

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "attribute_id")
    private UUID attributeId;

    @Column(name = "value")
    private String value;

    @Column(name = "unit")
    private String unit;

    @Column(name = "attribute_type")
    @Enumerated(EnumType.STRING)
    private AttributeType attributeType;

    @Column(name = "sort")
    private Integer sort;


}
