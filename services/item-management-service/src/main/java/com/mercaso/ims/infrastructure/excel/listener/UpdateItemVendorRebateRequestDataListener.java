package com.mercaso.ims.infrastructure.excel.listener;

import static com.mercaso.ims.infrastructure.util.FormatUtils.cleanInput;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.command.CreateItemAdjustmentRequestDetailCommand;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestApplicationService;
import com.mercaso.ims.application.service.ItemAdjustmentRequestDetailApplicationService;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentFailureReason;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentType;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import com.mercaso.ims.infrastructure.excel.data.UpdateItemVendorRebateRequestData;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
public class UpdateItemVendorRebateRequestDataListener extends ItemAdjustmentRequestDataListener<UpdateItemVendorRebateRequestData> {

    public UpdateItemVendorRebateRequestDataListener(UUID itemAdjustmentRequestId,
                                                     ItemAdjustmentRequestDetailApplicationService itemAdjustmentRequestDetailApplicationService,
                                                     ItemAdjustmentRequestApplicationService itemAdjustmentRequestApplicationService,
                                                     ItemRepository itemRepository,
                                                     VendorRepository vendorRepository,
                                                     VendorItemRepository vendorItemRepository,
                                                     CategoryApplicationService categoryApplicationService,
                                                     FeatureFlagsManager featureFlagsManager, BrandRepository brandRepository) {
        super(itemAdjustmentRequestId,
            itemAdjustmentRequestDetailApplicationService,
            itemAdjustmentRequestApplicationService,
            itemRepository,
            vendorRepository,
            vendorItemRepository,
            categoryApplicationService,
            featureFlagsManager, brandRepository);
    }

    @Override
    CreateItemAdjustmentRequestDetailCommand convertToCreateItemAdjustmentRequestDetailCommand(UpdateItemVendorRebateRequestData updateItemVendorRebateRequestData) {
        return CreateItemAdjustmentRequestDetailCommand.builder()
            .requestId(itemAdjustmentRequestId)
            .type(getItemAdjustmentType())
            .sku(updateItemVendorRebateRequestData.getSku())
            .vendor(updateItemVendorRebateRequestData.getVendor())
            .rebateStartDate(updateItemVendorRebateRequestData.getStartDate())
            .rebateEndDate(updateItemVendorRebateRequestData.getEndDate())
            .rebatePerSellingUnit(updateItemVendorRebateRequestData.getRebatePerSellingUnit())
            .build();
    }


    @Override
    ItemAdjustmentType getItemAdjustmentType() {
        return ItemAdjustmentType.UPDATE_REBATE;
    }

    @Override
    List<ItemAdjustmentFailureReason> validateInput(UpdateItemVendorRebateRequestData updateItemVendorRebateRequestData) {
        List<ItemAdjustmentFailureReason> failureReasons = new ArrayList<>();
        String skuNumber = cleanInput(updateItemVendorRebateRequestData.getSku());

        Item item = itemRepository.findBySku(skuNumber);
        if (item == null) {
            failureReasons.add(ItemAdjustmentFailureReason.ITEM_NOT_FOUND);
            return failureReasons;
        }
        if (StringUtils.isBlank(updateItemVendorRebateRequestData.getVendor())) {
            failureReasons.add(ItemAdjustmentFailureReason.VENDOR_NAME_IS_REQUIRED);
            return failureReasons;
        }

        String vendorName = updateItemVendorRebateRequestData.getVendor();
        ItemAdjustmentFailureReason validateVendorResult = validateVendor(vendorName);
        if (validateVendorResult != null) {
            failureReasons.add(validateVendorResult);
            return failureReasons;
        }

        Vendor vendor = vendorRepository.findByVendorName(vendorName);

        if (Boolean.TRUE.equals(vendor.getExternalPicking())) {
            failureReasons.add(ItemAdjustmentFailureReason.SUPPLIER_MUST_BE_DIRECT);
        }

        ItemAdjustmentFailureReason validateVendorItemResult = validateVendorItem(item.getId(), vendor.getId());
        if (validateVendorItemResult != null) {
            failureReasons.add(validateVendorItemResult);
        }

        String startDate = updateItemVendorRebateRequestData.getStartDate();
        String endDate = updateItemVendorRebateRequestData.getEndDate();
        ItemAdjustmentFailureReason validateRebateDateWindowResult = checkRebateDateWindow(startDate, endDate);
        if (validateRebateDateWindowResult != null) {
            failureReasons.add(validateRebateDateWindowResult);
        }


        String rebatePerSellingUnit = updateItemVendorRebateRequestData.getRebatePerSellingUnit();
        ItemAdjustmentFailureReason validateRebatePerSellingUnitResult = checkRebatePerSellingUnit(rebatePerSellingUnit);
        if (validateRebatePerSellingUnitResult != null) {
            failureReasons.add(validateRebatePerSellingUnitResult);
        }

        return failureReasons;

    }
}
