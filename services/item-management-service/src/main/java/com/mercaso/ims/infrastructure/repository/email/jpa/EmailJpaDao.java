package com.mercaso.ims.infrastructure.repository.email.jpa;

import com.mercaso.ims.domain.email.enums.EmailType;
import com.mercaso.ims.infrastructure.repository.email.jpa.dataobject.EmailDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface EmailJpaDao extends JpaRepository<EmailDo, UUID> {

    /**
     * Find emails by entity type and entity ID
     */
    List<EmailDo> findByEntityTypeAndEntityId(String entityType, UUID entityId);

    /**
     * Find emails by entity type, entity ID and email type
     */
    List<EmailDo> findByEntityTypeAndEntityIdAndEmailType(String entityType, UUID entityId, EmailType emailType);

    /**
     * Find emails by email address
     */
    List<EmailDo> findByEmail(String email);

    /**
     * Find emails by entity type
     */
    List<EmailDo> findByEntityType(String entityType);

    /**
     * Find emails by entity type and entity ID with custom query for better performance
     */
    @Query("SELECT e FROM EmailDo e WHERE e.entityType = :entityType AND e.entityId = :entityId")
    List<EmailDo> findByEntityTypeAndEntityIdCustom(@Param("entityType") String entityType, @Param("entityId") UUID entityId);
}
