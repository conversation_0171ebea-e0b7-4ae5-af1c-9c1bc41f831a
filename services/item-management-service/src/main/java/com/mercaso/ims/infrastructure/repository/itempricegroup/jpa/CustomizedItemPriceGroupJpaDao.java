package com.mercaso.ims.infrastructure.repository.itempricegroup.jpa;

import com.mercaso.ims.application.dto.ItemPriceGroupItemDto;
import com.mercaso.ims.application.dto.ItemPriceGroupListItemDto;
import com.mercaso.ims.application.query.ItemPriceGroupQuery;
import java.util.List;
import java.util.UUID;

public interface CustomizedItemPriceGroupJpaDao {

    List<ItemPriceGroupListItemDto> fetchItemPriceGroupDtoList(ItemPriceGroupQuery query);

    long countQuery(ItemPriceGroupQuery query);

    List<ItemPriceGroupItemDto> getItemPriceGroupItemDto(UUID itemPriceGroupId);

}
