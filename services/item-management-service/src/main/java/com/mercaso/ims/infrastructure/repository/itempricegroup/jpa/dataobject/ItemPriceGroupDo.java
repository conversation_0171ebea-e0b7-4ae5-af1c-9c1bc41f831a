package com.mercaso.ims.infrastructure.repository.itempricegroup.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;


@Entity
@Table(name = "item_price_group")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_price_group set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class ItemPriceGroupDo extends BaseDo {


    @Column(name = "group_name")
    private String groupName;

    @Column(name = "price")
    private BigDecimal price;

}