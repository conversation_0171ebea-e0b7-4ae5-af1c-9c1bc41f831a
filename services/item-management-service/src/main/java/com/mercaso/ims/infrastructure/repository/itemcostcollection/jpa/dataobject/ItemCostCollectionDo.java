package com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa.dataobject;

import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Table(name = "item_cost_collection")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_cost_collection set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class ItemCostCollectionDo extends BaseDo {


    @Column(name = "source")
    @Enumerated(EnumType.STRING)
    private ItemCostCollectionSources source;

    @Column(name = "vendor_id")
    private UUID vendorId;

    @Column(name = "vendor_name")
    private String vendorName;

    @Column(name = "collection_number")
    private String collectionNumber;

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private ItemCostCollectionTypes type;

    @Column(columnDefinition = "file_name")
    private String fileName;

    @Column(columnDefinition = "vendor_collection_number")
    private String vendorCollectionNumber;

}
