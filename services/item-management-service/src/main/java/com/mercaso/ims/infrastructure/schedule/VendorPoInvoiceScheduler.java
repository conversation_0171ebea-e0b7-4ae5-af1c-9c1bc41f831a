package com.mercaso.ims.infrastructure.schedule;


import com.google.common.collect.Lists;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.dto.AttachmentDto;
import com.mercaso.ims.application.dto.GmailMessageDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.excel.data.CostcoItemDailyUpdatedData;
import com.mercaso.ims.infrastructure.excel.generator.CostcoItemDailyUpdatedExcelGenerator;
import com.mercaso.ims.infrastructure.external.google.GmailService;
import com.mercaso.ims.infrastructure.external.google.GoogleSheetAdaptor;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import javax.mail.Flags;
import javax.mail.search.AndTerm;
import javax.mail.search.ComparisonTerm;
import javax.mail.search.FlagTerm;
import javax.mail.search.FromStringTerm;
import javax.mail.search.ReceivedDateTerm;
import javax.mail.search.SearchTerm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class VendorPoInvoiceScheduler {

    private static final Integer VERNON_LIST_LOCK_KEY = "[VendorPoInvoiceScheduler.syncVernorItemCostCollectionByGmail]".hashCode();
    private static final Integer EXOTIC_BLVD_LIST_LOCK_KEY = "[VendorPoInvoiceScheduler.syncExoticBlvdCostCollectionByGmail]".hashCode();
    private static final Integer SEVEN_STAR_LIST_LOCK_KEY = "[VendorPoInvoiceScheduler.syncSevenStarCostCollectionByGmail]".hashCode();
    private static final Integer COSTCO_LOCK_KEY = "[VendorPoInvoiceScheduler.syncCostcoItemCostCollection]".hashCode();
    private final PgAdvisoryLock pgAdvisoryLock;
    private final EntityManagerFactory managerFactory;
    private final VendorService vendorService;
    private final ItemCostCollectionService itemCostCollectionService;
    private final ItemCostCollectionApplicationService itemCostCollectionApplicationService;
    private final DocumentApplicationService documentApplicationService;
    private final GmailService gmailService;
    private final GoogleSheetAdaptor googleSheetAdaptor;
    private final CostcoItemDailyUpdatedExcelGenerator costcoItemDailyUpdatedExcelGenerator;

    @Value("${external.costco.item_list_sheet_id}")
    private String costcoSheetId;

    @Value("${external.vernon.from_email}")
    private String vernonFromEmail;

    @Value("${external.exotic_blvd.from_email}")
    private String exoticBlvdFromEmail;

    @Value("${external.seven_star.from_email}")
    private String sevenStarFromEmail;


    @Scheduled(fixedRate = 2 * 60 * 60 * 1000)
    public void syncSevenStarCostCollectionByGmail() {
        log.info("Starting sync 7 Star CostCollection FromGmail Task");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                SEVEN_STAR_LIST_LOCK_KEY,
                "Sync 7 Star cost Task");
            if (isAcquired == null || !isAcquired) {
                log.warn(
                    "[VendorPoInvoiceScheduler.syncSevenStarCostCollectionByGmail] is already in progress, please try again later.");
                return;
            }
            Vendor vendor = vendorService.findByVendorName(VendorConstant.SEVEN_STAR);
            if (vendor.isWithinShutdownWindow()) {
                log.info("7 Star is within shutdown window, skipping sync");
                return;
            }
            AttachmentDto attachmentDto = getSevenStarLatestAttachment();
            if (attachmentDto != null) {
                String vendorCollectionNumber = FilenameUtils.getBaseName(attachmentDto.getFilename());
                List<ItemCostCollection> itemCostCollections = itemCostCollectionService.findByVendorId(vendor.getId());
                if (itemCostCollections.stream()
                    .noneMatch(itemCostCollection -> itemCostCollection.getVendorCollectionNumber()
                        .equals(vendorCollectionNumber))) {
                    DocumentResponse documentResponse = documentApplicationService.uploadExcel(attachmentDto.getFileData(),
                        attachmentDto.getFilename());
                    createItemCostCollectionByItemListAttachment(documentResponse.getName(),
                        vendorCollectionNumber,
                        vendor.getId(),
                        VendorConstant.SEVEN_STAR,
                        ItemCostCollectionSources.SEVEN_STAR_DAILY_ITEM_LISTS);
                }
            }

        } catch (Exception e) {
            log.error("[syncSevenStarCostCollectionByGmail] Exception occurred: {}", e.getMessage(), e);
        } finally {
            pgAdvisoryLock.unLock(entityManager,
                SEVEN_STAR_LIST_LOCK_KEY,
                "unlock [VendorPoInvoiceScheduler.syncSevenStarCostCollectionByGmail]");
            entityManager.close();
            log.info("Finished sync 7 Star  CostCollection FromGmail");
        }
    }


    @Scheduled(cron = "0 0 7 * * *", zone = "America/Los_Angeles")
    public void syncExoticBlvdCostCollectionByGmail() {
        log.info("Starting sync ExoticBlvd CostCollection FromGmail Task");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                EXOTIC_BLVD_LIST_LOCK_KEY,
                "Sync ExoticBlvd cost Task");
            if (isAcquired == null || !isAcquired) {
                log.warn(
                    "[VendorPoInvoiceScheduler.syncExoticBlvdCostCollectionByGmail] is already in progress, please try again later.");
                return;
            }
            Vendor vendor = vendorService.findByVendorName(VendorConstant.EXOTIC_BLVD);
            if (vendor.isWithinShutdownWindow()) {
                log.info("ExoticBlvd is within shutdown window, skipping sync");
                return;
            }
            AttachmentDto attachmentDto = getExoticBlvdLatestAttachment();
            if (attachmentDto != null) {

                String vendorCollectionNumber = FilenameUtils.getBaseName(attachmentDto.getFilename());
                List<ItemCostCollection> itemCostCollections = itemCostCollectionService.findByVendorId(vendor.getId());
                if (itemCostCollections.stream()
                    .noneMatch(itemCostCollection -> itemCostCollection.getVendorCollectionNumber()
                        .equals(vendorCollectionNumber))) {
                    DocumentResponse documentResponse = documentApplicationService.uploadFileContent(attachmentDto.getFileData(),
                        attachmentDto.getFilename(),
                        false);
                    createItemCostCollectionByItemListAttachment(documentResponse.getName(), vendorCollectionNumber,
                        vendor.getId(),
                        VendorConstant.EXOTIC_BLVD,
                        ItemCostCollectionSources.EXOTIC_BLVD_DAILY_ITEM_LISTS);
                }
            }

        } catch (Exception e) {
            log.error("[syncExoticBlvdCostCollectionByGmail] Exception occurred: {}", e.getMessage(), e);
        } finally {
            pgAdvisoryLock.unLock(entityManager,
                EXOTIC_BLVD_LIST_LOCK_KEY,
                "unlock [VendorPoInvoiceScheduler.syncExoticBlvdCostCollectionByGmail]");
            entityManager.close();
            log.info("Finished sync ExoticBlvd CostCollection FromGmail");
        }
    }


    //Tentatively scheduled to be process every 4 hours due to uncertainty as to when the latest email will be received
    @Scheduled(fixedRate = 4 * 60 * 60 * 1000)
    public void syncVernorItemCostCollectionByGmail() {
        log.info("Starting sync vernor CostCollection FromGmail Task");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                VERNON_LIST_LOCK_KEY,
                "Sync vernor po invoice Task");
            if (isAcquired == null || !isAcquired) {
                log.warn(
                    "[VendorPoInvoiceScheduler.syncVernorItemCostCollectionFromGmail] is already in progress, please try again later.");
                return;
            }
            Vendor vendor = vendorService.findByVendorName(VendorConstant.VERNON_SALES);
            if (vendor.isWithinShutdownWindow()) {
                log.info("VERNON_SALES is within shutdown window, skipping sync");
                return;
            }
            AttachmentDto attachmentDto = getVernonLatestAttachment();
            if (attachmentDto != null) {
                String vendorCollectionNumber = FilenameUtils.getBaseName(attachmentDto.getFilename());
                List<ItemCostCollection> itemCostCollections = itemCostCollectionService.findByVendorId(vendor.getId());
                if (itemCostCollections.stream()
                    .noneMatch(itemCostCollection -> itemCostCollection.getVendorCollectionNumber()
                        .equals(vendorCollectionNumber))) {
                    DocumentResponse documentResponse = documentApplicationService.uploadFileContent(attachmentDto.getFileData(),
                        attachmentDto.getFilename(),
                        false);
                    createItemCostCollectionByItemListAttachment(documentResponse.getName(), vendorCollectionNumber,
                        vendor.getId(),
                        VendorConstant.VERNON_SALES,
                        ItemCostCollectionSources.VERNON_DAILY_ITEM_LISTS);
                }
            }

        } catch (Exception e) {
            log.error("[syncVernorItemCostCollectionByGmail] Exception occurred: {}", e.getMessage(), e);
        } finally {
            pgAdvisoryLock.unLock(entityManager,
                VERNON_LIST_LOCK_KEY,
                "unlock [VendorPoInvoiceScheduler.syncVernorItemCostCollectionFromGmail]");
            entityManager.close();
            log.info("Finished sync vernor CostCollection FromGmail Task");
        }
    }

    private void createItemCostCollectionByItemListAttachment(String filename,
        String vendorCollectionNumber,
        UUID vendorId,
        String vendorName,
        ItemCostCollectionSources source) {

        CreateItemCostCollectionCommand command = CreateItemCostCollectionCommand.builder()
            .vendorId(vendorId)
            .vendorName(vendorName)
            .source(source)
            .type(ItemCostCollectionTypes.CSV_FILE)
            .vendorCollectionNumber(vendorCollectionNumber)
            .fileName(filename)
            .build();
        itemCostCollectionApplicationService.create(command);

    }


    @Scheduled(cron = "0 5 6 * * *", zone = "America/Los_Angeles")
    public void syncCostcoItemCostCollection() {
        log.info("Starting sync Costco ItemCostCollection Task");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                COSTCO_LOCK_KEY,
                "Sync Costco ItemCostCollection Task");
            if (isAcquired == null || !isAcquired) {
                log.warn("[VendorPoInvoiceScheduler.syncCostcoItemCostCollection] is already in progress, please try again later.");
                return;
            }

            String vendorCollectionNumber = getCostcoItemCostCollectionNumber();
            log.info("syncCostcoItemCostCollection vendorCollectionNumber : {}", vendorCollectionNumber);
            Vendor vendor = vendorService.findByVendorName(VendorConstant.COSTCO);
            if (vendor.isWithinShutdownWindow()) {
                log.info("COSTCO is within shutdown window, skipping sync");
                return;
            }
            List<ItemCostCollection> itemCostCollections = itemCostCollectionService.findByVendorId(vendor.getId());
            if (itemCostCollections.stream().noneMatch(itemCostCollection -> itemCostCollection.getVendorCollectionNumber()
                .equals(vendorCollectionNumber))) {
                log.info("Creating item cost collection for Costco collectionNumber: {}", vendorCollectionNumber);
                createCostcoItemCostCollection(vendorCollectionNumber, vendor.getId());
            }

        } catch (Exception e) {
            log.error("[syncCostcoItemCostCollection] Exception occurred: {}", e.getMessage(), e);
        } finally {
            pgAdvisoryLock.unLock(entityManager,
                COSTCO_LOCK_KEY,
                "unlock [VendorPoInvoiceScheduler.syncCostcoItemCostCollection]");
            entityManager.close();
            log.info("Finished sync Costco ItemCostCollection Task");
        }
    }

    private String getCostcoItemCostCollectionNumber() {
        List<List<Object>> readGoogleSheet = googleSheetAdaptor.readGoogleSheet(costcoSheetId,
            "569!S2");
        return readGoogleSheet.getFirst().getFirst().toString();
    }

    private List<CostcoItemDailyUpdatedData> geCostcoItemDailyUpdatedData() {
        List<List<Object>> readGoogleSheet = googleSheetAdaptor.readGoogleSheet(costcoSheetId,
            "569!A2:S");
        return readGoogleSheet.stream().map(raw -> CostcoItemDailyUpdatedData.builder()
            .whseNumber(raw.get(0).toString())
            .dNumber(raw.get(1).toString())
            .description(raw.get(2).toString())
            .cat1AndDescription(raw.get(3).toString())
            .vendorItemNumber(raw.get(4).toString())
            .description1(raw.get(5).toString())
            .description2(raw.get(6).toString())
            .floorPrice(new BigDecimal(raw.get(7).toString()))
            .onHand(Integer.valueOf(raw.get(8).toString()))
            .inTransit(raw.get(9).toString())
            .onOrder(raw.get(10).toString())
            .statusDesc(raw.get(11).toString())
            .bbs(raw.get(12).toString())
            .randomWeight(raw.get(13).toString())
            .upc(raw.get(14).toString())
            .mpkQty(raw.get(15).toString())
            .mpkType(raw.get(16).toString())
            .unitWeight(raw.get(17).toString())
            .asOf(raw.get(18).toString())
            .build()).toList();
    }

    private DocumentResponse saveCostcoItemDailyUpdatedData(List<CostcoItemDailyUpdatedData> costcoItemDailyUpdatedData,
        String collectionNumber) {

        byte[] content = costcoItemDailyUpdatedExcelGenerator.generate(costcoItemDailyUpdatedData, collectionNumber);

        String sb = "CostcoDailyUpdatedData-"
            + collectionNumber
            + "-details"
            + ".xlsx";
        String fileName = URLEncoder.encode(sb, StandardCharsets.UTF_8).replace("\\+", "-");
        log.info("uploadCostcoDailyUpdatedData for docName  :{}", fileName);
        return documentApplicationService.uploadExcel(content, fileName);
    }

    private void createCostcoItemCostCollection(
        String vendorCollectionNumber,
        UUID vendorId) {
        List<CostcoItemDailyUpdatedData> costcoItemDailyUpdatedData = geCostcoItemDailyUpdatedData();
        DocumentResponse documentResponse = saveCostcoItemDailyUpdatedData(costcoItemDailyUpdatedData, vendorCollectionNumber);
        log.info("Creating item cost collection for collectionNumber: {}", vendorCollectionNumber);
        CreateItemCostCollectionCommand command = CreateItemCostCollectionCommand.builder()
            .vendorId(vendorId)
            .vendorName(VendorConstant.COSTCO)
            .source(ItemCostCollectionSources.COSTCO_DAILY_ITEM_LISTS)
            .type(ItemCostCollectionTypes.EXCEL_FILE)
            .vendorCollectionNumber(vendorCollectionNumber)
            .fileName(documentResponse.getName())
            .build();
        itemCostCollectionApplicationService.create(command);

    }

    private AttachmentDto getVernonLatestAttachment() {
        //get passed 1 days emails
        Calendar cal = java.util.Calendar.getInstance();
        cal.add(java.util.Calendar.DATE, -1);
        Date date = cal.getTime();
        SearchTerm dateTerm = new ReceivedDateTerm(ComparisonTerm.GT, date);
        SearchTerm unseenTerm = new FlagTerm(new Flags(Flags.Flag.SEEN), false);
        SearchTerm fromTerm = new FromStringTerm(vernonFromEmail);
        SearchTerm searchTerm = new AndTerm(new AndTerm(unseenTerm, fromTerm), dateTerm);
        List<GmailMessageDto> messages = gmailService.queryEmails(searchTerm);
        if (CollectionUtils.isEmpty(messages)) {
            log.info("No message found for Vernon Email");
            return null;
        }
        if (messages.size() > 1) {
            log.warn("More than one message found for Vernon Email");
        }
        return getLatestMessageAttachment(messages);

    }

    private AttachmentDto getSevenStarLatestAttachment() {
        //get passed 1 days emails
        Calendar cal = java.util.Calendar.getInstance();
        cal.add(java.util.Calendar.DATE, -2);
        Date date = cal.getTime();
        SearchTerm dateTerm = new ReceivedDateTerm(ComparisonTerm.GT, date);
        SearchTerm unseenTerm = new FlagTerm(new Flags(Flags.Flag.SEEN), false);
        SearchTerm fromTerm = new FromStringTerm(sevenStarFromEmail);
        SearchTerm searchTerm = new AndTerm(new AndTerm(unseenTerm, fromTerm), dateTerm);
        List<GmailMessageDto> messages = gmailService.queryEmails(searchTerm);
        if (CollectionUtils.isEmpty(messages)) {
            log.info("No message found for ExoticBlvd Email");
            return null;
        }
        return getLatestMessageAttachment(messages);
    }

    private AttachmentDto getExoticBlvdLatestAttachment() {
        //get passed 1 days emails
        Calendar cal = java.util.Calendar.getInstance();
        cal.add(java.util.Calendar.DATE, -2);
        Date date = cal.getTime();
        SearchTerm dateTerm = new ReceivedDateTerm(ComparisonTerm.GT, date);
        SearchTerm unseenTerm = new FlagTerm(new Flags(Flags.Flag.SEEN), false);
        SearchTerm fromTerm = new FromStringTerm(exoticBlvdFromEmail);
        SearchTerm searchTerm = new AndTerm(new AndTerm(unseenTerm, fromTerm), dateTerm);
        List<GmailMessageDto> messages = gmailService.queryEmails(searchTerm);
        if (CollectionUtils.isEmpty(messages)) {
            log.info("No message found for ExoticBlvd Email");
            return null;
        }
        return getLatestMessageAttachment(messages);

    }


    private AttachmentDto getLatestMessageAttachment(List<GmailMessageDto> messages) {
        if (CollectionUtils.isEmpty(messages)) {
            log.info("No message found for Email");
            return null;
        }

        AttachmentDto attachmentDto = Lists.reverse(messages).stream()
            .filter(message -> !CollectionUtils.isEmpty(message.getAttachments()))
            .map(message -> message.getAttachments().getFirst())
            .findFirst()
            .orElse(null);
        if (attachmentDto != null) {
            log.info("Attachment :{} found ", attachmentDto.getFilename());
        }
        return attachmentDto;
    }


}