package com.mercaso.ims.infrastructure.repository.itemattribute.jpa.mapper;

import com.mercaso.ims.domain.itemattribute.ItemAttribute;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.itemattribute.jpa.dataobject.ItemAttributeDo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ItemAttributeDoMapper extends BaseDoMapper<ItemAttributeDo, ItemAttribute> {

    ItemAttributeDoMapper INSTANCE = Mappers.getMapper(ItemAttributeDoMapper.class);

    List<ItemAttributeDo> domainListToDoList(List<ItemAttribute> domainList);

    List<ItemAttribute> doListToDomainList(List<ItemAttributeDo> doList);

}
