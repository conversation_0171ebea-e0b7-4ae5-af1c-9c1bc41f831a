package com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord.jpa.mapper;

import com.mercaso.ims.domain.vendorpoanalyzerecord.VendorPoAnalyzeRecord;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord.jpa.dataobject.VendorPoAnalyzeRecordDo;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface VendorPoAnalyzeRecordDoMapper extends BaseDoMapper<VendorPoAnalyzeRecordDo, VendorPoAnalyzeRecord> {

}