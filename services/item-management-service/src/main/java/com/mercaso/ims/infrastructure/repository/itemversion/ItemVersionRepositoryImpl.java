package com.mercaso.ims.infrastructure.repository.itemversion;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_VERSION_NOT_FOUND;

import com.mercaso.ims.domain.itemversion.ItemVersion;
import com.mercaso.ims.domain.itemversion.ItemVersionRepository;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.item.jpa.dataobject.ItemDo;
import com.mercaso.ims.infrastructure.repository.itemversion.jpa.ItemVersionJpaDao;
import com.mercaso.ims.infrastructure.repository.itemversion.jpa.dataobject.ItemVersionDo;
import com.mercaso.ims.infrastructure.repository.itemversion.jpa.mapper.ItemVersionDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class ItemVersionRepositoryImpl implements ItemVersionRepository {

    private final ItemVersionJpaDao itemVersionJpaDao;

    private final ItemVersionDoMapper itemVersionDoMapper;


    @Override
    public ItemVersion findBySkuAndVersion(String skuNumber, Integer version) {
        ItemVersionDo itemVersionDo = itemVersionJpaDao.findBySkuNumberAndVersionNumber(skuNumber, version);
        if (null == itemVersionDo) {
            return null;
        }
        return itemVersionDoMapper.doToDomain(itemVersionDo);
    }

    @Override
    public ItemVersion findByItemIdAndVersion(UUID itemId, Integer version) {
        ItemVersionDo itemVersionDo = itemVersionJpaDao.findByItemIdAndVersionNumber(itemId, version);
        if (null == itemVersionDo) {
            log.warn("Not found the itemVersionDo by itemId :{} and version :{}", itemId, version);
            return null;
        }
        return itemVersionDoMapper.doToDomain(itemVersionDo);
    }

    @Override
    public List<ItemVersion> findByItemId(UUID itemId) {
        List<ItemVersionDo> itemVersionDos = itemVersionJpaDao.findByItemId(itemId);
        if (null == itemVersionDos) {
            return List.of();
        }
        return itemVersionDos.stream().map(itemVersionDoMapper::doToDomain).toList();
    }

    @Override
    public ItemVersion save(ItemVersion domain) {
        ItemVersionDo itemVersionDo = itemVersionJpaDao.save(itemVersionDoMapper.domainToDo(domain));
        return itemVersionDoMapper.doToDomain(itemVersionDo);
    }

    @Override
    public ItemVersion findById(UUID id) {
        ItemVersionDo itemVersionDo = itemVersionJpaDao.findById(id).orElse(null);
        log.info("[ItemVersionRepository] find itemVersionDo by id :{}", itemVersionDo);
        if (null == itemVersionDo) {
            return null;
        }
        return itemVersionDoMapper.doToDomain(itemVersionDo);
    }

    @Override
    public ItemVersion update(ItemVersion domain) {
        ItemVersionDo itemVersionDo = itemVersionJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(itemVersionDo)) {
            throw new ImsBusinessException(ITEM_VERSION_NOT_FOUND.getCode());
        }
        ItemVersionDo itemVersionDoTarget = itemVersionDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList(
            "createdBy",
            "createdAt");
        BeanUtils.copyProperties(itemVersionDoTarget, itemVersionDoTarget, ignoreProperties.toArray(new String[0]));
        ItemVersionDo result = itemVersionJpaDao.save(itemVersionDoTarget);
        return itemVersionDoMapper.doToDomain(result);
    }

    @Override
    public ItemVersion deleteById(UUID id) {
        ItemVersionDo itemVersionDo = itemVersionJpaDao.findById(id).orElse(null);
        if (null == itemVersionDo) {
            return null;
        }
        itemVersionDo.setDeletedAt(Instant.now());

        itemVersionDo.setDeletedBy(SecurityUtil.getLoginUserId());
        itemVersionDo.setDeletedUserName(SecurityUtil.getUserName());
        return itemVersionDoMapper.doToDomain(itemVersionJpaDao.save(itemVersionDo));
    }
}
