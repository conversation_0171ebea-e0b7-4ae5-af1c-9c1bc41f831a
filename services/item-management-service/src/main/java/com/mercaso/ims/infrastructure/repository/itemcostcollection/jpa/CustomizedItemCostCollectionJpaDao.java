package com.mercaso.ims.infrastructure.repository.itemcostcollection.jpa;

import com.mercaso.ims.application.dto.ItemCostCollectionDetailDto;
import com.mercaso.ims.application.query.ItemCostCollectionQuery;
import java.util.List;
import java.util.UUID;

public interface CustomizedItemCostCollectionJpaDao {

    List<ItemCostCollectionDetailDto> fetchItemCostCollectionDtoList(ItemCostCollectionQuery itemCostCollectionQuery);

    ItemCostCollectionDetailDto getItemCostCollectionDto(UUID id);

    long countQuery(ItemCostCollectionQuery itemCostCollectionQuery);

}
