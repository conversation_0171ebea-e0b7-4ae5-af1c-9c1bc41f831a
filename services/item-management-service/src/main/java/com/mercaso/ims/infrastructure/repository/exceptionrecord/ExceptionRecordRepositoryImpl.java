package com.mercaso.ims.infrastructure.repository.exceptionrecord;

import com.mercaso.ims.domain.exceptionrecord.ExceptionRecord;
import com.mercaso.ims.domain.exceptionrecord.ExceptionRecordRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.exceptionrecord.jpa.ExceptionRecordDao;
import com.mercaso.ims.infrastructure.repository.exceptionrecord.jpa.dataobject.ExceptionRecordDo;
import com.mercaso.ims.infrastructure.repository.exceptionrecord.jpa.mapper.ExceptionRecordDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ExceptionRecordRepositoryImpl implements ExceptionRecordRepository {


    private final ExceptionRecordDoMapper exceptionRecordDoMapper;

    private final ExceptionRecordDao exceptionRecordDao;


    @Override
    public ExceptionRecord save(ExceptionRecord domain) {
        ExceptionRecordDo exceptionRecordDo = exceptionRecordDoMapper.domainToDo(domain);
        exceptionRecordDo = exceptionRecordDao.save(exceptionRecordDo);
        return exceptionRecordDoMapper.doToDomain(exceptionRecordDo);
    }

    @Override
    public ExceptionRecord findById(UUID id) {
        ExceptionRecordDo exceptionRecordDo = exceptionRecordDao.findById(id).orElse(null);
        return exceptionRecordDoMapper.doToDomain(exceptionRecordDo);
    }

    @Override
    public ExceptionRecord update(ExceptionRecord domain) {
        ExceptionRecordDo exceptionRecordDo = exceptionRecordDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(exceptionRecordDo)) {
            throw new ImsBusinessException(ErrorCodeEnums.EXCEPTION_RECORD_NOT_FOUND);
        }
        exceptionRecordDo = exceptionRecordDoMapper.domainToDo(domain);
        exceptionRecordDo = exceptionRecordDao.save(exceptionRecordDo);
        return exceptionRecordDoMapper.doToDomain(exceptionRecordDo);
    }

    @Override
    public ExceptionRecord deleteById(UUID id) {
        ExceptionRecordDo exceptionRecordDo = exceptionRecordDao.findById(id).orElse(null);
        if (null == exceptionRecordDo) {
            return null;
        }
        exceptionRecordDo.setDeletedAt(Instant.now());
        exceptionRecordDo.setDeletedBy(SecurityUtil.getLoginUserId());
        return exceptionRecordDoMapper.doToDomain(exceptionRecordDao.save(exceptionRecordDo));
    }
}
