package com.mercaso.ims.infrastructure.repository.difyworkflowrecord;

import com.mercaso.ims.domain.difyworkflowsrecord.DifyWorkflowRecord;
import com.mercaso.ims.domain.difyworkflowsrecord.DifyWorkflowRecordRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.difyworkflowrecord.jpa.DifyWorkflowRecordJpaDao;
import com.mercaso.ims.infrastructure.repository.difyworkflowrecord.jpa.dataobject.DifyWorkflowRecordDo;
import com.mercaso.ims.infrastructure.repository.difyworkflowrecord.jpa.mapper.DifyWorkflowRecordDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class DifyWorkflowRecordRepositoryImpl implements DifyWorkflowRecordRepository {

    private final DifyWorkflowRecordDoMapper difyWorkflowRecordDoMapper;

    private final DifyWorkflowRecordJpaDao difyWorkflowRecordJpaDao;


    @Override
    public DifyWorkflowRecord save(DifyWorkflowRecord difyWorkflowRecord) {
        DifyWorkflowRecordDo difyWorkflowRecordDo = difyWorkflowRecordDoMapper.domainToDo(difyWorkflowRecord);
        difyWorkflowRecordDo = difyWorkflowRecordJpaDao.save(difyWorkflowRecordDo);
        return difyWorkflowRecordDoMapper.doToDomain(difyWorkflowRecordDo);
    }

    @Override
    public DifyWorkflowRecord findById(UUID id) {
        DifyWorkflowRecordDo difyWorkflowRecordDo = difyWorkflowRecordJpaDao.findById(id).orElse(null);
        return difyWorkflowRecordDoMapper.doToDomain(difyWorkflowRecordDo);
    }

    @Override
    public DifyWorkflowRecord update(DifyWorkflowRecord difyWorkflowRecord) {
        DifyWorkflowRecordDo difyWorkflowRecordDo = difyWorkflowRecordJpaDao.findById(difyWorkflowRecord.getId()).orElse(null);
        if (Objects.isNull(difyWorkflowRecordDo)) {
            throw new ImsBusinessException(ErrorCodeEnums.COMPANY_NOT_FOUND.getCode());
        }
        DifyWorkflowRecordDo difyWorkflowRecordDoTarget = difyWorkflowRecordDoMapper.domainToDo(difyWorkflowRecord);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(difyWorkflowRecordDoTarget, difyWorkflowRecordDo, ignoreProperties.toArray(new String[0]));
        DifyWorkflowRecordDo result = difyWorkflowRecordJpaDao.save(difyWorkflowRecordDo);
        return difyWorkflowRecordDoMapper.doToDomain(result);
    }

    @Override
    public DifyWorkflowRecord deleteById(UUID id) {
        DifyWorkflowRecordDo difyWorkflowRecordDo = difyWorkflowRecordJpaDao.findById(id).orElse(null);
        if (null == difyWorkflowRecordDo) {
            return null;
        }
        difyWorkflowRecordDo.setDeletedAt(Instant.now());
        difyWorkflowRecordDo.setDeletedBy(SecurityUtil.getLoginUserId());
        return difyWorkflowRecordDoMapper.doToDomain(difyWorkflowRecordJpaDao.save(difyWorkflowRecordDo));
    }
}
