package com.mercaso.ims.infrastructure.external.aws_ocr.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BlockDTO {
    private String blockType;
    private String text;
    private Integer page;
    private GeometryDTO geometry;
    private Float confidence;
    private List<RelationshipDTO> relationships;
}
