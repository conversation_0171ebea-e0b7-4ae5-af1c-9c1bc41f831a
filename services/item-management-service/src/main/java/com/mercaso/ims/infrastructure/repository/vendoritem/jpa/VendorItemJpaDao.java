package com.mercaso.ims.infrastructure.repository.vendoritem.jpa;


import com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject.VendorItemDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface VendorItemJpaDao extends JpaRepository<VendorItemDo, UUID> {

    List<VendorItemDo> findByItemId(UUID itemID);

    List<VendorItemDo> findByVendorId(UUID vendorID);

    VendorItemDo findByVendorIdAndItemIdAndVendorSkuNumber(UUID vendorID, UUID itemID, String vendorSkuNum);

    VendorItemDo findByVendorIdAndItemId(UUID vendorID, UUID itemID);

    List<VendorItemDo> findByItemIdIn(List<UUID> itemID);

    List<VendorItemDo> findByVendorIdAndVendorSkuNumber(UUID vendorID, String vendorSkuNum);

    List<VendorItemDo> findByVendorIdAndItemIdIn(UUID vendorID, List<UUID> itemID);
}
