package com.mercaso.ims.infrastructure.repository.itempromoprice;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_PROMO_PRICE_NOT_FOUND;

import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.domain.itempromoprice.ItemPromoPriceRepository;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itempromoprice.jpa.ItemPromoPriceJpaDao;
import com.mercaso.ims.infrastructure.repository.itempromoprice.jpa.dataobject.ItemPromoPriceDo;
import com.mercaso.ims.infrastructure.repository.itempromoprice.jpa.mapper.ItemPromoPriceDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ItemPromoPriceRepositoryImpl implements ItemPromoPriceRepository {

    private final ItemPromoPriceJpaDao itemPromoPriceJpaDao;

    private final ItemPromoPriceDoMapper itemPromoPriceDoMapper;

    @Override
    public ItemPromoPrice save(ItemPromoPrice domain) {
        ItemPromoPriceDo itemPromoPriceDo = itemPromoPriceDoMapper.domainToDo(domain);
        itemPromoPriceDo = itemPromoPriceJpaDao.save(itemPromoPriceDo);
        return itemPromoPriceDoMapper.doToDomain(itemPromoPriceDo);
    }

    @Override
    public ItemPromoPrice findById(UUID id) {
        ItemPromoPriceDo itemPromoPriceDo = itemPromoPriceJpaDao.findById(id).orElse(null);
        return itemPromoPriceDoMapper.doToDomain(itemPromoPriceDo);
    }

    @Override
    public ItemPromoPrice update(ItemPromoPrice domain) {
        ItemPromoPriceDo itemPromoPriceDo = itemPromoPriceJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(itemPromoPriceDo)) {
            throw new ImsBusinessException(ITEM_PROMO_PRICE_NOT_FOUND.getCode());
        }
        ItemPromoPriceDo itemPromoPriceDoTarget = itemPromoPriceDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(itemPromoPriceDoTarget, itemPromoPriceDo, ignoreProperties.toArray(new String[0]));
        ItemPromoPriceDo result = itemPromoPriceJpaDao.save(itemPromoPriceDo);
        return itemPromoPriceDoMapper.doToDomain(result);
    }

    @Override
    public ItemPromoPrice deleteById(UUID id) {
        ItemPromoPriceDo itemPromoPriceDo = itemPromoPriceJpaDao.findById(id).orElse(null);
        if (null == itemPromoPriceDo) {
            return null;
        }
        itemPromoPriceDo.setDeletedAt(Instant.now());
        itemPromoPriceDo.setDeletedBy(SecurityUtil.getLoginUserId());
        return itemPromoPriceDoMapper.doToDomain(itemPromoPriceJpaDao.save(itemPromoPriceDo));
    }

    @Override
    public List<ItemPromoPrice> findByItemId(UUID itemId) {
        return Optional.ofNullable(itemPromoPriceJpaDao.findByItemId(itemId))
            .orElse(Collections.emptyList())
            .stream()
            .map(itemPromoPriceDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<ItemPromoPrice> findByItemIds(List<UUID> itemIds) {
        return Optional.ofNullable(itemPromoPriceJpaDao.findByItemIdIn(itemIds))
            .orElse(Collections.emptyList())
            .stream()
            .map(itemPromoPriceDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<ItemPromoPrice> findByPromoBeginTimeBetween(Instant start, Instant end) {
        return Optional.ofNullable(itemPromoPriceJpaDao.findByPromoBeginTimeBetween(start, end))
            .orElse(Collections.emptyList())
            .stream()
            .map(itemPromoPriceDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<ItemPromoPrice> findByPromoEndTimeBetween(Instant start, Instant end) {
        return Optional.ofNullable(itemPromoPriceJpaDao.findByPromoEndTimeBetween(start, end))
            .orElse(Collections.emptyList())
            .stream()
            .map(itemPromoPriceDoMapper::doToDomain)
            .toList().reversed();
    }

}
