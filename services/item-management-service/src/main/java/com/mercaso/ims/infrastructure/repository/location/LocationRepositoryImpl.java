package com.mercaso.ims.infrastructure.repository.location;

import com.mercaso.ims.domain.location.Location;
import com.mercaso.ims.domain.location.LocationRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.location.jpa.LocationJpaDao;
import com.mercaso.ims.infrastructure.repository.location.jpa.dataobject.LocationDo;
import com.mercaso.ims.infrastructure.repository.location.jpa.mapper.LocationDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LocationRepositoryImpl implements LocationRepository {

    private final LocationJpaDao locationJpaDao;

    private final LocationDoMapper locationDoMapper;

    @Override
    public Location save(Location domain) {
        LocationDo locationDo = locationDoMapper.domainToDo(domain);
        locationDo = locationJpaDao.save(locationDo);
        return locationDoMapper.doToDomain(locationDo);
    }

    @Override
    public Location findById(UUID id) {
        LocationDo locationDo = locationJpaDao.findById(id).orElse(null);
        return locationDoMapper.doToDomain(locationDo);
    }

    @Override
    public Location update(Location domain) {
        LocationDo locationDo = locationJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(locationDo)) {
            throw new ImsBusinessException(ErrorCodeEnums.LOCATION_NOT_FOUND.getCode());
        }
        LocationDo locationDoTarget = locationDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(locationDoTarget, locationDo, ignoreProperties.toArray(new String[0]));
        LocationDo result = locationJpaDao.save(locationDo);
        return locationDoMapper.doToDomain(result);
    }

    @Override
    public Location deleteById(UUID id) {
        LocationDo locationDo = locationJpaDao.findById(id).orElse(null);
        if (null == locationDo) {
            return null;
        }
        locationDo.setDeletedAt(Instant.now());
        locationDo.setDeletedBy(SecurityUtil.getLoginUserId());
        return locationDoMapper.doToDomain(locationJpaDao.save(locationDo));
    }

    @Override
    public List<Location> findByCompanyUUID(UUID companyId) {
        return Optional.ofNullable(locationJpaDao.findByCompanyUUID(companyId))
            .orElse(Collections.emptyList())
            .stream()
            .map(locationDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public Location findByLocationId(Long locationId) {
        LocationDo locationDo = locationJpaDao.findByLocationId(locationId);
        if (null == locationDo) {
            return null;
        }
        return locationDoMapper.doToDomain(locationDo);
    }
}
