package com.mercaso.ims.infrastructure.repository.category;

import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.category.CategoryRepository;
import com.mercaso.ims.domain.category.enums.CategoryStatus;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.category.jpa.CategoryJpaDao;
import com.mercaso.ims.infrastructure.repository.category.jpa.dataobject.CategoryDo;
import com.mercaso.ims.infrastructure.repository.category.jpa.mapper.CategoryDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class CategoryRepositoryImpl implements CategoryRepository {

    private final CategoryJpaDao categoryJpaDao;
    private final CategoryDoMapper categoryDoMapper;


    @Override
    public Category save(Category domain) {
        CategoryDo categoryDo = categoryDoMapper.domainToDo(domain);
        categoryDo = categoryJpaDao.save(categoryDo);
        return categoryDoMapper.doToDomain(categoryDo);
    }

    @Override
    public Category findById(UUID id) {
        return categoryDoMapper.doToDomain(categoryJpaDao.findById(id).orElse(null));
    }

    @Override
    public Category update(Category domain) {
        CategoryDo categoryDo = categoryJpaDao.findById(domain.getId()).orElse(null);
        if (null == categoryDo) {
            throw new ImsBusinessException(ErrorCodeEnums.CATEGORY_NOT_FOUND.getCode());
        }

        CategoryDo categoryDoTarget = categoryDoMapper.domainToDo(domain);
        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
            "createdAt"));
        BeanUtils.copyProperties(categoryDoTarget, categoryDo, ignoreProperties.toArray(new String[0]));
        categoryDo = categoryJpaDao.save(categoryDoTarget);
        return categoryDoMapper.doToDomain(categoryDo);
    }

    @Override
    public Category deleteById(UUID id) {
        CategoryDo categoryDo = categoryJpaDao.findById(id).orElse(null);
        if (null == categoryDo) {
            return null;
        }
        categoryDo.setDeletedAt(Instant.now());
        categoryDo.setDeletedBy(SecurityUtil.getLoginUserId());
        categoryDo = categoryJpaDao.save(categoryDo);
        return categoryDoMapper.doToDomain(categoryDo);
    }

    @Override
    public List<Category> findByName(String name) {
        return Optional.ofNullable(categoryJpaDao.findByName(name))
            .orElse(Collections.emptyList())
            .stream()
            .map(categoryDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<Category> findAllByNameIn(List<String> names) {
        return Optional.ofNullable(categoryJpaDao.findAllByNameIn(names))
            .orElse(Collections.emptyList())
            .stream()
            .map(categoryDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<Category> findAllByIdIn(List<UUID> ids) {
        return Optional.ofNullable(categoryJpaDao.findAllByIdIn(ids))
            .orElse(Collections.emptyList())
            .stream()
            .map(categoryDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<Category> findByNameAndStatus(String name, CategoryStatus categoryStatus) {
        return Optional.ofNullable(categoryJpaDao.findAllByNameAndStatus(name, categoryStatus))
            .orElse(Collections.emptyList())
            .stream()
            .map(categoryDoMapper::doToDomain)
            .toList().reversed();
    }
}
