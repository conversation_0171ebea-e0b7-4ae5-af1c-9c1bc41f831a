package com.mercaso.ims.infrastructure.repository.attributegroup.jpa.mapper;

import com.mercaso.ims.domain.attributegroup.AttributeGroupDetail;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.attribute.jpa.mapper.AttributeDoMapper;
import com.mercaso.ims.infrastructure.repository.attributegroup.jpa.dataobject.AttributeGroupDetailDo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.util.ObjectUtils;

@Mapper(componentModel = "spring", imports = ObjectUtils.class, uses = AttributeDoMapper.class)
public interface AttributeGroupDetailDoMapper extends BaseDoMapper<AttributeGroupDetailDo, AttributeGroupDetail> {

    @Mapping(target = "attribute", source = "attributeDo")
    AttributeGroupDetail doToDomain(AttributeGroupDetailDo attributeGroupDetailDo);

    @Mapping(target = "attributeDo", source = "attribute")
    AttributeGroupDetailDo domainToDo(AttributeGroupDetail attributeGroupDetail);


}