package com.mercaso.ims.infrastructure.repository.itempricegroup;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_PRICE_GROUP_NOT_FOUND;

import com.mercaso.ims.domain.itempricegroup.ItemPriceGroup;
import com.mercaso.ims.domain.itempricegroup.ItemPriceGroupRepository;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itempricegroup.jpa.ItemPriceGroupJpaDao;
import com.mercaso.ims.infrastructure.repository.itempricegroup.jpa.dataobject.ItemPriceGroupDo;
import com.mercaso.ims.infrastructure.repository.itempricegroup.jpa.mapper.ItemPriceGroupDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class ItemPriceGroupRepositoryImpl implements ItemPriceGroupRepository {

    private final ItemPriceGroupJpaDao itemPriceGroupJpaDao;

    private final ItemPriceGroupDoMapper itemPriceGroupDoMapper;


    @Override
    public ItemPriceGroup save(ItemPriceGroup domain) {
        ItemPriceGroupDo itemPriceGroupDo = itemPriceGroupJpaDao.save(itemPriceGroupDoMapper.domainToDo(domain));
        return itemPriceGroupDoMapper.doToDomain(itemPriceGroupDo);
    }

    @Override
    public ItemPriceGroup findById(UUID id) {
        ItemPriceGroupDo itemPriceGroupDo = itemPriceGroupJpaDao.findById(id).orElse(null);
        if (null == itemPriceGroupDo) {
            return null;
        }
        return itemPriceGroupDoMapper.doToDomain(itemPriceGroupDo);
    }

    @Override
    public ItemPriceGroup update(ItemPriceGroup domain) {
        ItemPriceGroupDo itemPriceGroupDo = itemPriceGroupJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(itemPriceGroupDo)) {
            throw new ImsBusinessException(ITEM_PRICE_GROUP_NOT_FOUND.getCode());
        }
        ItemPriceGroupDo doTarget = itemPriceGroupDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList("createdBy",
            "createdAt");
        BeanUtils.copyProperties(doTarget, itemPriceGroupDo, ignoreProperties.toArray(new String[0]));
        ItemPriceGroupDo result = itemPriceGroupJpaDao.save(itemPriceGroupDo);
        return itemPriceGroupDoMapper.doToDomain(result);
    }

    @Override
    public ItemPriceGroup deleteById(UUID id) {
        ItemPriceGroupDo itemPriceGroupDo = itemPriceGroupJpaDao.findById(id).orElse(null);
        if (null == itemPriceGroupDo) {
            return null;
        }
        String uniqueSuffix = "-deleted-" + System.currentTimeMillis();
        itemPriceGroupDo.setDeletedAt(Instant.now());
        itemPriceGroupDo.setGroupName(itemPriceGroupDo.getGroupName() + uniqueSuffix);
        itemPriceGroupDo.setDeletedBy(SecurityUtil.getLoginUserId());
        itemPriceGroupDo.setDeletedUserName(SecurityUtil.getUserName());
        return itemPriceGroupDoMapper.doToDomain(itemPriceGroupJpaDao.save(itemPriceGroupDo));
    }

    @Override
    public ItemPriceGroup findByGroupName(String groupName) {
        ItemPriceGroupDo itemPriceGroupDo = itemPriceGroupJpaDao.findByGroupName(groupName);
        if (null == itemPriceGroupDo) {
            return null;
        }
        return itemPriceGroupDoMapper.doToDomain(itemPriceGroupDo);
    }

    @Override
    public List<ItemPriceGroup> findAll() {
        return Optional.of(itemPriceGroupJpaDao.findAll())
            .orElse(Collections.emptyList())
            .stream()
            .map(itemPriceGroupDoMapper::doToDomain)
            .toList();
    }

    @Override
    public List<ItemPriceGroup> findByFuzzyName(String groupName) {
        return Optional.of(itemPriceGroupJpaDao.findByGroupNameIsLikeIgnoreCase(groupName))
            .orElse(Collections.emptyList())
            .stream()
            .map(itemPriceGroupDoMapper::doToDomain)
            .toList();
    }
}
