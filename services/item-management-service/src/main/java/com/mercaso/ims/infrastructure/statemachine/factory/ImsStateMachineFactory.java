package com.mercaso.ims.infrastructure.statemachine.factory;


import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.statemachine.StateTransitionType;
import com.mercaso.ims.infrastructure.statemachine.StateType;
import com.mercaso.ims.infrastructure.statemachine.StatefulContext;
import lombok.RequiredArgsConstructor;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.statemachine.support.DefaultStateMachineContext;

import java.io.Serializable;
import java.util.UUID;

@RequiredArgsConstructor
public class ImsStateMachineFactory<S extends StateType, E extends StateTransitionType, T extends StatefulContext<S>> implements Serializable {

    final transient StateMachineFactory<S, E> stateMachineFactory;

    final transient StateMachinePersister<S, E, T> persister;

    public void updateInstanceState(StateMachine<S, E> stateMachine, T entity) {
        try {
            persister.persist(stateMachine, entity);
        } catch (Exception e) {
            throw new ImsBusinessException("update state error: %s", e.getMessage());
        }
    }

    /**
     * Build a new Statemachine instance with an given UUID in concurrent scenario.
     */
    public StateMachine<S, E> create(StatefulContext<S> entity) {
        StateMachine<S, E> stateMachine = stateMachineFactory.getStateMachine(UUID.randomUUID());
        S status = entity.getStatus();

        StateMachineContext<S, E> context = new DefaultStateMachineContext<>(status, null, null, null);
        stateMachine.stop();
        stateMachine.getStateMachineAccessor().doWithAllRegions(function -> function.resetStateMachine(context));
        stateMachine.start();
        return stateMachine;
    }

}
