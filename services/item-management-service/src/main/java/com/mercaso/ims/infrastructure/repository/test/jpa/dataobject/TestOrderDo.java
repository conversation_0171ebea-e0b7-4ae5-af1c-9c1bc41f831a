package com.mercaso.ims.infrastructure.repository.test.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;


@Entity
@Table(name = "test_order")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update test_order set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class TestOrderDo extends BaseDo {

    @Column(name = "name")
    private String name;

    @Column(name = "status")
    private Integer status;


}
