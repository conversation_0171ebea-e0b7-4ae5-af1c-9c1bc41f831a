package com.mercaso.ims.infrastructure.apitaskprocess.finale;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskType;
import com.mercaso.ims.infrastructure.apitaskprocess.AbstractApiTaskProcessor;
import com.mercaso.ims.infrastructure.apitaskprocess.payload.TaskRequestPayload;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Processor for FINALE_CREATE_ITEM tasks
 * Handles creating items in Finale API
 */
@Slf4j
@Component
public class FinaleCreateItemProcessor extends AbstractApiTaskProcessor<Void> {

    private final FinaleExternalApiClient finaleExternalApiClient;

    public FinaleCreateItemProcessor(ObjectMapper objectMapper,
                                     FinaleExternalApiClient finaleExternalApiClient) {
        super(objectMapper);
        this.finaleExternalApiClient = finaleExternalApiClient;
    }

    @Override
    public String getTaskType() {
        return TaskType.FINALE_CREATE_ITEM.getType();
    }

    @Override
    public boolean canProcess(String taskType) {
        try {
            TaskType type = TaskType.fromTaskType(taskType);
            return TaskType.FINALE_CREATE_ITEM.equals(type);
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    @Override
    public boolean needsResponse() {
        return false; // Create operations typically don't need response
    }

    @Override
    public Void executeTask(ApiTaskQueue task) throws JsonProcessingException {
        // Parse the request payload to get the SKU parameter
        TaskRequestPayload requestPayload = parseRequestPayload(task, TaskRequestPayload.class);

        // Extract SKU parameter
        String skuNumber = extractParameter(requestPayload, "skuNumber", "sku");
        String statusId = extractParameter(requestPayload, "statusId");

        log.info("Creating item with SKU: {}", skuNumber);

        try {
            // Call the finale API to create item
            // Context management prevents infinite loop
            finaleExternalApiClient.createFinaleItem(skuNumber, statusId);

            log.info("Successfully created item with SKU: {}", skuNumber);
            return null;

        } catch (Exception e) {
            log.error("Failed to create item with SKU {}: {}", skuNumber, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void validateTask(ApiTaskQueue task) {
        super.validateTask(task);

        try {
            TaskRequestPayload requestPayload = parseRequestPayload(task, TaskRequestPayload.class);

            // Validate required parameters
            String skuNumber = extractParameter(requestPayload, "skuNumber", "sku");
            if (skuNumber == null || skuNumber.trim().isEmpty()) {
                throw new IllegalArgumentException("SKU number is required for create item task");
            }

        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid request payload for create item task: " + e.getMessage());
        }
    }
}
