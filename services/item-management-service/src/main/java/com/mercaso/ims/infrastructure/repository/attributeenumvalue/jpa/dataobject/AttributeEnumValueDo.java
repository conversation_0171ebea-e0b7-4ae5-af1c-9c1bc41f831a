package com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "attribute_enum_value")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "update attribute_enum_value set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class AttributeEnumValueDo extends BaseDo {

    @Column(name = "attribute_id")
    private UUID attributeId;

    @Column(name = "value")
    private String value;

    @Column(name = "sort_order")
    private Float sortOrder;

}
