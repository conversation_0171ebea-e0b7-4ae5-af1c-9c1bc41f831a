package com.mercaso.ims.infrastructure.repository.vendoritem.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.util.UUID;

@Entity
@Table(name = "vendor_item_availability_snapshot_detail")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update vendor_item_availability_snapshot_detail set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class VendorItemAvailabilitySnapshotDetailDo extends BaseDo {

    @Column(name = "snapshot_id")
    private UUID snapshotId;

    @Column(name = "vendor_item_id")
    private UUID vendorItemId;

    @Column(name = "vendor_sku_number")
    private String vendorSkuNumber;

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "previous_availability")
    private Boolean previousAvailability;

    @Column(name = "new_availability")
    private Boolean newAvailability;
} 