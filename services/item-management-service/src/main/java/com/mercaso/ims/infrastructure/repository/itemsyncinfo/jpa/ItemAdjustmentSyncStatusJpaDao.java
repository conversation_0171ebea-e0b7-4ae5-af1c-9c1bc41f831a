package com.mercaso.ims.infrastructure.repository.itemsyncinfo.jpa;

import com.mercaso.ims.domain.itemsyncinfo.enums.SyncShopifyStatus;
import com.mercaso.ims.infrastructure.repository.itemsyncinfo.jpa.dataobject.ItemAdjustmentSyncStatusDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ItemAdjustmentSyncStatusJpaDao extends JpaRepository<ItemAdjustmentSyncStatusDo, UUID> {

    ItemAdjustmentSyncStatusDo findByBusinessEventId(UUID businessEventId);

    List<ItemAdjustmentSyncStatusDo> findTop500BySyncShopifyStatusOrderByCreatedAt (SyncShopifyStatus syncShopifyStatus);


}
