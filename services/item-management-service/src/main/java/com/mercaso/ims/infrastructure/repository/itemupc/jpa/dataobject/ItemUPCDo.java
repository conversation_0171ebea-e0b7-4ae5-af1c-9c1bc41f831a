package com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject;

import com.mercaso.ims.domain.item.enums.ItemUpcType;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;


@Entity
@Table(name = "item_upc")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_upc set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemUPCDo extends BaseDo {

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "upc_number")
    private String upcNumber;

    @Column(name = "upc_type")
    @Enumerated(EnumType.STRING)
    private ItemUpcType itemUpcType;
}
