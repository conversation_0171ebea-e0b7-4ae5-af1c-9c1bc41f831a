package com.mercaso.ims.infrastructure.repository.attribute.jpa.dataobject;

import com.mercaso.ims.domain.attribute.enums.AttributeFormat;
import com.mercaso.ims.domain.attribute.enums.AttributeStatus;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import com.mercaso.ims.infrastructure.repository.attributeenumvalue.jpa.dataobject.AttributeEnumValueDo;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "attribute")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "update attribute set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class AttributeDo extends BaseDo {

    @Column(name = "name")
    private String name;

    @Column(name = "category_id")
    private UUID categoryId;

    @Column(name = "description")
    private String description;

    @Column(name = "attribute_format")
    @Enumerated(EnumType.STRING)
    private AttributeFormat attributeFormat;


    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private AttributeStatus status;

    @OneToMany(mappedBy = "attributeId", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @OrderBy("sortOrder ASC")
    private List<AttributeEnumValueDo> attributeEnumValues;


}