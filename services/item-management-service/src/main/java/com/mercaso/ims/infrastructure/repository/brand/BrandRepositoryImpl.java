package com.mercaso.ims.infrastructure.repository.brand;

import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.BrandRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.brand.jpa.BrandJpaDao;
import com.mercaso.ims.infrastructure.repository.brand.jpa.dataobject.BrandDo;
import com.mercaso.ims.infrastructure.repository.brand.jpa.mapper.BrandDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class BrandRepositoryImpl implements BrandRepository {

    private final BrandDoMapper brandDoMapper;
    private final BrandJpaDao brandJpaDao;


    @Override
    public Brand save(Brand domain) {

        BrandDo brandDo = brandDoMapper.domainToDo(domain);
        brandDo = brandJpaDao.save(brandDo);
        return brandDoMapper.doToDomain(brandDo);
    }

    @Override
    public Brand findById(UUID id) {
        BrandDo brandDo = brandJpaDao.findById(id).orElse(null);
        if (null == brandDo) {
            return null;
        }
        return brandDoMapper.doToDomain(brandDo);
    }

    @Override
    public Brand update(Brand domain) {

        BrandDo brandDo = brandJpaDao.findById(domain.getId()).orElse(null);
        if (null == brandDo) {
            throw new ImsBusinessException(ErrorCodeEnums.BRAND_NOT_FOUND.getCode());
        }
        BrandDo brandDoTarget = brandDoMapper.domainToDo(domain);

        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
            "createdAt"));
        BeanUtils.copyProperties(brandDoTarget, brandDo, ignoreProperties.toArray(new String[0]));

        brandDo = brandJpaDao.save(brandDo);
        return brandDoMapper.doToDomain(brandDo);
    }

    @Override
    public Brand deleteById(UUID id) {
        BrandDo brandDo = brandJpaDao.findById(id).orElse(null);
        if (null == brandDo) {
            return null;
        }
        brandDo.setDeletedAt(Instant.now());
        brandDo.setDeletedBy(SecurityUtil.getLoginUserId());
        brandDo = brandJpaDao.save(brandDo);
        return brandDoMapper.doToDomain(brandDo);
    }

    @Override
    public Brand findByName(String name) {
        BrandDo brandDo = brandJpaDao.findByName(name);
        if (null == brandDo) {
            return null;
        }
        return brandDoMapper.doToDomain(brandDo);
    }

    @Override
    public List<Brand> findByIds(List<UUID> ids) {
        return Optional.ofNullable(brandJpaDao.findByIdIn(ids))
            .orElse(Collections.emptyList())
            .stream()
            .map(brandDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<Brand> findAll() {
        return Optional.of(brandJpaDao.findAll())
            .orElse(Collections.emptyList())
            .stream()
            .map(brandDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public List<Brand> findByFuzzyName(String name) {
        return Optional.of(brandJpaDao.findByNameContainsIgnoreCase(name))
            .orElse(Collections.emptyList())
            .stream()
            .map(brandDoMapper::doToDomain)
            .toList();
    }

    @Override
    public List<Brand> findByNameIgnoreCase(String name) {
        return Optional.of(brandJpaDao.findByNameIgnoreCase(name))
                .orElse(Collections.emptyList())
                .stream()
                .map(brandDoMapper::doToDomain)
                .toList();
    }
}
