package com.mercaso.ims.infrastructure.repository;

import lombok.experimental.SuperBuilder;

@SuperBuilder
public abstract class PageQuery {

    private static final int DEFAULT_PAGE_SIZE = 20;

    private final Integer page;
    private final Integer pageSize;

    public String offsetLimitSql() {
        int limit = (pageSize != null && pageSize > 0) ? pageSize : DEFAULT_PAGE_SIZE;
        int offset = (page != null && page > 0) ? (page - 1) * limit : 0;
        return " LIMIT " + limit + " OFFSET " + offset;
    }
}
