package com.mercaso.ims.infrastructure.exception;

import lombok.Getter;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.COMMON_CODE;


@Getter
public class ImsBusinessException extends RuntimeException {

    private final String code;

    private final Object[] args;

    public ImsBusinessException(String code) {
        this.code = code;
        this.args = null;
    }

    public ImsBusinessException(String message, Object... args) {
        super(String.format(message, args));
        this.code = COMMON_CODE.getCode();
        this.args = args;
    }

    public ImsBusinessException(ErrorCodeEnums codeEnums, Object... args) {
        super(String.format(codeEnums.getCode(), args));
        this.code = codeEnums.getCode();
        this.args = args;
    }

    public ImsBusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = COMMON_CODE.getCode();
        this.args = null;
    }

}
