package com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord.jpa;

import com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord.jpa.dataobject.VendorPoAnalyzeRecordDo;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface VendorPoAnalyzeRecordJpaDao extends JpaRepository<VendorPoAnalyzeRecordDo, UUID> {

    VendorPoAnalyzeRecordDo findByItemCostCollectionId(UUID itemCostCollectionId);

    VendorPoAnalyzeRecordDo findByOriginalFileName(String originalFileName);
}
