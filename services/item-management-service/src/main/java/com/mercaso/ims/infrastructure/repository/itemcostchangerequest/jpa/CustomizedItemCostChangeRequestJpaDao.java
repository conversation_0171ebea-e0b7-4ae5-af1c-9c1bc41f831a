package com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa;

import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.application.query.ItemCostChangeRequestQuery;
import java.util.List;

public interface CustomizedItemCostChangeRequestJpaDao {

    List<ItemCostChangeRequestDto> fetchItemCostChangeRequestDtoList(ItemCostChangeRequestQuery itemCostChangeRequestQuery);

    long countQuery(ItemCostChangeRequestQuery itemCostChangeRequestQuery);
}
