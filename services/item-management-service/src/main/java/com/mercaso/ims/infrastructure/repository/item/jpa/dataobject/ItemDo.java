package com.mercaso.ims.infrastructure.repository.item.jpa.dataobject;

import com.mercaso.ims.domain.item.enums.*;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import com.mercaso.ims.infrastructure.repository.itemattribute.jpa.dataobject.ItemAttributeDo;
import com.mercaso.ims.infrastructure.repository.itemimage.jpa.dataobject.ItemImageDo;
import com.mercaso.ims.infrastructure.repository.itemtag.jpa.dataobject.ItemTagDo;
import com.mercaso.ims.infrastructure.repository.itemupc.jpa.dataobject.ItemUPCDo;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;


@Entity
@ToString(callSuper = true)
@Table(name = "item")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemDo extends BaseDo {

    @Column(name = "category_id")
    private UUID categoryId;

    @Column(name = "brand_id")
    private UUID brandId;

    @Column(name = "name")
    private String name;

    @Column(name = "title")
    private String title;

    @Column(name = "sku_number")
    private String skuNumber;

    @Column(name = "description")
    private String description;

    @Column(name = "note")
    private String note;

    @Column(name = "photo")
    private String photo;

    @Column(name = "primary_vendor_id")
    private UUID primaryVendorId;

    @Column(name = "backup_vendor_id")
    private UUID backupVendorId;

    @Column(name = "detail")
    private String detail;

    @Column(name = "package_type")
    @Enumerated(EnumType.STRING)
    private PackageType packageType;

    @Column(name = "package_size")
    private Integer packageSize;

    @Column(name = "shelf_life")
    private String shelfLife;

    @Column(name = "item_type")
    @Enumerated(EnumType.STRING)
    private ItemType itemType;

    @Column(name = "sales_status")
    @Enumerated(EnumType.STRING)
    private SalesStatus salesStatus;

    @Column(name = "availability_status")
    @Enumerated(EnumType.STRING)
    private AvailabilityStatus availabilityStatus;

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "location_id")
    private Long locationId;

    @Column(name = "handle")
    private String handle;

    @Column(name = "department")
    private String department;
    @Column(name = "category")
    private String category;
    @Column(name = "sub_category")
    private String subCategory;
    @Column(name = "clazz")
    private String clazz;
    @Column(name = "new_description")
    private String newDescription;
    @Column(name = "item_length")
    private Double length;
    @Column(name = "item_height")
    private Double height;
    @Column(name = "item_width")
    private Double width;
    @Column(name = "missing_each_upc_reason")
    @Enumerated(EnumType.STRING)
    private MissingEachUpcReason missingEachUpcReason;
    @Column(name = "missing_case_upc_reason")
    @Enumerated(EnumType.STRING)
    private MissingCaseUpcReason missingCaseUpcReason;

    @Column(name = "archived_reason")
    @Enumerated(EnumType.STRING)
    private ArchivedReason archivedReason;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
    @JoinColumn(name = "item_id")
    private List<ItemAttributeDo> itemAttributes;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
    @JoinColumn(name = "item_id")
    private List<ItemTagDo> itemTags;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
    @JoinColumn(name = "item_id")
    private List<ItemImageDo> itemImages;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true)
    @JoinColumn(name = "item_id")
    private List<ItemUPCDo> itemUPCs;

}
