package com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail.jpa;

import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequestdetail.jpa.dataobject.ItemAdjustmentRequestDetailDo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface ItemAdjustmentRequestDetailJpaDao extends JpaRepository<ItemAdjustmentRequestDetailDo, UUID> {

    List<ItemAdjustmentRequestDetailDo> findAllByStatus(ItemAdjustmentStatus status);

    List<ItemAdjustmentRequestDetailDo> findAllByRequestId(UUID requestId);

    List<ItemAdjustmentRequestDetailDo> findAllBySku(String skuNumber);
}
