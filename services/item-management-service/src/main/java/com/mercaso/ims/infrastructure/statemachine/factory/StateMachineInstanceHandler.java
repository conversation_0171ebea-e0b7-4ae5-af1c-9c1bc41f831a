package com.mercaso.ims.infrastructure.statemachine.factory;


import com.mercaso.ims.infrastructure.statemachine.StateTransitionType;
import com.mercaso.ims.infrastructure.statemachine.StateType;
import com.mercaso.ims.infrastructure.statemachine.StatefulContext;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.stereotype.Component;

@Component
public class StateMachineInstanceHandler<S extends StateType, E extends StateTransitionType> implements
    StateMachinePersist<S, E, StatefulContext<S>> {

    @Override
    public void write(StateMachineContext<S, E> context, StatefulContext<S> contextObj) {
        contextObj.setStatus(context.getState());
    }

    @Override
    public StateMachineContext<S, E> read(StatefulContext<S> contextObj) {
        return new DefaultStateMachineContext<>(contextObj.getStatus(), null, null, null);
    }

}
