package com.mercaso.ims.infrastructure.external.finale.enums;

import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import java.util.Arrays;

public enum StatusId {

    PRODUCT_INACTIVE("deactivate"),
    PRODUCT_ACTIVE("activate"),
    UNKNOWN(null),
    ;

    private final String action;

    StatusId(String action) {
        this.action = action;
    }

    public String getAction() {
        return action;
    }

    public static StatusId fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equals(name)).findFirst().orElse(UNKNOWN);
    }

    public static StatusId fromAvailabilityStatus(String availabilityStatus) {
        if (availabilityStatus.equals(AvailabilityStatus.ARCHIVED.name())) {
            return PRODUCT_INACTIVE;
        } else {
            return PRODUCT_ACTIVE;
        }
    }

    public static StatusId fromAction(String action) {
        return Arrays.stream(values()).filter(v -> action.equals(v.action)).findFirst().orElse(UNKNOWN);
    }
}
