package com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord;

import com.mercaso.ims.domain.vendorpoanalyzerecord.VendorPoAnalyzeRecord;
import com.mercaso.ims.domain.vendorpoanalyzerecord.VendorPoAnalyzeRecordRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord.jpa.VendorPoAnalyzeRecordJpaDao;
import com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord.jpa.dataobject.VendorPoAnalyzeRecordDo;
import com.mercaso.ims.infrastructure.repository.vendorpoanalyzerecord.jpa.mapper.VendorPoAnalyzeRecordDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class VendorPoAnalyzeRecordRepositoryImpl implements VendorPoAnalyzeRecordRepository {

    private final VendorPoAnalyzeRecordJpaDao vendorPoAnalyzeRecordJpaDao;

    private final VendorPoAnalyzeRecordDoMapper vendorPoAnalyzeRecordDoMapper;


    @Override
    public VendorPoAnalyzeRecord save(VendorPoAnalyzeRecord domain) {
        VendorPoAnalyzeRecordDo vendorPoAnalyzeRecordDo = vendorPoAnalyzeRecordDoMapper.domainToDo(domain);
        vendorPoAnalyzeRecordDo = vendorPoAnalyzeRecordJpaDao.save(vendorPoAnalyzeRecordDo);
        return vendorPoAnalyzeRecordDoMapper.doToDomain(vendorPoAnalyzeRecordDo);
    }

    @Override
    public VendorPoAnalyzeRecord findById(UUID id) {
        VendorPoAnalyzeRecordDo doObj = vendorPoAnalyzeRecordJpaDao.findById(id).orElse(null);
        if (null == doObj) {
            return null;
        }
        return vendorPoAnalyzeRecordDoMapper.doToDomain(doObj);
    }

    @Override
    public VendorPoAnalyzeRecord update(VendorPoAnalyzeRecord domain) {
        VendorPoAnalyzeRecordDo doObj = vendorPoAnalyzeRecordJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(doObj)) {
            throw new ImsBusinessException(ErrorCodeEnums.VENDOR_PO_ANALYZE_RECORD_NOT_FOUND);
        }
        VendorPoAnalyzeRecordDo doObjTarget = vendorPoAnalyzeRecordDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(doObjTarget, doObj, ignoreProperties.toArray(new String[0]));
        VendorPoAnalyzeRecordDo result = vendorPoAnalyzeRecordJpaDao.save(doObj);
        return vendorPoAnalyzeRecordDoMapper.doToDomain(result);
    }

    @Override
    public VendorPoAnalyzeRecord deleteById(UUID id) {
        VendorPoAnalyzeRecordDo doObj = vendorPoAnalyzeRecordJpaDao.findById(id).orElse(null);
        if (null == doObj) {
            return null;
        }
        doObj.setDeletedAt(Instant.now());
        doObj.setDeletedBy(SecurityUtil.getLoginUserId());
        doObj.setDeletedUserName(SecurityUtil.getUserName());
        doObj = vendorPoAnalyzeRecordJpaDao.save(doObj);
        return vendorPoAnalyzeRecordDoMapper.doToDomain(doObj);
    }

    @Override
    public VendorPoAnalyzeRecord findByItemCostCollectionId(UUID itemCostCollectionId) {
        VendorPoAnalyzeRecordDo doObj = vendorPoAnalyzeRecordJpaDao.findByItemCostCollectionId(itemCostCollectionId);
        if (null == doObj) {
            return null;
        }
        return vendorPoAnalyzeRecordDoMapper.doToDomain(doObj);
    }

    @Override
    public VendorPoAnalyzeRecord findByOriginalFileName(String originalFileName) {
        VendorPoAnalyzeRecordDo doObj = vendorPoAnalyzeRecordJpaDao.findByOriginalFileName(originalFileName);
        if (null == doObj) {
            return null;
        }
        return vendorPoAnalyzeRecordDoMapper.doToDomain(doObj);
    }
}
