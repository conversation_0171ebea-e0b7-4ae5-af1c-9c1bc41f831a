package com.mercaso.ims.infrastructure.apitaskprocess.payload;

import com.mercaso.ims.infrastructure.annotation.RateLimitedTask;
import lombok.Builder;
import lombok.Data;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * Context object that holds all information needed for task execution
 * Used by both synchronous and asynchronous task processors
 */
@Data
@Builder
public class TaskExecutionContextPayload {

    /**
     * The original AOP join point
     */
    private final ProceedingJoinPoint joinPoint;

    /**
     * The @RateLimitedTask annotation
     */
    private final RateLimitedTask annotation;

    /**
     * The target method being executed
     */
    private final Method method;

    /**
     * Method signature information
     */
    private final MethodSignature signature;

    /**
     * Method parameters mapped by name
     */
    private final Map<String, Object> parameters;

    /**
     * Full method name (ClassName.methodName)
     */
    private final String fullMethodName;

    /**
     * Simple method name
     */
    private final String methodName;

    /**
     * Class name
     */
    private final String className;

    /**
     * Task type from annotation
     */
    private final String taskType;

    /**
     * Rate limiter name for this task type
     */
    private final String rateLimiterName;

    /**
     * API endpoint (from annotation or derived from method name)
     */
    private final String endpoint;

    /**
     * Whether this is a synchronous execution
     */
    private final boolean synchronous;

    /**
     * Whether to bypass the task queue
     */
    private final boolean bypass;

    /**
     * Execute the original method through the join point
     */
    public Object executeOriginalMethod() throws Throwable {
        return joinPoint.proceed();
    }

    /**
     * Get method arguments
     */
    public Object[] getMethodArguments() {
        return joinPoint.getArgs();
    }

    /**
     * Get parameter names
     */
    public String[] getParameterNames() {
        return signature.getParameterNames();
    }
}
