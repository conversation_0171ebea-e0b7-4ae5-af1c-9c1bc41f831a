package com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa.mapper;

import com.mercaso.ims.domain.bulkexportrecords.BulkExportRecords;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.bulkexportrecords.jpa.dataobject.BulkExportRecordsDo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface BulkExportRecordsDoMapper extends BaseDoMapper<BulkExportRecordsDo, BulkExportRecords> {
    @Override
    @Mapping(target = "exportBy", source = "createdUserName")
    BulkExportRecords doToDomain(BulkExportRecordsDo bulkExportRecordsDo);


    @Override
    @Mapping(target = "createdUserName", source = "exportBy")
    BulkExportRecordsDo domainToDo(BulkExportRecords bulkExportRecords);
}

