package com.mercaso.ims.infrastructure.repository.address;

import com.mercaso.ims.domain.address.Address;
import com.mercaso.ims.domain.address.AddressRepository;
import com.mercaso.ims.domain.address.enums.AddressPurpose;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.address.jpa.AddressJpaDao;
import com.mercaso.ims.infrastructure.repository.address.jpa.dataobject.AddressDo;
import com.mercaso.ims.infrastructure.repository.address.jpa.mapper.AddressDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class AddressRepositoryImpl implements AddressRepository {

    private final AddressDoMapper addressDoMapper;
    private final AddressJpaDao addressJpaDao;

    @Override
    public Address save(Address domain) {
        AddressDo addressDo = addressDoMapper.domainToDo(domain);
        addressDo = addressJpaDao.save(addressDo);
        return addressDoMapper.doToDomain(addressDo);
    }

    @Override
    public Address findById(UUID id) {
        return addressDoMapper.doToDomain(addressJpaDao.findById(id).orElse(null));
    }

    @Override
    public Address update(Address domain) {
        AddressDo addressDo = addressJpaDao.findById(domain.getId()).orElse(null);
        if (null == addressDo) {
            throw new ImsBusinessException(ErrorCodeEnums.ADDRESS_NOT_FOUND);
        }
        AddressDo target = addressDoMapper.domainToDo(domain);
        List<String> ignoreProperties = List.of("createdBy", "createdAt");
        BeanUtils.copyProperties(target, addressDo, ignoreProperties.toArray(new String[0]));
        addressDo = addressJpaDao.save(addressDo);
        return addressDoMapper.doToDomain(addressDo);
    }

    @Override
    public Address deleteById(UUID id) {
        AddressDo addressDo = addressJpaDao.findById(id).orElse(null);
        if (null == addressDo) {
            return null;
        }
        addressDo.setDeletedAt(Instant.now());
        addressDo.setDeletedBy(SecurityUtil.getLoginUserId());
        addressDo.setDeletedUserName(SecurityUtil.getUserName());
        addressDo = addressJpaDao.save(addressDo);
        return addressDoMapper.doToDomain(addressDo);
    }

    @Override
    public List<Address> findByEntityTypeAndEntityId(String entityType, UUID entityId) {
        return Optional.ofNullable(addressJpaDao.findByEntityTypeAndEntityId(entityType, entityId))
                .orElse(List.of())
                .stream()
                .map(addressDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<Address> findByEntityTypeAndEntityIdAndPurpose(String entityType, UUID entityId, AddressPurpose purpose) {
        return Optional.ofNullable(addressJpaDao.findByEntityTypeAndEntityIdAndPurpose(entityType, entityId, purpose))
                .orElse(List.of())
                .stream()
                .map(addressDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<Address> findByPostalCode(String postalCode) {
        return Optional.ofNullable(addressJpaDao.findByPostalCode(postalCode))
                .orElse(List.of())
                .stream()
                .map(addressDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<Address> findByCity(String city) {
        return Optional.ofNullable(addressJpaDao.findByCity(city))
                .orElse(List.of())
                .stream()
                .map(addressDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<Address> findByState(String state) {
        return Optional.ofNullable(addressJpaDao.findByState(state))
                .orElse(List.of())
                .stream()
                .map(addressDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<Address> findByCountry(String country) {
        return Optional.ofNullable(addressJpaDao.findByCountry(country))
                .orElse(List.of())
                .stream()
                .map(addressDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<Address> findByEntityType(String entityType) {
        return Optional.ofNullable(addressJpaDao.findByEntityType(entityType))
                .orElse(List.of())
                .stream()
                .map(addressDoMapper::doToDomain)
                .toList();
    }
}
