package com.mercaso.ims.infrastructure.repository.itemadjustmentrequest;

import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequest;
import com.mercaso.ims.domain.itemadjustmentrequest.ItemAdjustmentRequestRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa.ItemAdjustmentRequestJpaDao;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa.dataobject.ItemAdjustmentRequestDo;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa.mapper.ItemAdjustmentRequestDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ItemAdjustmentRequestRepositoryImpl implements ItemAdjustmentRequestRepository {

    private final ItemAdjustmentRequestJpaDao jpaDao;
    private final ItemAdjustmentRequestDoMapper requestDoMapper;
    

    @Override
    public List<ItemAdjustmentRequest> findByIdIn(List<UUID> ids) {
        return Optional.ofNullable(jpaDao.findByIdIn(ids))
            .orElse(Collections.emptyList())
            .stream()
            .map(requestDoMapper::doToDomain)
            .toList().reversed();
    }

    @Override
    public ItemAdjustmentRequest save(ItemAdjustmentRequest domain) {
        ItemAdjustmentRequestDo requestDo = requestDoMapper.domainToDo(domain);
        requestDo = jpaDao.save(requestDo);
        return requestDoMapper.doToDomain(requestDo);

    }

    @Override
    public ItemAdjustmentRequest findById(UUID id) {
        return requestDoMapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public ItemAdjustmentRequest update(ItemAdjustmentRequest domain) {
        ItemAdjustmentRequestDo requestDo = jpaDao.findById(domain.getId()).orElse(null);
        if (null == requestDo) {
            throw new ImsBusinessException(ErrorCodeEnums.ITEM_ADJUSTMENT_REQUEST_NOT_FOUND.getCode());
        }

        List<String> ignoreProperties = new ArrayList<>(List.of("createdBy",
            "createdAt"));

        ItemAdjustmentRequestDo requestDoTarget = requestDoMapper.domainToDo(domain);
        BeanUtils.copyProperties(requestDoTarget, requestDo, ignoreProperties.toArray(new String[0]));

        requestDo = jpaDao.save(requestDoTarget);
        return requestDoMapper.doToDomain(requestDo);
    }

    @Override
    public ItemAdjustmentRequest deleteById(UUID id) {
        ItemAdjustmentRequestDo requestDo = jpaDao.findById(id).orElse(null);
        if (null == requestDo) {
            return null;
        }
        requestDo.setDeletedAt(Instant.now());
        requestDo.setDeletedBy(SecurityUtil.getLoginUserId());
        requestDo = jpaDao.save(requestDo);
        return requestDoMapper.doToDomain(requestDo);
    }
}
