package com.mercaso.ims.infrastructure.repository.attributegroup.jpa.mapper;

import com.mercaso.ims.domain.attributegroup.AttributeGroup;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.attributegroup.jpa.dataobject.AttributeGroupDo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.util.ObjectUtils;

@Mapper(componentModel = "spring", imports = ObjectUtils.class, uses = AttributeGroupDetailDoMapper.class)
public interface AttributeGroupDoMapper extends BaseDoMapper<AttributeGroupDo, AttributeGroup> {

    AttributeGroupDoMapper INSTANCE = Mappers.getMapper(AttributeGroupDoMapper.class);

    @Mapping(target = "attributeGroupDetails", source = "attributeGroupDetails")
    AttributeGroup doToDomain(AttributeGroupDo attributeGroupDo);

    @Mapping(target = "attributeGroupDetails", source = "attributeGroupDetails")
    AttributeGroupDo domainToDo(AttributeGroup attributeGroup);

}