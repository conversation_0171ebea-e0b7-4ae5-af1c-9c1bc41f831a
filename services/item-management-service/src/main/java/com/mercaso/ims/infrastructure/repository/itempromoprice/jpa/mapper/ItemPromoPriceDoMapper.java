package com.mercaso.ims.infrastructure.repository.itempromoprice.jpa.mapper;

import com.mercaso.ims.domain.itempromoprice.ItemPromoPrice;
import com.mercaso.ims.infrastructure.repository.BaseDoMapper;
import com.mercaso.ims.infrastructure.repository.itempromoprice.jpa.dataobject.ItemPromoPriceDo;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ItemPromoPriceDoMapper extends BaseDoMapper<ItemPromoPriceDo, ItemPromoPrice> {

}
