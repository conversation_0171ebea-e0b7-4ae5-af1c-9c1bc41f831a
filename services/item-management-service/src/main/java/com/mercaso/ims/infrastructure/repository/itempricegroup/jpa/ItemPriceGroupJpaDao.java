package com.mercaso.ims.infrastructure.repository.itempricegroup.jpa;

import com.mercaso.ims.infrastructure.repository.itempricegroup.jpa.dataobject.ItemPriceGroupDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;


public interface ItemPriceGroupJpaDao extends JpaRepository<ItemPriceGroupDo, UUID> {

    ItemPriceGroupDo findByGroupName(String groupName);

    List<ItemPriceGroupDo> findByGroupNameIsLikeIgnoreCase(String groupName);

}