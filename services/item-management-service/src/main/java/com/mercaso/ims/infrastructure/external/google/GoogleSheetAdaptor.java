package com.mercaso.ims.infrastructure.external.google;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.GET_FIRST_SHEET_NAME_ERROR;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.NO_SHEETS_FOUND_IN_SPREADSHEET;
import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.READ_GOOGLE_SHEET_ERROR;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.sheets.v4.Sheets;
import com.google.api.services.sheets.v4.SheetsScopes;
import com.google.api.services.sheets.v4.model.Spreadsheet;
import com.google.api.services.sheets.v4.model.ValueRange;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GoogleSheetAdaptor {

    private static final String APPLICATION_NAME = "IMS SUPPLIER COST";
    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    private static final String LOG_PREFIX = "[GoogleSheetAdaptor]";


    @Value("${googleDriver.credentials}")
    private String credentials;

    public List<List<Object>> readGoogleSheet(String spreadsheetId, String range) {
        validateSpreadsheetId(spreadsheetId);

        try {
            Sheets sheetsService = createSheetsService();

            String effectiveRange = Optional.ofNullable(range)
                .filter(StringUtils::isNotBlank)
                .orElseGet(() -> getFirstSheetName(spreadsheetId, sheetsService));

            ValueRange response = sheetsService.spreadsheets().values()
                .get(spreadsheetId, effectiveRange)
                .execute();

            return Optional.ofNullable(response.getValues())
                .orElseGet(() -> {
                    log.warn("{} No data found in sheet: {}", LOG_PREFIX, spreadsheetId);
                    return Collections.emptyList();
                });

        } catch (Exception e) {
            log.error("{} Failed to read Google Sheet: {}", LOG_PREFIX, e.getMessage(), e);
            throw new ImsBusinessException(READ_GOOGLE_SHEET_ERROR);
        }
    }

    private Sheets createSheetsService() throws IOException, GeneralSecurityException {
        GoogleCredentials googleCredentials = createGoogleCredentials();
        NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();

        return new Sheets.Builder(httpTransport, JSON_FACTORY, new HttpCredentialsAdapter(googleCredentials))
            .setApplicationName(APPLICATION_NAME)
            .build();
    }

    private GoogleCredentials createGoogleCredentials() throws IOException {
        return GoogleCredentials.fromStream(new ByteArrayInputStream(credentials.getBytes(StandardCharsets.UTF_8)))
            .createScoped(Collections.singleton(SheetsScopes.SPREADSHEETS_READONLY));
    }

    private String getFirstSheetName(String spreadsheetId, Sheets sheetsService) {
        try {
            Spreadsheet spreadsheet = sheetsService.spreadsheets().get(spreadsheetId).execute();
            return Optional.ofNullable(spreadsheet.getSheets())
                .filter(sheets -> !sheets.isEmpty())
                .map(List::getFirst)
                .map(sheet -> sheet.getProperties().getTitle())
                .orElseThrow(() -> new ImsBusinessException(NO_SHEETS_FOUND_IN_SPREADSHEET));

        } catch (Exception e) {
            log.error("{} Failed to get first sheet name: {}", LOG_PREFIX, e.getMessage(), e);
            throw new ImsBusinessException(GET_FIRST_SHEET_NAME_ERROR);
        }
    }

    private void validateSpreadsheetId(String spreadsheetId) {
        if (StringUtils.isBlank(spreadsheetId)) {
            throw new IllegalArgumentException("SpreadsheetId cannot be null or empty");
        }
    }
}
