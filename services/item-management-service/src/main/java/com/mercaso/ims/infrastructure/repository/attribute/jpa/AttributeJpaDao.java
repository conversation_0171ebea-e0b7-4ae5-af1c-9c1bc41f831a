package com.mercaso.ims.infrastructure.repository.attribute.jpa;

import com.mercaso.ims.infrastructure.repository.attribute.jpa.dataobject.AttributeDo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface AttributeJpaDao extends JpaRepository<AttributeDo, UUID> {
    List<AttributeDo> findByName(String name);

    AttributeDo findByCategoryIdAndName(UUID categoryId, String name);

    List<AttributeDo> findByCategoryId(UUID categoryId);

    List<AttributeDo> findAllByIdIn(List<UUID> ids);

    List<AttributeDo> findByNameContainsIgnoreCase(String name);

}
