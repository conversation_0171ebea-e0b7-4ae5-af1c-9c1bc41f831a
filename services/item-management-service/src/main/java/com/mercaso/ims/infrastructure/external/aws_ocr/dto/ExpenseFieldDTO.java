package com.mercaso.ims.infrastructure.external.aws_ocr.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExpenseFieldDTO {
    private ExpenseTypeDTO type;
    private LabelDetectionDTO labelDetection;
    private ValueDetectionDTO valueDetection;
    private Integer pageNumber;
    private ExpenseCurrencyDTO currency;
    private List<ExpenseGroupPropertyDTO> groupProperties;
}
