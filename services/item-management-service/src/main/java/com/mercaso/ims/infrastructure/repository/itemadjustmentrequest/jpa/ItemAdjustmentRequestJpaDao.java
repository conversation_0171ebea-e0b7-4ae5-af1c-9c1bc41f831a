package com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa;

import com.mercaso.ims.domain.itemadjustmentrequest.enums.ItemAdjustmentRequestStatus;
import com.mercaso.ims.infrastructure.repository.itemadjustmentrequest.jpa.dataobject.ItemAdjustmentRequestDo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface ItemAdjustmentRequestJpaDao extends JpaRepository<ItemAdjustmentRequestDo, UUID> {

    ItemAdjustmentRequestDo findByCreatedBy(String uploaderId);

    Page<ItemAdjustmentRequestDo> findAllByStatusInOrderByCreatedAtDesc(List<ItemAdjustmentRequestStatus> statuses, Pageable pageable);

    List<ItemAdjustmentRequestDo> findByIdIn(List<UUID> ids);
}
