package com.mercaso.ims.infrastructure.external.finale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompletedPurchaseOrderItemDto {
    
    private String supplierProductId;
    private String productId;
    private String packing;
    private Double pricePerUnit;
    private Double amount;
    private String productUnitsOrdered;
    private String description;
}