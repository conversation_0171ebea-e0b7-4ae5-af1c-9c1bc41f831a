package com.mercaso.ims.infrastructure.repository.itemimage.jpa.dataobject;

import com.mercaso.ims.domain.item.enums.ImageType;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;


@Entity
@Table(name = "item_image")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_image set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemImageDo extends BaseDo {

    @Column(name = "item_id")
    private UUID itemId;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "image_type")
    @Enumerated(EnumType.STRING)
    private ImageType imageType;

    @Column(name = "status")
    private String status;

    @Column(name = "sort")
    private Integer sort;


}
