package com.mercaso.ims.infrastructure.repository.location.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;

@Entity
@Table(name = "location")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update location set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class LocationDo extends BaseDo {

    @Column(name = "location_id")
    private Long locationId;

    @Column(name = "company_uuid")
    private UUID companyUUID;

    @Column(name = "name")
    private String name;


}
