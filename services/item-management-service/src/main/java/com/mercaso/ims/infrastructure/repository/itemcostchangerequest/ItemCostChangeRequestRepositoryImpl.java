package com.mercaso.ims.infrastructure.repository.itemcostchangerequest;

import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequest;
import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequestRepository;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa.ItemCostChangeRequestJpaDao;
import com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa.dataobject.ItemCostChangeRequestDo;
import com.mercaso.ims.infrastructure.repository.itemcostchangerequest.jpa.mapper.ItemCostChangeRequestDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ItemCostChangeRequestRepositoryImpl implements ItemCostChangeRequestRepository {

    private final ItemCostChangeRequestDoMapper itemCostChangeRequestDoMapper;

    private final ItemCostChangeRequestJpaDao itemCostChangeRequestJpaDao;

    @Override
    public ItemCostChangeRequest save(ItemCostChangeRequest domain) {
        ItemCostChangeRequestDo itemCostChangeRequestDo = itemCostChangeRequestDoMapper.domainToDo(domain);
        itemCostChangeRequestDo = itemCostChangeRequestJpaDao.save(itemCostChangeRequestDo);
        return itemCostChangeRequestDoMapper.doToDomain(itemCostChangeRequestDo);
    }

    @Override
    public ItemCostChangeRequest findById(UUID id) {
        ItemCostChangeRequestDo itemCostChangeRequestDo = itemCostChangeRequestJpaDao.findById(id).orElse(null);
        if (null == itemCostChangeRequestDo) {
            return null;
        }
        return itemCostChangeRequestDoMapper.doToDomain(itemCostChangeRequestDo);
    }

    @Override
    public ItemCostChangeRequest update(ItemCostChangeRequest domain) {
        ItemCostChangeRequestDo itemCostChangeRequestDo = itemCostChangeRequestJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(itemCostChangeRequestDo)) {
            throw new ImsBusinessException(ErrorCodeEnums.VENDOR_PO_INVOICE_ITEM_NOT_FOUND);
        }
        ItemCostChangeRequestDo itemCostChangeRequestTarget = itemCostChangeRequestDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(itemCostChangeRequestTarget, itemCostChangeRequestDo, ignoreProperties.toArray(new String[0]));
        ItemCostChangeRequestDo result = itemCostChangeRequestJpaDao.save(itemCostChangeRequestDo);
        return itemCostChangeRequestDoMapper.doToDomain(result);
    }

    @Override
    public ItemCostChangeRequest deleteById(UUID id) {
        ItemCostChangeRequestDo itemCostChangeRequestDo = itemCostChangeRequestJpaDao.findById(id).orElse(null);
        if (Objects.isNull(itemCostChangeRequestDo)) {
            return null;
        }

        itemCostChangeRequestDo.setDeletedAt(Instant.now());
        itemCostChangeRequestDo.setDeletedBy(SecurityUtil.getLoginUserId());
        itemCostChangeRequestDo.setDeletedUserName(SecurityUtil.getUserName());
        itemCostChangeRequestDo = itemCostChangeRequestJpaDao.save(itemCostChangeRequestDo);
        return itemCostChangeRequestDoMapper.doToDomain(itemCostChangeRequestDo);

    }

    @Override
    public List<ItemCostChangeRequest> findByItemCostCollectionId(UUID itemCostCollectionId) {
        return Optional.ofNullable(itemCostChangeRequestJpaDao.findByItemCostCollectionId(itemCostCollectionId))
            .orElse(Collections.emptyList())
            .stream()
            .map(itemCostChangeRequestDoMapper::doToDomain)
            .sorted(Comparator.comparing(ItemCostChangeRequest::getStatus))
            .toList();
    }

    @Override
    public List<ItemCostChangeRequest> findByItemCostCollectionIdIn(List<UUID> itemCostCollectionIds) {
        return Optional.ofNullable(itemCostChangeRequestJpaDao.findByItemCostCollectionIdIn(itemCostCollectionIds))
            .orElse(Collections.emptyList())
            .stream()
            .map(itemCostChangeRequestDoMapper::doToDomain)
            .sorted(Comparator.comparing(ItemCostChangeRequest::getStatus))
            .toList();
    }
}


