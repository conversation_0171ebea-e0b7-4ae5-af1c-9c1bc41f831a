package com.mercaso.ims.infrastructure.apitaskprocess;

import lombok.extern.slf4j.Slf4j;

/**
 * Task execution context manager
 * Uses ThreadLocal to track whether current thread is executing within a task processor
 * This helps avoid infinite loops when processors call methods with @RateLimitedTask annotations
 */
@Slf4j
public class TaskExecutionContext {

    private TaskExecutionContext() {
    }

    private static final ThreadLocal<Boolean> inTaskExecution = new ThreadLocal<>();

    /**
     * Mark current thread as executing within a task processor
     */
    public static void markAsTaskExecution() {
        inTaskExecution.set(true);
        log.debug("Marked current thread as task execution context");
    }

    /**
     * Clear task execution context for current thread
     * Should be called in finally block to ensure cleanup
     */
    public static void clearTaskExecution() {
        inTaskExecution.remove();
        log.debug("Cleared task execution context for current thread");
    }

    /**
     * Check if current thread is executing within a task processor
     *
     * @return true if in task execution context, false otherwise
     */
    public static boolean isInTaskExecution() {
        return Boolean.TRUE.equals(inTaskExecution.get());
    }

    /**
     * Execute a runnable within task execution context
     * Automatically manages context setup and cleanup
     *
     * @param runnable the code to execute
     */
    public static void executeInTaskContext(Runnable runnable) {
        try {
            markAsTaskExecution();
            runnable.run();
        } finally {
            clearTaskExecution();
        }
    }

    /**
     * Execute a supplier within task execution context
     * Automatically manages context setup and cleanup
     *
     * @param supplier the code to execute
     * @param <T> return type
     * @return result of supplier execution
     */
    public static <T> T executeInTaskContext(java.util.function.Supplier<T> supplier) {
        try {
            markAsTaskExecution();
            return supplier.get();
        } finally {
            clearTaskExecution();
        }
    }
}
