package com.mercaso.ims.infrastructure.repository.itemregprice.jpa;

import com.mercaso.ims.infrastructure.repository.itemregprice.jpa.dataobject.ItemRegPriceDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ItemRegPriceJpaDao extends JpaRepository<ItemRegPriceDo, UUID> {

    ItemRegPriceDo findByItemId(UUID itemId);

    List<ItemRegPriceDo> findByItemIdIn(List<UUID> itemIds);

    List<ItemRegPriceDo> findByItemPriceGroupId(UUID itemPriceGroupId);

}
