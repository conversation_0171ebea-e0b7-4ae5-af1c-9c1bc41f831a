package com.mercaso.ims.infrastructure.repository.phonenumber.jpa;

import com.mercaso.ims.domain.phone.enums.PhoneType;
import com.mercaso.ims.infrastructure.repository.phonenumber.jpa.dataobject.PhoneNumberDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface PhoneNumberJpaDao extends JpaRepository<PhoneNumberDo, UUID> {

    /**
     * Find phone numbers by entity type and entity ID
     */
    List<PhoneNumberDo> findByEntityTypeAndEntityId(String entityType, UUID entityId);

    /**
     * Find phone numbers by entity type, entity ID and phone type
     */
    List<PhoneNumberDo> findByEntityTypeAndEntityIdAndPhoneType(String entityType, UUID entityId, PhoneType phoneType);

    /**
     * Find phone numbers by phone number
     */
    List<PhoneNumberDo> findByPhoneNumber(String phoneNumber);

    /**
     * Find phone numbers by entity type
     */
    List<PhoneNumberDo> findByEntityType(String entityType);

    /**
     * Find phone numbers by entity type and entity ID with custom query for better performance
     */
    @Query("SELECT p FROM PhoneNumberDo p WHERE p.entityType = :entityType AND p.entityId = :entityId")
    List<PhoneNumberDo> findByEntityTypeAndEntityIdCustom(@Param("entityType") String entityType, @Param("entityId") UUID entityId);
}
