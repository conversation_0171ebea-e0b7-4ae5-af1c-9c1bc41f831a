package com.mercaso.ims.infrastructure.statemachine.factory;

import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.statemachine.StateTransitionType;
import com.mercaso.ims.infrastructure.statemachine.StateType;
import com.mercaso.ims.infrastructure.statemachine.StatefulContext;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateMachine;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StateEventHandler {

    public static <S extends StateType, E extends StateTransitionType, T extends StatefulContext<S>>
    void processEvent(ImsStateMachineFactory<S, E, T> factory, T entity, E event) {
        StateMachine<S, E> stateMachine = null;
        try {
            stateMachine = factory.create(entity);
            if (stateMachine.sendEvent(event)) {
                StateType oldStatus = entity.getStatus();
                factory.updateInstanceState(stateMachine, entity);

                log.info("{} 's instance state changed from: {}, to: {}",
                        entity.getClass().getName(), oldStatus, entity.getStatus());
            } else {
                throw new ImsBusinessException("processEvent - %s State change event %s not accepted from state %s.",
                        entity.getClass().getName(), event.toString(), entity.getStatus());
            }
        } finally {
            if (stateMachine != null) {
                stateMachine.stop();
            }
        }
    }
}
