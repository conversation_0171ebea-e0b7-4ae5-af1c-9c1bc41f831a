package com.mercaso.ims.infrastructure.repository.address.jpa;

import com.mercaso.ims.domain.address.enums.AddressPurpose;
import com.mercaso.ims.infrastructure.repository.address.jpa.dataobject.AddressDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface AddressJpaDao extends JpaRepository<AddressDo, UUID> {

    List<AddressDo> findByEntityTypeAndEntityId(String entityType, UUID entityId);

    List<AddressDo> findByEntityTypeAndEntityIdAndPurpose(String entityType, UUID entityId, AddressPurpose purpose);

    List<AddressDo> findByPostalCode(String postalCode);

    List<AddressDo> findByCity(String city);

    List<AddressDo> findByState(String state);

    List<AddressDo> findByCountry(String country);

    List<AddressDo> findByEntityType(String entityType);
}
