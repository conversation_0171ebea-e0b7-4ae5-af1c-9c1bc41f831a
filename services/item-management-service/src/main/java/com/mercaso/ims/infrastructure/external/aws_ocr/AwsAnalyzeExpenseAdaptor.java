package com.mercaso.ims.infrastructure.external.aws_ocr;

import static com.mercaso.document.operations.constants.CommonSymbols.SLASH;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.textract.TextractClient;
import software.amazon.awssdk.services.textract.model.DocumentLocation;
import software.amazon.awssdk.services.textract.model.GetExpenseAnalysisRequest;
import software.amazon.awssdk.services.textract.model.GetExpenseAnalysisResponse;
import software.amazon.awssdk.services.textract.model.JobStatus;
import software.amazon.awssdk.services.textract.model.S3Object;
import software.amazon.awssdk.services.textract.model.StartExpenseAnalysisRequest;
import software.amazon.awssdk.services.textract.model.StartExpenseAnalysisResponse;

@Slf4j
@Component
@RequiredArgsConstructor
public class AwsAnalyzeExpenseAdaptor {

    @Value("${mercaso.document.operations.storage.bucket-name}")
    private String bucketName;

    @Value("${spring.application.name}")
    private String applicationName;

    public GetExpenseAnalysisResponse analyzeExpense(String documentKey) {
        try (TextractClient textractClient = TextractClient.builder().region(Region.US_WEST_2).build()) {
            StartExpenseAnalysisRequest request = StartExpenseAnalysisRequest.builder()
                .documentLocation(DocumentLocation.builder()
                    .s3Object(S3Object.builder()
                        .bucket(bucketName)
                        .name(applicationName + SLASH + documentKey)
                        .build())
                    .build())
                .build();

            StartExpenseAnalysisResponse response = textractClient.startExpenseAnalysis(request);
            String jobId = response.jobId();
            log.info("Textract Job ID: {}", jobId);
            return waitForAnalysisCompletion(jobId, textractClient);
        } catch (Exception e) {
            log.error("[AwsAnalyzeExpenseAdaptor] Failed to analyze expense: ", e);
        }

        return null;
    }


    private GetExpenseAnalysisResponse waitForAnalysisCompletion(String jobId, TextractClient textractClient) {
        GetExpenseAnalysisRequest resultRequest = GetExpenseAnalysisRequest.builder().jobId(jobId).build();

        Supplier<GetExpenseAnalysisResponse> analysisSupplier = () -> {
            GetExpenseAnalysisResponse resultResponse = textractClient.getExpenseAnalysis(resultRequest);
            log.info("Job Status: {}", resultResponse.jobStatus());
            return resultResponse;
        };

        try {
            return pollUntilSuccess(analysisSupplier);
        } catch (InterruptedException e) {
            log.error("[AwsAnalyzeExpenseAdaptor] Error during polling: ", e);
            Thread.currentThread().interrupt();
            throw new IllegalArgumentException("Polling error", e);
        }
    }

    private GetExpenseAnalysisResponse pollUntilSuccess(Supplier<GetExpenseAnalysisResponse> supplier) throws InterruptedException {

        int attempts = 0;
        while (attempts < 50) {
            GetExpenseAnalysisResponse response = supplier.get();
            if (response.jobStatus() != JobStatus.IN_PROGRESS) {
                return response;
            }
            attempts++;
            log.info("Waiting {} seconds before next check... (Attempt {}/{})", 6, attempts, 50);
            TimeUnit.SECONDS.sleep(6);
        }
        throw new InterruptedException("Textract analysis timed out after " + 50 * 6 + " seconds");
    }
}
