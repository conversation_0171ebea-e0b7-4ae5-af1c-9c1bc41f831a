package com.mercaso.ims.infrastructure.repository.email;

import com.mercaso.ims.domain.email.Email;
import com.mercaso.ims.domain.email.EmailRepository;
import com.mercaso.ims.domain.email.enums.EmailType;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.email.jpa.EmailJpaDao;
import com.mercaso.ims.infrastructure.repository.email.jpa.dataobject.EmailDo;
import com.mercaso.ims.infrastructure.repository.email.jpa.mapper.EmailDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class EmailRepositoryImpl implements EmailRepository {

    private final EmailDoMapper emailDoMapper;
    private final EmailJpaDao emailJpaDao;

    @Override
    public Email save(Email domain) {
        EmailDo emailDo = emailDoMapper.domainToDo(domain);
        emailDo = emailJpaDao.save(emailDo);
        return emailDoMapper.doToDomain(emailDo);
    }

    @Override
    public Email findById(UUID id) {
        return emailDoMapper.doToDomain(emailJpaDao.findById(id).orElse(null));
    }

    @Override
    public Email update(Email domain) {
        EmailDo emailDo = emailJpaDao.findById(domain.getId()).orElse(null);
        if (null == emailDo) {
            throw new ImsBusinessException(ErrorCodeEnums.EMAIL_NOT_FOUND.getCode());
        }
        EmailDo target = emailDoMapper.domainToDo(domain);
        List<String> ignoreProperties = List.of("createdBy", "createdAt");
        BeanUtils.copyProperties(target, emailDo, ignoreProperties.toArray(new String[0]));
        emailDo = emailJpaDao.save(emailDo);
        return emailDoMapper.doToDomain(emailDo);
    }

    @Override
    public Email deleteById(UUID id) {
        EmailDo emailDo = emailJpaDao.findById(id).orElse(null);
        if (null == emailDo) {
            return null;
        }
        emailDo.setDeletedAt(Instant.now());
        emailDo.setDeletedBy(SecurityUtil.getLoginUserId());
        emailDo.setDeletedUserName(SecurityUtil.getUserName());
        emailDo = emailJpaDao.save(emailDo);
        return emailDoMapper.doToDomain(emailDo);
    }

    @Override
    public List<Email> findByEntityTypeAndEntityId(String entityType, UUID entityId) {
        return Optional.ofNullable(emailJpaDao.findByEntityTypeAndEntityId(entityType, entityId))
                .orElse(List.of())
                .stream()
                .map(emailDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<Email> findByEntityTypeAndEntityIdAndEmailType(String entityType, UUID entityId, EmailType emailType) {
        return Optional.ofNullable(emailJpaDao.findByEntityTypeAndEntityIdAndEmailType(entityType, entityId, emailType))
                .orElse(List.of())
                .stream()
                .map(emailDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<Email> findByEmail(String email) {
        return Optional.ofNullable(emailJpaDao.findByEmail(email))
                .orElse(List.of())
                .stream()
                .map(emailDoMapper::doToDomain)
                .toList();
    }

    @Override
    public List<Email> findByEntityType(String entityType) {
        return Optional.ofNullable(emailJpaDao.findByEntityType(entityType))
                .orElse(List.of())
                .stream()
                .map(emailDoMapper::doToDomain)
                .toList();
    }
}
