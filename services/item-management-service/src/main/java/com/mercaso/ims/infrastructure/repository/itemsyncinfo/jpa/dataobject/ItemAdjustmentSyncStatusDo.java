package com.mercaso.ims.infrastructure.repository.itemsyncinfo.jpa.dataobject;

import com.mercaso.ims.domain.itemsyncinfo.enums.SyncShopifyStatus;
import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.SQLDelete;


@Entity
@Table(name = "item_adjustment_sync_status")
@Data
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_adjustment_sync_status set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemAdjustmentSyncStatusDo extends BaseDo {


    @Column(name = "business_event_id")
    private UUID businessEventId;


    @Column(name = "sync_shopify_status")
    @Enumerated(EnumType.STRING)
    private SyncShopifyStatus syncShopifyStatus;
}
