package com.mercaso.ims.infrastructure.statemachine.config;

import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentStatus;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.enums.ItemAdjustmentTransitionEvents;
import com.mercaso.ims.infrastructure.statemachine.StatemachineFactory;
import com.mercaso.ims.infrastructure.statemachine.constant.StateMachineConstant;
import com.mercaso.ims.infrastructure.statemachine.factory.ImsStateMachineFactory;
import java.util.EnumSet;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;


@Configuration
@EnableStateMachineFactory(contextEvents = false, name = StateMachineConstant.ITEM_ADJUSTMENT_REQUEST_DETAIL_STATEMACHINE_NAME)
public class ItemAdjustmentRequestDetailStateMachineConfig extends
    EnumStateMachineConfigurerAdapter<ItemAdjustmentStatus, ItemAdjustmentTransitionEvents> {


    @Override
    public void configure(StateMachineStateConfigurer<ItemAdjustmentStatus, ItemAdjustmentTransitionEvents> states)
        throws Exception {
        states.withStates()
            .initial(ItemAdjustmentStatus.PENDING)
            .states(EnumSet.allOf(ItemAdjustmentStatus.class));
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<ItemAdjustmentStatus, ItemAdjustmentTransitionEvents> transitions)
        throws Exception {
        transitions
            .withExternal()
            .source(ItemAdjustmentStatus.PENDING)
            .target(ItemAdjustmentStatus.IMS_UPDATED)
            .event(ItemAdjustmentTransitionEvents.IMS_UPDATED)
            .and()
            .withExternal()
            .source(ItemAdjustmentStatus.PENDING)
            .target(ItemAdjustmentStatus.IMS_UPDATED_FAILURE)
            .event(ItemAdjustmentTransitionEvents.IMS_UPDATED_FAILURE)
            .and()
            .withExternal()
            .source(ItemAdjustmentStatus.IMS_UPDATED)
            .target(ItemAdjustmentStatus.SHOPIFY_SYNCHRONIZED)
            .event(ItemAdjustmentTransitionEvents.SHOPIFY_SYNCHRONIZED)
            .and()
            .withExternal()
            .source(ItemAdjustmentStatus.PENDING)
            .target(ItemAdjustmentStatus.SHOPIFY_SYNCHRONIZED)
            .event(ItemAdjustmentTransitionEvents.SHOPIFY_SYNCHRONIZED_DIRECTLY)
            .and()
            .withExternal()
            .source(ItemAdjustmentStatus.SHOPIFY_SYNCHRONIZED_FAILURE)
            .target(ItemAdjustmentStatus.SHOPIFY_SYNCHRONIZED)
            .event(ItemAdjustmentTransitionEvents.SHOPIFY_SYNCHRONIZED_RECOVER);
    }

    @Bean(name = StateMachineConstant.ITEM_ADJUSTMENT_REQUEST_DETAIL_STATEMACHINE_FACTORY_NAME)
    @StatemachineFactory(domainClass = ItemAdjustmentRequestDetail.class)
    public ImsStateMachineFactory<ItemAdjustmentStatus, ItemAdjustmentTransitionEvents, ItemAdjustmentRequestDetail> itemAdjustmentRequestDetailStateMachineAdapter(
        @Qualifier(value = StateMachineConstant.ITEM_ADJUSTMENT_REQUEST_DETAIL_STATEMACHINE_NAME)
        StateMachineFactory<ItemAdjustmentStatus, ItemAdjustmentTransitionEvents> stateMachineFactory,
        @Qualifier(value = StateMachineConstant.ITEM_ADJUSTMENT_REQUEST_DETAIL_STATEMACHINE_PERSISTER_NAME)
        StateMachinePersister<ItemAdjustmentStatus, ItemAdjustmentTransitionEvents, ItemAdjustmentRequestDetail> persister) {
        return new ImsStateMachineFactory<>(stateMachineFactory, persister);
    }

    @Bean(name = StateMachineConstant.ITEM_ADJUSTMENT_REQUEST_DETAIL_STATEMACHINE_PERSISTER_NAME)
    public StateMachinePersister<ItemAdjustmentStatus, ItemAdjustmentTransitionEvents, ItemAdjustmentRequestDetail> itemAdjustmentRequestDetailStateMachinePersister(
        StateMachinePersist<ItemAdjustmentStatus, ItemAdjustmentTransitionEvents, ItemAdjustmentRequestDetail> persist) {
        return new DefaultStateMachinePersister<>(persist);
    }

}
